<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SimpleRegisterTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Manually load API routes for testing
        $this->app['router']->group([
            'middleware' => 'api',
            'prefix' => 'api',
        ], function ($router) {
            require base_path('routes/api.php');
        });
    }

    /** @test */
    public function test_simple_register()
    {
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123'
        ];
        
        // Debug: Check if routes are loaded
        $routes = collect(app('router')->getRoutes())->map(function ($route) {
            return $route->uri();
        })->toArray();
        echo "Available routes: " . implode(', ', $routes) . "\n";
        
        $response = $this->postJson('/api/auth/register', $userData);
        
        echo "Status: " . $response->getStatusCode() . "\n";
        echo "Content: " . $response->getContent() . "\n";
        
        if ($response->getStatusCode() === 500) {
            echo "Headers: " . json_encode($response->headers->all()) . "\n";
            // Try to get more error details
            $content = $response->getContent();
            if (json_decode($content)) {
                echo "JSON Response: " . json_encode(json_decode($content), JSON_PRETTY_PRINT) . "\n";
            }
        }
        
        $this->assertEquals(201, $response->getStatusCode());
    }
}