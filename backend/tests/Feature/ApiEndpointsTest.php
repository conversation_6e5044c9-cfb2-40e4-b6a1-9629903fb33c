<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Category;
use App\Models\Service;
use App\Models\Staff;
use App\Models\Booking;
use App\Models\Feedback;
use Lara<PERSON>\Sanctum\Sanctum;

class ApiEndpointsTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $admin;
    protected $category;
    protected $service;
    protected $staff;
    protected $booking;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Manually load API routes for testing
        $this->app['router']->group([
            'middleware' => 'api',
            'prefix' => 'api',
        ], function ($router) {
            require base_path('routes/api.php');
        });
        
        // Create test users
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
        
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin'
        ]);
        
        // Create test data using factories
        $this->category = Category::factory()->create();
        $this->service = Service::factory()->create(['category_id' => $this->category->id]);
        $this->staff = Staff::factory()->create();
        
        // Связываем service и staff через pivot таблицу
        $this->service->staff()->attach($this->staff->id);
        
        $this->booking = Booking::factory()->create([
            'user_id' => $this->user->id,
            'service_id' => $this->service->id,
            'staff_id' => $this->staff->id,
            'status' => 'completed'
        ]);
    }

    // Authentication Tests
    /** @test */
    public function test_user_can_register()
    {
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123'
        ];
        
        // Debug: Check if routes are loaded
        $routes = collect(app('router')->getRoutes())->map(function ($route) {
            return $route->uri();
        })->toArray();
        echo "Available routes: " . implode(', ', $routes) . "\n";
        
        $response = $this->postJson('/api/auth/register', $userData);
        
        // Debug output
        echo "Status: " . $response->getStatusCode() . "\n";
        echo "Content: " . $response->getContent() . "\n";
        
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'user' => ['id', 'name', 'email'],
                    'token'
                ]);
        
        $this->assertDatabaseHas('users', [
            'name' => 'Test User',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function test_registration_validation()
    {
        // Test missing required fields
        $response = $this->postJson('/api/auth/register', []);
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'email', 'password']);
        
        // Test duplicate email
        $response = $this->postJson('/api/auth/register', [
            'name' => 'Test User',
            'email' => $this->user->email, // Use existing email
            'password' => 'password123',
            'password_confirmation' => 'password123'
        ]);
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
        
        // Test password confirmation mismatch
        $response = $this->postJson('/api/auth/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'different_password'
        ]);
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['password']);
    }

    /** @test */
    public function test_user_can_login()
    {
        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password'
        ];
        
        $response = $this->postJson('/api/auth/login', $loginData);
        

        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'user' => ['id', 'name', 'email'],
                    'token'
                ]);
    }

    /** @test */
    public function test_login_with_invalid_credentials()
    {
        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'wrong_password'
        ];
        
        $response = $this->postJson('/api/auth/login', $loginData);
        
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email' => 'The provided credentials are incorrect.']);
    }

    /** @test */
    public function test_login_validation()
    {
        // Test missing required fields
        $response = $this->postJson('/api/auth/login', []);
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email', 'password']);
        
        // Test invalid email format
        $response = $this->postJson('/api/auth/login', [
            'email' => 'invalid-email',
            'password' => 'password'
        ]);
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    /** @test */
    public function test_authenticated_user_can_logout()
    {
        Sanctum::actingAs($this->user);
        
        $response = $this->postJson('/api/auth/logout');
        
        $response->assertStatus(200)
                ->assertJson(['message' => 'Logged out successfully']);
    }

    /** @test */
    public function test_unauthenticated_user_cannot_logout()
    {
        $response = $this->postJson('/api/auth/logout');
        
        $response->assertStatus(401);
    }

    /** @test */
    public function test_authenticated_user_can_get_profile()
    {
        Sanctum::actingAs($this->user);
        
        $response = $this->getJson('/api/auth/user');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'id',
                    'name',
                    'email',
                    'email_verified_at',
                    'created_at',
                    'updated_at'
                ])
                ->assertJson([
                    'id' => $this->user->id,
                    'email' => $this->user->email
                ]);
    }

    // Categories Tests
    /** @test */
    public function test_can_get_categories()
    {
        // Create additional categories for testing
        Category::factory()->count(3)->create();
        
        $response = $this->getJson('/api/categories');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'description',
                            'image',
                            'is_active',
                            'sort_order',
                            'created_at',
                            'updated_at'
                        ]
                    ]
                ])
                ->assertJsonCount(4, 'data'); // 1 from setUp + 3 created here
    }

    /** @test */
    public function test_can_get_single_category()
    {
        Sanctum::actingAs($this->admin);
        
        $response = $this->getJson("/api/admin/categories/{$this->category->id}");
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'name',
                        'description',
                        'image',
                        'is_active',
                        'sort_order',
                        'services',
                        'created_at',
                        'updated_at'
                    ]
                ])
                ->assertJson([
                    'data' => [
                        'id' => $this->category->id,
                        'name' => $this->category->name
                    ]
                ]);
    }

    /** @test */
    public function test_category_not_found()
    {
        $response = $this->getJson('/api/categories/999');
        
        $response->assertStatus(404);
    }

    public function test_admin_can_create_category()
    {
        Sanctum::actingAs($this->admin);
        
        $categoryData = [
            'name' => 'New Category',
            'description' => 'Test description',
            'image' => 'test-image.jpg',
            'is_active' => true
        ];
        
        $response = $this->postJson('/api/admin/categories', $categoryData);
        
        $response->assertStatus(201)
                ->assertJsonFragment($categoryData);
    }

    public function test_admin_can_update_category()
    {
        Sanctum::actingAs($this->admin);
        
        $updateData = ['name' => 'Updated Category'];
        
        $response = $this->putJson("/api/admin/categories/{$this->category->id}", $updateData);
        
        $response->assertStatus(200)
                ->assertJsonFragment($updateData);
    }

    public function test_admin_can_delete_category()
    {
        Sanctum::actingAs($this->admin);
        
        // Создаем категорию без услуг для удаления
        $emptyCategory = Category::factory()->create();
        
        $response = $this->deleteJson("/api/admin/categories/{$emptyCategory->id}");
        
        $response->assertStatus(200);
        $this->assertDatabaseMissing('categories', ['id' => $emptyCategory->id]);
    }

    // Services Tests
    /** @test */
    public function test_can_get_services_list()
    {
        // Create additional services for testing
        Service::factory()->count(3)->create(['category_id' => $this->category->id]);
        
        $response = $this->getJson('/api/services');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'description',
                            'price',
                            'duration',
                            'image',
                            'is_active',
                            'category'
                        ]
                    ]
                ])
                ->assertJsonCount(4, 'data'); // 1 from setUp + 3 created here
    }

    /** @test */
    public function test_can_get_single_service()
    {
        Sanctum::actingAs($this->admin);
        
        $response = $this->getJson("/api/admin/services/{$this->service->id}");


        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'name',
                        'description',
                        'price',
                        'duration',
                        'image',
                        'is_active',
                        'category',
                        'staff'
                    ]
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'id' => $this->service->id,
                        'name' => $this->service->name
                    ]
                ]);
    }

    /** @test */
    public function test_can_get_services_by_category()
    {
        // Create services for this category
        Service::factory()->count(2)->create(['category_id' => $this->category->id]);
        
        $response = $this->getJson("/api/categories/{$this->category->id}/services");
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'description',
                            'price',
                            'duration',
                            'image',
                            'is_active'
                        ]
                    ]
                ])
                ->assertJsonCount(3, 'data'); // 1 from setUp + 2 created here
    }

    /** @test */
    public function test_service_not_found()
    {
        $response = $this->getJson('/api/services/999');
        
        $response->assertStatus(404);
    }

    public function test_admin_can_create_service()
    {
        Sanctum::actingAs($this->admin);
        
        $serviceData = [
            'category_id' => $this->category->id,
            'name' => 'New Service',
            'description' => 'Test service description',
            'price' => 99.99,
            'duration' => 60,
            'is_active' => true
        ];
        
        $response = $this->postJson('/api/admin/services', $serviceData);
        
        $response->assertStatus(201)
                ->assertJsonFragment([
                    'name' => 'New Service',
                    'price' => '99.99'
                ]);
    }

    // Staff Tests
    /** @test */
    public function test_can_get_staff_list()
    {
        // Create additional staff for testing
        Staff::factory()->count(3)->create();
        
        $response = $this->getJson('/api/staff');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'email',
                            'phone',
                            'bio',
                            'photo',
                            'position',
                            'specializations',
                            'is_active'
                        ]
                    ]
                ])
                ->assertJsonCount(4, 'data'); // 1 from setUp + 3 created here
    }

    /** @test */
    public function test_can_get_single_staff()
    {
        Sanctum::actingAs($this->admin);
        
        $response = $this->getJson("/api/admin/staff/{$this->staff->id}");
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'name',
                        'email',
                        'phone',
                        'bio',
                        'photo',
                        'position',
                        'specializations',
                        'is_active',
                        'services'
                    ]
                ])
                ->assertJson([
                    'data' => [
                        'id' => $this->staff->id,
                        'name' => $this->staff->name
                    ]
                ]);
    }

    /** @test */
    public function test_can_get_staff_by_service()
    {
        // Staff is already attached in setUp
        
        $response = $this->getJson("/api/services/{$this->service->id}/staff");
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'position',
                            'specializations',
                            'photo'
                        ]
                    ]
                ])
                ->assertJsonCount(1, 'data');
    }

    /** @test */
    public function test_staff_not_found()
    {
        $response = $this->getJson('/api/staff/999');
        
        $response->assertStatus(404);
    }

    public function test_admin_can_create_staff()
    {
        Sanctum::actingAs($this->admin);
        
        $staffData = [
            'name' => 'New Staff Member',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'position' => 'Specialist',
            'bio' => 'Test bio',
            'is_active' => true
        ];
        
        $response = $this->postJson('/api/admin/staff', $staffData);
        
        $response->assertStatus(201)
                ->assertJsonFragment(['name' => 'New Staff Member']);
    }

    // Bookings Tests
    /** @test */
    public function test_authenticated_user_can_get_bookings()
    {
        Sanctum::actingAs($this->user);
        
        // Create additional bookings for this user
        Booking::factory()->count(2)->create(['user_id' => $this->user->id]);
        
        $response = $this->getJson('/api/bookings');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'service',
                                'staff',
                                'client_name',
                                'client_email',
                                'client_phone',
                                'booking_date',
                                'start_time',
                                'end_time',
                                'total_price',
                                'status',
                                'notes'
                            ]
                        ],
                        'current_page',
                        'per_page',
                        'total'
                    ]
                ])
                ->assertJsonCount(3, 'data.data'); // 1 from setUp + 2 created here
    }

    /** @test */
    public function test_unauthenticated_user_cannot_get_bookings()
    {
        $response = $this->getJson('/api/bookings');
        
        $response->assertStatus(401);
    }

    /** @test */
    public function test_authenticated_user_can_get_single_booking()
    {
        Sanctum::actingAs($this->user);
        
        $response = $this->getJson("/api/bookings/{$this->booking->id}");
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'service',
                        'staff',
                        'client_name',
                        'client_email',
                        'client_phone',
                        'booking_date',
                        'start_time',
                        'end_time',
                        'total_price',
                        'status',
                        'notes'
                    ]
                ])
                ->assertJson([
                    'data' => [
                        'id' => $this->booking->id
                    ]
                ]);
    }

    /** @test */
    public function test_authenticated_user_can_create_booking()
    {
        Sanctum::actingAs($this->user);
        
        $bookingData = [
            'service_id' => $this->service->id,
            'staff_id' => $this->staff->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '11:00',
            'client_name' => 'John Doe',
            'client_email' => '<EMAIL>',
            'client_phone' => '+1234567890',
            'total_price' => 100.00
        ];
        
        $response = $this->postJson('/api/bookings', $bookingData);
        
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'service',
                        'staff',
                        'client_name',
                        'client_email',
                        'client_phone',
                        'booking_date',
                        'start_time',
                        'end_time',
                        'total_price',
                        'status'
                    ]
                ]);
        
        $this->assertDatabaseHas('bookings', [
            'service_id' => $this->service->id,
            'staff_id' => $this->staff->id,
            'client_name' => 'John Doe',
            'client_email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function test_authenticated_user_can_update_booking()
    {
        Sanctum::actingAs($this->user);
        
        $updateData = [
            'client_name' => 'Jane Doe Updated',
            'notes' => 'Updated notes'
        ];
        
        $response = $this->putJson("/api/bookings/{$this->booking->id}", $updateData);
        
        $response->assertStatus(200);
        
        $this->assertDatabaseHas('bookings', [
            'id' => $this->booking->id,
            'client_name' => 'Jane Doe Updated',
            'notes' => 'Updated notes'
        ]);
    }

    /** @test */
    public function test_authenticated_user_can_cancel_booking()
    {
        // Create a separate booking with pending status for cancellation test
        $pendingBooking = Booking::factory()->create([
            'user_id' => $this->user->id,
            'service_id' => $this->service->id,
            'staff_id' => $this->staff->id,
            'status' => 'pending'
        ]);
        
        Sanctum::actingAs($this->user);
        
        $response = $this->deleteJson("/api/bookings/{$pendingBooking->id}");
        
        $response->assertStatus(200);
        
        $this->assertDatabaseHas('bookings', [
            'id' => $pendingBooking->id,
            'status' => 'cancelled'
        ]);
    }

    /** @test */
    public function test_user_cannot_access_other_user_booking()
    {
        $otherUser = User::factory()->create();
        $otherBooking = Booking::factory()->create([
            'user_id' => $otherUser->id,
            'status' => 'completed'
        ]);
        
        Sanctum::actingAs($this->user);
        
        $response = $this->getJson("/api/bookings/{$otherBooking->id}");
        
        $response->assertStatus(403);
    }

    public function test_can_create_booking()
    {
        $bookingData = [
            'service_id' => $this->service->id,
            'staff_id' => $this->staff->id,
            'client_name' => 'Test Client',
            'client_email' => '<EMAIL>',
            'client_phone' => '+1234567890',
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'notes' => 'Test booking'
        ];
        
        $response = $this->postJson('/api/bookings', $bookingData);

        $response->assertStatus(201)
                ->assertJsonFragment(['client_name' => 'Test Client']);
    }

    public function test_admin_can_get_bookings()
    {
        Sanctum::actingAs($this->admin);
        
        $response = $this->getJson('/api/admin/bookings');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'data' => [
                            '*' => ['id', 'client_name', 'client_email', 'client_phone', 'booking_date', 'start_time', 'end_time', 'status', 'service', 'staff']
                        ],
                        'current_page',
                        'total'
                    ]
                ]);
    }

    public function test_admin_can_update_booking_status()
    {
        Sanctum::actingAs($this->admin);
        
        $response = $this->putJson("/api/admin/bookings/{$this->booking->id}", [
            'status' => 'confirmed'
        ]);
        
        $response->assertStatus(200)
                ->assertJsonFragment(['status' => 'confirmed']);
    }

    // Feedback Tests
    /** @test */
    public function test_can_get_published_feedback_list()
    {
        // Clear existing feedback
        \App\Models\Feedback::truncate();
        
        // Create published and unpublished feedback
        Feedback::factory()->count(3)->published()->create();
        Feedback::factory()->count(2)->unpublished()->create();
        
        $response = $this->actingAs($this->admin, 'sanctum')
                         ->getJson('/api/admin/feedback');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'booking',
                                'rating',
                                'comment',
                                'is_published',
                                'is_featured',
                                'response',
                                'created_at'
                            ]
                        ],
                        'current_page',
                        'total'
                    ]
                ])
                ->assertJsonCount(5, 'data.data'); // All feedback should be returned (3 published + 2 unpublished)
    }

    /** @test */
    public function test_can_get_featured_feedback()
    {
        // Clear existing feedback
        \App\Models\Feedback::truncate();
        
        // Create featured and regular feedback
        Feedback::factory()->count(2)->featured()->create();
        Feedback::factory()->count(3)->published()->create();
        
        $response = $this->actingAs($this->admin, 'sanctum')
                         ->getJson('/api/admin/feedback?featured=1');
        
        $response->assertStatus(200)
                ->assertJsonCount(2, 'data.data');
    }

    /** @test */
    public function test_can_get_public_feedback()
    {
        // Clear existing feedback
        \App\Models\Feedback::truncate();
        
        // Create some published feedback
        Feedback::factory()->count(3)->published()->create();

        $response = $this->getJson('/api/feedback/public');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => ['id', 'rating', 'comment', 'client_name', 'service', 'created_at']
                    ]
                ]);
    }

    /** @test */
    public function test_authenticated_user_can_create_feedback()
    {
        // Clear existing feedback
        \App\Models\Feedback::truncate();
        
        Sanctum::actingAs($this->user);
        
        $feedbackData = [
            'booking_id' => $this->booking->id,
            'rating' => 5,
            'comment' => 'Excellent service! Very professional staff.',
            'client_name' => 'John Doe',
            'client_email' => '<EMAIL>'
        ];
        
        $response = $this->postJson('/api/feedback', $feedbackData);
        
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'booking',
                        'rating',
                        'comment',
                        'is_published'
                    ]
                ]);
        
        $this->assertDatabaseHas('feedback', [
            'booking_id' => $this->booking->id,
            'rating' => 5,
            'comment' => 'Excellent service! Very professional staff.'
        ]);
    }

    /** @test */
    public function test_unauthenticated_user_cannot_create_feedback()
    {
        // Clear existing feedback
        \App\Models\Feedback::truncate();
        
        $feedbackData = [
            'booking_id' => $this->booking->id,
            'rating' => 5,
            'comment' => 'Great service!',
            'client_name' => 'John Doe',
            'client_email' => '<EMAIL>'
        ];
        
        $response = $this->postJson('/api/feedback', $feedbackData);
        
        $response->assertStatus(401);
    }

    /** @test */
    public function test_feedback_validation_rules()
    {
        // Clear existing feedback
        \App\Models\Feedback::truncate();
        
        Sanctum::actingAs($this->user);
        
        // Test missing required fields
        $response = $this->postJson('/api/feedback', []);
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['booking_id', 'rating']);
        
        // Test invalid rating
        $response = $this->postJson('/api/feedback', [
            'booking_id' => $this->booking->id,
            'rating' => 6, // Invalid rating (should be 1-5)
            'comment' => 'Test comment',
            'client_name' => 'John Doe',
            'client_email' => '<EMAIL>'
        ]);
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['rating']);
    }

    /** @test */
    public function test_user_cannot_create_feedback_for_other_user_booking()
    {
        // Clear existing feedback
        \App\Models\Feedback::truncate();
        
        $otherUser = User::factory()->create();
        $otherBooking = Booking::factory()->create([
            'user_id' => $otherUser->id,
            'status' => 'completed'
        ]);
        
        Sanctum::actingAs($this->user);
        
        $feedbackData = [
            'booking_id' => $otherBooking->id,
            'rating' => 5,
            'comment' => 'Great service!',
            'client_name' => 'John Doe',
            'client_email' => '<EMAIL>'
        ];
        
        $response = $this->postJson('/api/feedback', $feedbackData);
        
        $response->assertStatus(403);
    }

    public function test_client_can_create_feedback()
    {
        // Clear existing feedback
        \App\Models\Feedback::truncate();
        
        Sanctum::actingAs($this->user);
        
        $feedbackData = [
            'booking_id' => $this->booking->id,
            'rating' => 5,
            'comment' => 'Excellent service!',
            'client_name' => 'John Doe',
            'client_email' => '<EMAIL>'
        ];
        
        $response = $this->postJson('/api/feedback', $feedbackData);
        
        $response->assertStatus(201)
                ->assertJsonFragment(['rating' => 5]);
    }

    public function test_admin_can_get_feedback_stats()
    {
        // Clear existing feedback
        \App\Models\Feedback::truncate();
        
        Sanctum::actingAs($this->admin);
        
        $response = $this->getJson('/api/admin/feedback/stats');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'total',
                    'published',
                    'featured',
                    'average_rating',
                    'rating_distribution',
                    'recent_count'
                ]);
    }

    // Analytics Tests
    public function test_admin_can_get_dashboard_analytics()
    {
        Sanctum::actingAs($this->admin);
        
        $response = $this->getJson('/api/admin/analytics/dashboard');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'total_bookings',
                    'total_revenue',
                    'total_clients',
                    'average_rating',
                    'bookings_by_status',
                    'revenue_by_month',
                    'popular_services',
                    'recent_bookings'
                ]);
    }

    public function test_admin_can_get_revenue_details()
    {
        Sanctum::actingAs($this->admin);
        
        $response = $this->getJson('/api/admin/analytics/revenue');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'total_revenue',
                    'revenue_by_period',
                    'revenue_by_service',
                    'revenue_by_staff'
                ]);
    }

    // Contact Tests
    public function test_can_send_contact_message()
    {
        $contactData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'subject' => 'Test Subject',
            'message' => 'Test message content'
        ];
        
        $response = $this->postJson('/api/contacts', $contactData);
        
        $response->assertStatus(201)
                ->assertJson(['message' => 'Contact message sent successfully']);
    }

    // SPA Config Tests
    public function test_can_get_spa_config()
    {
        $response = $this->getJson('/api/spa-config');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'app_name',
                    'contact_info',
                    'business_hours',
                    'social_links'
                ]);
    }

    // Validation Tests
    public function test_booking_validation()
    {
        $response = $this->postJson('/api/bookings', [
            'service_id' => 999,
            'client_name' => '',
            'client_email' => 'invalid-email'
        ]);
        
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['service_id', 'client_name', 'client_email']);
    }

    public function test_unauthorized_access_to_admin_routes()
    {
        $response = $this->getJson('/api/admin/categories');
        
        $response->assertStatus(401);
    }

    public function test_client_cannot_access_admin_routes()
    {
        Sanctum::actingAs($this->user);
        
        $response = $this->getJson('/api/admin/categories');
        
        $response->assertStatus(403);
    }

    // Error Handling Tests
    public function test_404_for_nonexistent_resource()
    {
        Sanctum::actingAs($this->admin);
        
        $response = $this->getJson('/api/admin/categories/999');
        
        $response->assertStatus(404);
    }

    public function test_method_not_allowed()
    {
        $response = $this->patchJson('/api/categories');
        
        $response->assertStatus(405);
    }
}