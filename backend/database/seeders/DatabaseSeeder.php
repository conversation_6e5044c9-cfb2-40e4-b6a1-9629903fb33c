<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Category;
use App\Models\Service;
use App\Models\Staff;
use App\Models\Booking;
use App\Models\Feedback;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Create test client
        $client = User::create([
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'client',
            'phone' => '+**********',
            'email_verified_at' => now(),
        ]);

        // Create categories
        $beautyCategory = Category::create([
            'name' => 'Beauty & Wellness',
            'description' => 'Professional beauty and wellness services',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $healthCategory = Category::create([
            'name' => 'Health & Fitness',
            'description' => 'Health and fitness services',
            'is_active' => true,
            'sort_order' => 2,
        ]);

        $homeCategory = Category::create([
            'name' => 'Home Services',
            'description' => 'Professional home maintenance services',
            'is_active' => true,
            'sort_order' => 3,
        ]);

        // Create services
        $haircut = Service::create([
            'category_id' => $beautyCategory->id,
            'name' => 'Haircut & Styling',
            'description' => 'Professional haircut and styling service',
            'duration' => 60,
            'price' => 50.00,
            'is_active' => true,
        ]);

        $massage = Service::create([
            'category_id' => $healthCategory->id,
            'name' => 'Relaxing Massage',
            'description' => 'Full body relaxing massage therapy',
            'duration' => 90,
            'price' => 80.00,
            'is_active' => true,
        ]);

        $cleaning = Service::create([
            'category_id' => $homeCategory->id,
            'name' => 'House Cleaning',
            'description' => 'Professional house cleaning service',
            'duration' => 120,
            'price' => 100.00,
            'is_active' => true,
        ]);

        $manicure = Service::create([
            'category_id' => $beautyCategory->id,
            'name' => 'Manicure',
            'description' => 'Professional nail care and manicure',
            'duration' => 45,
            'price' => 35.00,
            'is_active' => true,
        ]);

        // Create staff
        $stylist = Staff::create([
            'name' => 'Sarah Johnson',
            'email' => '<EMAIL>',
            'phone' => '+**********',
            'position' => 'Hair Stylist',
            'bio' => 'Professional hair stylist with 8 years of experience',
            'is_active' => true,
        ]);
        $stylist->services()->attach([$haircut->id, $manicure->id]);

        $therapist = Staff::create([
            'name' => 'Mike Wilson',
            'email' => '<EMAIL>',
            'phone' => '+**********',
            'position' => 'Massage Therapist',
            'bio' => 'Licensed massage therapist specializing in relaxation techniques',
            'is_active' => true,
        ]);
        $therapist->services()->attach([$massage->id]);

        $cleaner = Staff::create([
            'name' => 'Anna Davis',
            'email' => '<EMAIL>',
            'phone' => '+**********',
            'position' => 'Cleaning Specialist',
            'bio' => 'Professional cleaner with attention to detail',
            'is_active' => true,
        ]);
        $cleaner->services()->attach([$cleaning->id]);

        // Create sample bookings
        $booking1 = Booking::create([
            'user_id' => $client->id,
            'service_id' => $haircut->id,
            'staff_id' => $stylist->id,
            'booking_date' => now()->addDays(1),
            'start_time' => '10:00:00',
            'end_time' => '11:00:00',
            'total_price' => 50.00,
            'status' => 'confirmed',
            'client_name' => 'Test Client',
            'client_email' => '<EMAIL>',
            'client_phone' => '+**********',
            'notes' => 'First time client',
        ]);

        $booking2 = Booking::create([
            'user_id' => $client->id,
            'service_id' => $massage->id,
            'staff_id' => $therapist->id,
            'booking_date' => now()->subDays(1),
            'start_time' => '14:00:00',
            'end_time' => '15:30:00',
            'total_price' => 80.00,
            'status' => 'completed',
            'client_name' => 'Test Client',
            'client_email' => '<EMAIL>',
            'client_phone' => '+**********',
            'notes' => 'Regular client',
        ]);

        // Create sample feedback
        Feedback::create([
            'booking_id' => $booking2->id,
            'user_id' => $client->id,
            'client_name' => 'Test Client',
            'client_email' => '<EMAIL>',
            'rating' => 5,
            'comment' => 'Excellent service! Mike was very professional and the massage was exactly what I needed.',
            'is_published' => true,
            'is_featured' => true,
        ]);

        $this->command->info('Database seeded successfully!');
    }
}
