<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Booking;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Feedback>
 */
class FeedbackFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'booking_id' => Booking::factory(),
            'client_name' => $this->faker->name(),
            'client_email' => $this->faker->safeEmail(),
            'rating' => $this->faker->numberBetween(1, 5),
            'comment' => $this->faker->paragraph(),
            'is_published' => $this->faker->boolean(80), // 80% chance of being published
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
            'response' => $this->faker->optional(0.3)->sentence(), // 30% chance of admin response
            'responded_at' => function (array $attributes) {
                return $attributes['response'] ? $this->faker->dateTimeBetween('-1 week', 'now') : null;
            },
        ];
    }

    /**
     * Indicate that the feedback is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => true,
        ]);
    }

    /**
     * Indicate that the feedback is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
            'is_published' => true, // Featured feedback should be published
        ]);
    }

    /**
     * Indicate that the feedback has high rating.
     */
    public function highRating(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->numberBetween(4, 5),
        ]);
    }

    /**
     * Indicate that the feedback has low rating.
     */
    public function lowRating(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->numberBetween(1, 2),
        ]);
    }

    /**
     * Indicate that the feedback has admin response.
     */
    public function withResponse(): static
    {
        return $this->state(fn (array $attributes) => [
            'response' => $this->faker->paragraph(),
            'responded_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Indicate that the feedback is unpublished.
     */
    public function unpublished(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => false,
            'is_featured' => false,
        ]);
    }
}