<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Staff>
 */
class StaffFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'bio' => $this->faker->paragraph(),
            'photo' => $this->faker->imageUrl(200, 200, 'people'),
            'position' => $this->faker->randomElement([
                'Senior Specialist',
                'Massage Therapist',
                'Beauty Specialist',
                'Wellness Coach',
                'Fitness Trainer'
            ]),
            'specializations' => $this->faker->randomElements([
                'Deep Tissue Massage',
                'Swedish Massage',
                'Facial Treatment',
                'Hair Styling',
                'Manicure',
                'Pedicure',
                'Yoga',
                'Personal Training'
            ], $this->faker->numberBetween(1, 3)),
            'is_active' => true,
        ];
    }

    /**
     * Indicate that the staff member is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the staff member is a senior specialist.
     */
    public function senior(): static
    {
        return $this->state(fn (array $attributes) => [
            'position' => 'Senior Specialist',
            'specializations' => [
                'Advanced Massage Therapy',
                'Specialized Treatments',
                'Consultation'
            ],
        ]);
    }
}