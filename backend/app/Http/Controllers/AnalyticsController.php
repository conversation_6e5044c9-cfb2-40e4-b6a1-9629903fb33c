<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\Service;
use App\Models\Category;
use App\Models\Staff;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AnalyticsController extends Controller
{
    /**
     * Get dashboard analytics
     */
    public function dashboard(Request $request)
    {
        try {
            $period = $request->get('period', '30'); // days
            $startDate = Carbon::now()->subDays($period);
            
            // Total bookings
            $totalBookings = Booking::where('created_at', '>=', $startDate)->count();
            
            // Total revenue
            $totalRevenue = Booking::where('created_at', '>=', $startDate)
                ->where('status', 'completed')
                ->sum('total_price');
            
            // Booking status distribution
            $bookingsByStatus = Booking::where('created_at', '>=', $startDate)
                ->select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->get();
            
            // Daily bookings trend
            $dailyBookings = Booking::where('created_at', '>=', $startDate)
                ->select(DB::raw('DATE(created_at) as date'), DB::raw('count(*) as count'))
                ->groupBy('date')
                ->orderBy('date')
                ->get();
            
            // Popular services
            $popularServices = Service::withCount(['bookings' => function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }])
                ->orderBy('bookings_count', 'desc')
                ->take(5)
                ->get();
            
            // Staff performance
            $staffPerformance = Staff::withCount(['bookings' => function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }])
                ->with(['bookings' => function ($query) use ($startDate) {
                    $query->where('created_at', '>=', $startDate)
                        ->where('status', 'completed')
                        ->select('staff_id', DB::raw('SUM(total_price) as revenue'));
                }])
                ->orderBy('bookings_count', 'desc')
                ->get();
            
            // Calculate additional metrics for dashboard
            $totalClients = Booking::where('created_at', '>=', $startDate)
                ->distinct('user_id')
                ->count('user_id');
            
            $averageRating = 4.5; // Mock rating for now
            
            // Revenue by month (mock data)
            $revenueByMonth = [];
            for ($i = 11; $i >= 0; $i--) {
                $month = Carbon::now()->subMonths($i);
                $revenueByMonth[] = [
                    'month' => $month->format('Y-m'),
                    'revenue' => rand(1000, 5000)
                ];
            }
            
            // Recent bookings
            $recentBookings = Booking::with(['service', 'user'])
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get();
            
            return response()->json([
                'success' => true,
                'total_bookings' => $totalBookings,
                'total_revenue' => $totalRevenue,
                'total_clients' => $totalClients,
                'average_rating' => $averageRating,
                'bookings_by_status' => $bookingsByStatus,
                'revenue_by_month' => $revenueByMonth,
                'popular_services' => $popularServices,
                'recent_bookings' => $recentBookings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch analytics data',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get revenue analytics
     */
    public function revenue(Request $request)
    {
        try {
            $period = $request->get('period', '30');
            $startDate = Carbon::now()->subDays($period);
            
            // Daily revenue
            $dailyRevenue = Booking::where('created_at', '>=', $startDate)
                ->where('status', 'completed')
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('SUM(total_price) as revenue'),
                    DB::raw('COUNT(*) as bookings')
                )
                ->groupBy('date')
                ->orderBy('date')
                ->get();
            
            // Revenue by service
            $revenueByService = Service::join('bookings', 'services.id', '=', 'bookings.service_id')
                ->where('bookings.created_at', '>=', $startDate)
                ->where('bookings.status', 'completed')
                ->select(
                    'services.name',
                    DB::raw('SUM(bookings.total_price) as revenue'),
                    DB::raw('COUNT(bookings.id) as bookings')
                )
                ->groupBy('services.id', 'services.name')
                ->orderBy('revenue', 'desc')
                ->get();
            
            // Revenue by category
            $revenueByCategory = Category::join('services', 'categories.id', '=', 'services.category_id')
                ->join('bookings', 'services.id', '=', 'bookings.service_id')
                ->where('bookings.created_at', '>=', $startDate)
                ->where('bookings.status', 'completed')
                ->select(
                    'categories.name',
                    DB::raw('SUM(bookings.total_price) as revenue'),
                    DB::raw('COUNT(bookings.id) as bookings')
                )
                ->groupBy('categories.id', 'categories.name')
                ->orderBy('revenue', 'desc')
                ->get();
            
            // Calculate total revenue
            $totalRevenue = Booking::where('created_at', '>=', $startDate)
                ->where('status', 'completed')
                ->sum('total_price');
            
            // Revenue by staff
            $revenueByStaff = Staff::join('bookings', 'staff.id', '=', 'bookings.staff_id')
                ->where('bookings.created_at', '>=', $startDate)
                ->where('bookings.status', 'completed')
                ->select(
                    'staff.name',
                    DB::raw('SUM(bookings.total_price) as revenue'),
                    DB::raw('COUNT(bookings.id) as bookings')
                )
                ->groupBy('staff.id', 'staff.name')
                ->orderBy('revenue', 'desc')
                ->get();
            
            return response()->json([
                'success' => true,
                'total_revenue' => $totalRevenue,
                'revenue_by_period' => $dailyRevenue,
                'revenue_by_service' => $revenueByService,
                'revenue_by_staff' => $revenueByStaff
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch revenue analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get booking analytics
     */
    public function bookings(Request $request)
    {
        try {
            $period = $request->get('period', '30');
            $startDate = Carbon::now()->subDays($period);
            
            // Hourly booking distribution
            $hourlyBookings = Booking::where('created_at', '>=', $startDate)
                ->select(
                    DB::raw('HOUR(start_time) as hour'),
                    DB::raw('COUNT(*) as count')
                )
                ->groupBy('hour')
                ->orderBy('hour')
                ->get();
            
            // Weekly booking distribution
            $weeklyBookings = Booking::where('created_at', '>=', $startDate)
                ->select(
                    DB::raw('DAYOFWEEK(date) as day_of_week'),
                    DB::raw('COUNT(*) as count')
                )
                ->groupBy('day_of_week')
                ->orderBy('day_of_week')
                ->get();
            
            // Booking duration analysis
            $durationAnalysis = Booking::join('services', 'bookings.service_id', '=', 'services.id')
                ->where('bookings.created_at', '>=', $startDate)
                ->select(
                    'services.duration',
                    DB::raw('COUNT(*) as count'),
                    DB::raw('AVG(bookings.total_price) as avg_price')
                )
                ->groupBy('services.duration')
                ->orderBy('services.duration')
                ->get();
            
            // Cancellation analysis
            $cancellationRate = Booking::where('created_at', '>=', $startDate)
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->get();
            
            return response()->json([
                'success' => true,
                'data' => [
                    'hourly_distribution' => $hourlyBookings,
                    'weekly_distribution' => $weeklyBookings,
                    'duration_analysis' => $durationAnalysis,
                    'cancellation_analysis' => $cancellationRate
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch booking analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
