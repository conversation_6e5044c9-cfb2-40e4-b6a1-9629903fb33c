<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    /**
     * Display a listing of contacts
     */
    public function index(): JsonResponse
    {
        // For admin - return list of contact messages
        return response()->json([
            'success' => true,
            'data' => []
        ]);
    }

    /**
     * Store a new contact message
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'nullable|string|max:20',
                'subject' => 'required|string|max:255',
                'message' => 'required|string|max:1000'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ошибка валидации',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Here you would typically save to database and/or send email
            // For now, just return success response
            
            return response()->json([
                'success' => true,
                'message' => 'Contact message sent successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при отправке сообщения'
            ], 500);
        }
    }

    /**
     * Update contact message
     */
    public function update(Request $request, $id): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => 'Сообщение обновлено'
        ]);
    }

    /**
     * Delete contact message
     */
    public function destroy($id): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => 'Сообщение удалено'
        ]);
    }
}
