<?php

namespace App\Http\Controllers;

use App\Models\Staff;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class StaffController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Staff::query();

            // Filter by active status
            if ($request->has('active')) {
                $query->where('is_active', $request->boolean('active'));
            }

            // Search by name, email, or position
            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('position', 'like', "%{$search}%");
                });
            }

            // Filter by specialization
            if ($request->filled('specialization')) {
                $specialization = $request->get('specialization');
                $query->whereJsonContains('specializations', $specialization);
            }

            $staff = $query->orderBy('name')->get();

            return response()->json([
                'success' => true,
                'data' => $staff
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при получении списка персонала',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:staff,email',
                'phone' => 'nullable|string|max:20',
                'bio' => 'nullable|string',
                'photo' => 'nullable|url',
                'position' => 'required|string|max:255',
                'specializations' => 'nullable|array',
                'specializations.*' => 'string',
                'is_active' => 'boolean'
            ]);

            $staff = Staff::create($validated);

            return response()->json([
                'success' => true,
                'message' => 'Сотрудник успешно создан',
                'data' => $staff
            ], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка валидации',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при создании сотрудника',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $staff = Staff::with([
                'bookings' => function ($query) {
                    $query->orderBy('date', 'desc')->limit(10);
                },
                'services'
            ])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $staff
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Сотрудник не найден'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при получении данных сотрудника',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $staff = Staff::findOrFail($id);

            $validated = $request->validate([
                'name' => 'sometimes|required|string|max:255',
                'email' => [
                    'sometimes',
                    'required',
                    'email',
                    Rule::unique('staff', 'email')->ignore($staff->id)
                ],
                'phone' => 'nullable|string|max:20',
                'bio' => 'nullable|string',
                'photo' => 'nullable|url',
                'position' => 'sometimes|required|string|max:255',
                'specializations' => 'nullable|array',
                'specializations.*' => 'string',
                'is_active' => 'boolean'
            ]);

            $staff->update($validated);

            return response()->json([
                'success' => true,
                'message' => 'Данные сотрудника успешно обновлены',
                'data' => $staff->fresh()
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Сотрудник не найден'
            ], 404);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка валидации',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при обновлении данных сотрудника',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $staff = Staff::findOrFail($id);

            // Check if staff has any bookings
            if ($staff->bookings()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Невозможно удалить сотрудника с существующими записями'
                ], 400);
            }

            $staff->delete();

            return response()->json([
                'success' => true,
                'message' => 'Сотрудник успешно удален'
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Сотрудник не найден'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при удалении сотрудника',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get staff members who can perform a specific service
     */
    public function getByService(string $serviceId): JsonResponse
    {
        try {
            $staff = Staff::active()
                ->whereJsonContains('specializations', $serviceId)
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $staff
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при получении списка персонала для услуги',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
