<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Service;
use App\Models\Staff;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class BookingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Booking::with(['service', 'staff', 'user']);

            // Filter by status
            if ($request->filled('status')) {
                $query->where('status', $request->get('status'));
            }

            // Filter by date range
            if ($request->filled('date_from')) {
                $query->whereDate('booking_date', '>=', $request->get('date_from'));
            }
            if ($request->filled('date_to')) {
                $query->whereDate('booking_date', '<=', $request->get('date_to'));
            }

            // Filter by service
            if ($request->filled('service_id')) {
                $query->where('service_id', $request->get('service_id'));
            }

            // Filter by staff
            if ($request->filled('staff_id')) {
                $query->where('staff_id', $request->get('staff_id'));
            }

            // Filter by user (for authenticated users)
            if ($request->filled('user_id') && Auth::check()) {
                $query->where('user_id', $request->get('user_id'));
            }

            // Search by client name, email, or phone
            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('client_name', 'like', "%{$search}%")
                      ->orWhere('client_email', 'like', "%{$search}%")
                      ->orWhere('client_phone', 'like', "%{$search}%");
                });
            }

            $bookings = $query->orderBy('booking_date', 'desc')
                             ->orderBy('start_time', 'desc')
                             ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $bookings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при получении списка бронирований',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'service_id' => 'required|exists:services,id',
                'staff_id' => 'required|exists:staff,id',
                'client_name' => 'required|string|max:255',
                'client_email' => 'required|email|max:255',
                'client_phone' => 'required|string|max:20',
                'booking_date' => 'required|date|after_or_equal:today',
                'start_time' => 'required|date_format:H:i',
                'notes' => 'nullable|string'
            ]);

            // Get service to calculate end time and price
            $service = Service::findOrFail($validated['service_id']);
            
            // Calculate end time
            $startTime = Carbon::createFromFormat('H:i', $validated['start_time']);
            $endTime = $startTime->copy()->addMinutes($service->duration);
            
            // Check if staff is available at this time
            $conflictingBooking = Booking::where('staff_id', $validated['staff_id'])
                ->where('booking_date', $validated['booking_date'])
                ->where('status', '!=', 'cancelled')
                ->where(function ($query) use ($validated, $endTime) {
                    $query->whereBetween('start_time', [$validated['start_time'], $endTime->format('H:i')])
                          ->orWhereBetween('end_time', [$validated['start_time'], $endTime->format('H:i')])
                          ->orWhere(function ($q) use ($validated, $endTime) {
                              $q->where('start_time', '<=', $validated['start_time'])
                                ->where('end_time', '>=', $endTime->format('H:i'));
                          });
                })
                ->exists();

            if ($conflictingBooking) {
                return response()->json([
                    'success' => false,
                    'message' => 'Выбранное время уже занято'
                ], 400);
            }

            $validated['end_time'] = $endTime->format('H:i');
            $validated['total_price'] = $service->price;
            $validated['status'] = 'pending';
            
            // Add user_id if authenticated
            if (Auth::check()) {
                $validated['user_id'] = Auth::id();
            }

            $booking = Booking::create($validated);
            $booking->load(['service', 'staff', 'user']);

            return response()->json([
                'success' => true,
                'message' => 'Бронирование успешно создано',
                'data' => $booking
            ], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка валидации',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при создании бронирования',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $booking = Booking::with(['service', 'staff', 'user'])->findOrFail($id);
            
            // Check if user can access this booking
            if (Auth::check() && $booking->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Доступ запрещен'
                ], 403);
            }

            return response()->json([
                'success' => true,
                'data' => $booking
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Бронирование не найдено'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при получении данных бронирования',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $booking = Booking::findOrFail($id);

            $validated = $request->validate([
                'service_id' => 'sometimes|required|exists:services,id',
                'staff_id' => 'sometimes|required|exists:staff,id',
                'client_name' => 'sometimes|required|string|max:255',
                'client_email' => 'sometimes|required|email|max:255',
                'client_phone' => 'sometimes|required|string|max:20',
                'booking_date' => 'sometimes|required|date',
                'start_time' => 'sometimes|required|date_format:H:i',
                'status' => 'sometimes|required|in:pending,confirmed,completed,cancelled',
                'notes' => 'nullable|string',
                'cancellation_reason' => 'nullable|string'
            ]);

            // If time or service is being changed, recalculate end time and check availability
            if (isset($validated['service_id']) || isset($validated['start_time'])) {
                $serviceId = $validated['service_id'] ?? $booking->service_id;
                $service = Service::findOrFail($serviceId);
                
                $startTime = isset($validated['start_time']) 
                    ? Carbon::createFromFormat('H:i', $validated['start_time'])
                    : Carbon::createFromFormat('H:i', $booking->start_time);
                    
                $endTime = $startTime->copy()->addMinutes($service->duration);
                $validated['end_time'] = $endTime->format('H:i');
                $validated['total_price'] = $service->price;

                // Check availability if date, time, or staff is changing
                $staffId = $validated['staff_id'] ?? $booking->staff_id;
                $date = $validated['booking_date'] ?? $booking->booking_date;
                
                $conflictingBooking = Booking::where('staff_id', $staffId)
                    ->where('booking_date', $date)
                    ->where('id', '!=', $booking->id)
                    ->where('status', '!=', 'cancelled')
                    ->where(function ($query) use ($startTime, $endTime) {
                        $query->whereBetween('start_time', [$startTime->format('H:i'), $endTime->format('H:i')])
                              ->orWhereBetween('end_time', [$startTime->format('H:i'), $endTime->format('H:i')])
                              ->orWhere(function ($q) use ($startTime, $endTime) {
                                  $q->where('start_time', '<=', $startTime->format('H:i'))
                                    ->where('end_time', '>=', $endTime->format('H:i'));
                              });
                    })
                    ->exists();

                if ($conflictingBooking) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Выбранное время уже занято'
                    ], 400);
                }
            }

            // Handle status changes
            if (isset($validated['status'])) {
                if ($validated['status'] === 'cancelled') {
                    $validated['cancelled_at'] = now();
                }
            }

            $booking->update($validated);
            $booking->load(['service', 'staff', 'user']);

            return response()->json([
                'success' => true,
                'message' => 'Бронирование успешно обновлено',
                'data' => $booking
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Бронирование не найдено'
            ], 404);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка валидации',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при обновлении бронирования',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $booking = Booking::findOrFail($id);

            // Check if user owns this booking
            if ($booking->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Доступ запрещен'
                ], 403);
            }

            // Only allow cancellation of pending bookings
            if ($booking->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Можно отменить только ожидающие бронирования'
                ], 400);
            }

            // Cancel the booking instead of deleting
            $booking->update([
                'status' => 'cancelled',
                'cancelled_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Бронирование успешно отменено'
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Бронирование не найдено'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при отмене бронирования',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel a booking
     */
    public function cancel(Request $request, string $id): JsonResponse
    {
        try {
            $booking = Booking::findOrFail($id);

            if (!$booking->canBeCancelled()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Это бронирование нельзя отменить'
                ], 400);
            }

            $validated = $request->validate([
                'cancellation_reason' => 'nullable|string|max:500'
            ]);

            $booking->cancel($validated['cancellation_reason'] ?? null);

            return response()->json([
                'success' => true,
                'message' => 'Бронирование успешно отменено',
                'data' => $booking->fresh(['service', 'staff', 'user'])
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Бронирование не найдено'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при отмене бронирования',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Confirm a booking
     */
    public function confirm(string $id): JsonResponse
    {
        try {
            $booking = Booking::findOrFail($id);

            if ($booking->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Можно подтвердить только ожидающие бронирования'
                ], 400);
            }

            $booking->confirm();

            return response()->json([
                'success' => true,
                'message' => 'Бронирование успешно подтверждено',
                'data' => $booking->fresh(['service', 'staff', 'user'])
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Бронирование не найдено'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при подтверждении бронирования',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Complete a booking
     */
    public function complete(string $id): JsonResponse
    {
        try {
            $booking = Booking::findOrFail($id);

            if ($booking->status !== 'confirmed') {
                return response()->json([
                    'success' => false,
                    'message' => 'Можно завершить только подтвержденные бронирования'
                ], 400);
            }

            $booking->complete();

            return response()->json([
                'success' => true,
                'message' => 'Бронирование успешно завершено',
                'data' => $booking->fresh(['service', 'staff', 'user'])
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Бронирование не найдено'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при завершении бронирования',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available time slots for a service and staff member
     */
    public function getAvailableSlots(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'service_id' => 'required|exists:services,id',
                'staff_id' => 'required|exists:staff,id',
                'date' => 'required|date|after_or_equal:today'
            ]);

            $service = Service::findOrFail($validated['service_id']);
            $date = $validated['date'];
            $staffId = $validated['staff_id'];

            // Get existing bookings for the date and staff
            $existingBookings = Booking::where('staff_id', $staffId)
                ->where('date', $date)
                ->where('status', '!=', 'cancelled')
                ->orderBy('start_time')
                ->get(['start_time', 'end_time']);

            // Generate available slots (9 AM to 6 PM, 30-minute intervals)
            $slots = [];
            $startHour = 9;
            $endHour = 18;
            $interval = 30; // minutes

            for ($hour = $startHour; $hour < $endHour; $hour++) {
                for ($minute = 0; $minute < 60; $minute += $interval) {
                    $slotStart = sprintf('%02d:%02d', $hour, $minute);
                    $slotStartTime = Carbon::createFromFormat('H:i', $slotStart);
                    $slotEndTime = $slotStartTime->copy()->addMinutes($service->duration);
                    
                    // Check if slot would end after business hours
                    if ($slotEndTime->hour >= $endHour) {
                        break;
                    }

                    $slotEnd = $slotEndTime->format('H:i');

                    // Check if slot conflicts with existing bookings
                    $isAvailable = true;
                    foreach ($existingBookings as $booking) {
                        if (($slotStart >= $booking->start_time && $slotStart < $booking->end_time) ||
                            ($slotEnd > $booking->start_time && $slotEnd <= $booking->end_time) ||
                            ($slotStart <= $booking->start_time && $slotEnd >= $booking->end_time)) {
                            $isAvailable = false;
                            break;
                        }
                    }

                    if ($isAvailable) {
                        $slots[] = [
                            'start_time' => $slotStart,
                            'end_time' => $slotEnd,
                            'duration' => $service->duration
                        ];
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => $slots
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка валидации',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при получении доступных слотов',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
