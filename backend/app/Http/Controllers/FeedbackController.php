<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Feedback;
use App\Models\Booking;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class FeedbackController extends Controller
{
    /**
     * Display a listing of feedback
     */
    public function index(Request $request)
    {
        try {
            $query = Feedback::with(['booking.service', 'booking.staff', 'user']);
            
            // Filter by published status
            if ($request->has('published')) {
                $query->where('is_published', $request->boolean('published'));
            }
            
            // Filter by featured status
            if ($request->has('featured')) {
                $query->where('is_featured', $request->boolean('featured'));
            }
            
            // Filter by rating
            if ($request->has('rating')) {
                $query->where('rating', $request->get('rating'));
            }
            
            // Filter by minimum rating
            if ($request->has('min_rating')) {
                $query->where('rating', '>=', $request->get('min_rating'));
            }
            
            // Search in comments
            if ($request->has('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('comment', 'like', "%{$search}%")
                      ->orWhere('client_name', 'like', "%{$search}%")
                      ->orWhere('response', 'like', "%{$search}%");
                });
            }
            
            // Sort
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);
            
            $perPage = $request->get('per_page', 15);
            $feedback = $query->paginate($perPage);
            
            return response()->json([
                'success' => true,
                'data' => $feedback
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch feedback',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Store a newly created feedback
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'booking_id' => 'required|exists:bookings,id',
                'rating' => 'required|integer|min:1|max:5',
                'comment' => 'required|string|max:1000',
                'client_name' => 'required|string|max:255',
                'client_email' => 'required|email|max:255'
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            
            // Check if booking exists and is completed
            $booking = Booking::find($request->booking_id);
            if (!$booking || $booking->status !== 'completed') {
                return response()->json([
                    'success' => false,
                    'message' => 'Feedback can only be left for completed bookings'
                ], 400);
            }
            
            // Check if user owns this booking
            if (auth()->check() && $booking->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You can only leave feedback for your own bookings'
                ], 403);
            }
            
            // Check if feedback already exists for this booking
            if ($booking->feedback) {
                return response()->json([
                    'success' => false,
                    'message' => 'Feedback already exists for this booking'
                ], 400);
            }
            
            $feedback = Feedback::create([
                'booking_id' => $request->booking_id,
                'user_id' => $booking->user_id,
                'client_name' => $request->client_name,
                'client_email' => $request->client_email,
                'rating' => $request->rating,
                'comment' => $request->comment,
                'is_published' => true
            ]);
            
            $feedback->load(['booking.service', 'booking.staff']);
            
            return response()->json([
                'success' => true,
                'message' => 'Feedback created successfully',
                'data' => $feedback
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create feedback',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Display the specified feedback
     */
    public function show($id)
    {
        try {
            $feedback = Feedback::with(['booking.service', 'booking.staff', 'user'])->find($id);
            
            if (!$feedback) {
                return response()->json([
                    'success' => false,
                    'message' => 'Feedback not found'
                ], 404);
            }
            
            return response()->json([
                'success' => true,
                'data' => $feedback
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch feedback',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Update the specified feedback
     */
    public function update(Request $request, $id)
    {
        try {
            $feedback = Feedback::find($id);
            
            if (!$feedback) {
                return response()->json([
                    'success' => false,
                    'message' => 'Feedback not found'
                ], 404);
            }
            
            $validator = Validator::make($request->all(), [
                'rating' => 'sometimes|integer|min:1|max:5',
                'comment' => 'sometimes|string|max:1000',
                'response' => 'sometimes|string|max:1000',
                'is_published' => 'sometimes|boolean',
                'is_featured' => 'sometimes|boolean'
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            
            $updateData = $request->only(['rating', 'comment', 'response', 'is_published', 'is_featured']);
            
            // If adding a response, set responded_at timestamp
            if ($request->has('response') && !empty($request->response)) {
                $updateData['responded_at'] = now();
            }
            
            $feedback->update($updateData);
            $feedback->load(['booking.service', 'booking.staff']);
            
            return response()->json([
                'success' => true,
                'message' => 'Feedback updated successfully',
                'data' => $feedback
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update feedback',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Remove the specified feedback
     */
    public function destroy($id)
    {
        try {
            $feedback = Feedback::find($id);
            
            if (!$feedback) {
                return response()->json([
                    'success' => false,
                    'message' => 'Feedback not found'
                ], 404);
            }
            
            $feedback->delete();
            
            return response()->json([
                'success' => true,
                'message' => 'Feedback deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete feedback',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get public feedback (published only)
     */
    public function public(Request $request)
    {
        try {
            $query = Feedback::published()
                ->with(['booking.service', 'booking.staff'])
                ->select(['id', 'booking_id', 'client_name', 'rating', 'comment', 'response', 'created_at']);
            
            // Filter by service
            if ($request->has('service_id')) {
                $query->whereHas('booking', function ($q) use ($request) {
                    $q->where('service_id', $request->service_id);
                });
            }
            
            // Filter by staff
            if ($request->has('staff_id')) {
                $query->whereHas('booking', function ($q) use ($request) {
                    $q->where('staff_id', $request->staff_id);
                });
            }
            
            // Filter by minimum rating
            if ($request->has('min_rating')) {
                $query->where('rating', '>=', $request->get('min_rating'));
            }
            
            // Get featured feedback first
            $query->orderBy('is_featured', 'desc')
                  ->orderBy('created_at', 'desc');
            
            $perPage = $request->get('per_page', 10);
            $feedback = $query->paginate($perPage);
            
            return response()->json([
                'success' => true,
                'data' => $feedback
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch public feedback',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get feedback statistics
     */
    public function statistics()
    {
        try {
            $stats = [
                'total_feedback' => Feedback::count(),
                'published_feedback' => Feedback::published()->count(),
                'featured_feedback' => Feedback::featured()->count(),
                'average_rating' => round(Feedback::published()->avg('rating'), 2),
                'rating_distribution' => Feedback::published()
                    ->select('rating', DB::raw('count(*) as count'))
                    ->groupBy('rating')
                    ->orderBy('rating')
                    ->get(),
                'recent_feedback' => Feedback::published()
                    ->with(['booking.service'])
                    ->latest()
                    ->take(5)
                    ->get()
            ];
            
            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch feedback statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Toggle featured status
     */
    public function toggleFeatured($id)
    {
        try {
            $feedback = Feedback::find($id);
            
            if (!$feedback) {
                return response()->json([
                    'success' => false,
                    'message' => 'Feedback not found'
                ], 404);
            }
            
            $feedback->toggleFeatured();
            
            return response()->json([
                'success' => true,
                'message' => 'Featured status updated successfully',
                'data' => $feedback
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update featured status',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Toggle published status
     */
    public function togglePublished($id)
    {
        try {
            $feedback = Feedback::find($id);
            
            if (!$feedback) {
                return response()->json([
                    'success' => false,
                    'message' => 'Feedback not found'
                ], 404);
            }
            
            $feedback->togglePublished();
            
            return response()->json([
                'success' => true,
                'message' => 'Published status updated successfully',
                'data' => $feedback
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update published status',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get feedback statistics
     */
    public function getStats()
    {
        try {
            $total = Feedback::count();
            $published = Feedback::where('is_published', true)->count();
            $featured = Feedback::where('is_featured', true)->count();
            $averageRating = Feedback::avg('rating') ?: 0;
            
            // Rating distribution
            $ratingDistribution = [];
            for ($i = 1; $i <= 5; $i++) {
                $ratingDistribution[$i] = Feedback::where('rating', $i)->count();
            }
            
            // Recent feedback count (last 30 days)
            $recentCount = Feedback::where('created_at', '>=', now()->subDays(30))->count();
            
            return response()->json([
                'success' => true,
                'total' => $total,
                'published' => $published,
                'featured' => $featured,
                'average_rating' => round($averageRating, 2),
                'rating_distribution' => $ratingDistribution,
                'recent_count' => $recentCount
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch feedback statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get public feedback (published feedback only)
     */
    public function getPublicFeedback()
    {
        try {
            $feedback = Feedback::with(['booking.service'])
                ->where('is_published', true)
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'rating' => $item->rating,
                        'comment' => $item->comment,
                        'client_name' => $item->client_name,
                        'service' => $item->booking->service->name ?? 'Unknown Service',
                        'created_at' => $item->created_at->format('Y-m-d H:i:s')
                    ];
                });
            
            return response()->json([
                'success' => true,
                'data' => $feedback
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch public feedback',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
