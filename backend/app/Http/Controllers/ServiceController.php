<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class ServiceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Service::with('category')->active()->ordered();

        // Фильтр по категории
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Поиск по названию
        if ($request->has('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // Фильтр по цене
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        $services = $query->get();

        return response()->json([
            'success' => true,
            'data' => $services
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'category_id' => 'required|exists:categories,id',
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'price' => 'required|numeric|min:0',
                'duration' => 'required|integer|min:1',
                'image' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);

            $service = Service::create($validated);
            $service->load('category');

            return response()->json([
                'success' => true,
                'message' => 'Услуга успешно создана',
                'data' => $service
            ], 201);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка валидации',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при создании услуги'
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Service $service): JsonResponse
    {
        try {
            $service->load(['category', 'staff']);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $service->id,
                    'name' => $service->name,
                    'description' => $service->description,
                    'price' => $service->price,
                    'duration' => $service->duration,
                    'image' => $service->image,
                    'is_active' => $service->is_active,
                    'sort_order' => $service->sort_order,
                    'category_id' => $service->category_id,
                    'created_at' => $service->created_at,
                    'updated_at' => $service->updated_at,
                    'category' => $service->category,
                    'staff' => $service->staff
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при получении сервиса'
            ], 500);
        }
    }

    /**
     * Get staff members for a specific service.
     */
    public function getStaff(Service $service): JsonResponse
    {
        try {
            $staff = $service->staff()->get();

            return response()->json([
                'success' => true,
                'data' => $staff
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch staff for service',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Service $service): JsonResponse
    {
        try {
            $validated = $request->validate([
                'category_id' => 'sometimes|required|exists:categories,id',
                'name' => 'sometimes|required|string|max:255',
                'description' => 'nullable|string',
                'price' => 'sometimes|required|numeric|min:0',
                'duration' => 'sometimes|required|integer|min:1',
                'image' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);

            $service->update($validated);
            $service->load('category');

            return response()->json([
                'success' => true,
                'message' => 'Услуга успешно обновлена',
                'data' => $service
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка валидации',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при обновлении услуги'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Service $service): JsonResponse
    {
        try {
            // Проверяем, есть ли активные бронирования
            if ($service->bookings()->whereIn('status', ['pending', 'confirmed'])->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Нельзя удалить услугу с активными бронированиями'
                ], 400);
            }

            $service->delete();

            return response()->json([
                'success' => true,
                'message' => 'Услуга успешно удалена'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при удалении услуги'
            ], 500);
        }
    }

    /**
     * Get services by category.
     */
    public function byCategory(Category $category): JsonResponse
    {
        $services = $category->services()
            ->active()
            ->ordered()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $services
        ]);
    }

    /**
     * Get services by category ID.
     */
    public function getByCategory($categoryId): JsonResponse
    {
        try {
            $category = Category::findOrFail($categoryId);
            $services = $category->services()
                ->active()
                ->ordered()
                ->get();

            return response()->json([
                'data' => $services
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Категория не найдена'
            ], 404);
        }
    }
}
