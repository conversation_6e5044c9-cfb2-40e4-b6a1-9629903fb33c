<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Feedback extends Model
{
    use HasFactory;
    protected $fillable = [
        'booking_id',
        'user_id',
        'client_name',
        'client_email',
        'rating',
        'comment',
        'response',
        'responded_at',
        'is_published',
        'is_featured'
    ];

    protected $casts = [
        'rating' => 'integer',
        'is_published' => 'boolean',
        'is_featured' => 'boolean',
        'responded_at' => 'datetime'
    ];

    /**
     * Get the booking that this feedback belongs to
     */
    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    /**
     * Get the user that left this feedback
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get only published feedback
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope to get only featured feedback
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true)->where('is_published', true);
    }

    /**
     * Scope to filter by rating
     */
    public function scopeByRating($query, $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope to get feedback with minimum rating
     */
    public function scopeMinRating($query, $minRating = 4)
    {
        return $query->where('rating', '>=', $minRating);
    }

    /**
     * Add response to feedback
     */
    public function addResponse($response)
    {
        $this->update([
            'response' => $response,
            'responded_at' => Carbon::now()
        ]);
    }

    /**
     * Toggle featured status
     */
    public function toggleFeatured()
    {
        $this->update(['is_featured' => !$this->is_featured]);
    }

    /**
     * Toggle published status
     */
    public function togglePublished()
    {
        $this->update(['is_published' => !$this->is_published]);
    }

    /**
     * Get star rating as string
     */
    public function getStarRatingAttribute()
    {
        return str_repeat('★', $this->rating) . str_repeat('☆', 5 - $this->rating);
    }

    /**
     * Check if feedback has response
     */
    public function getHasResponseAttribute()
    {
        return !empty($this->response);
    }
}
