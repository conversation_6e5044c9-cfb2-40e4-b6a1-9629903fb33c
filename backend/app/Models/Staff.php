<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Staff extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'email',
        'phone',
        'bio',
        'photo',
        'position',
        'specializations',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'specializations' => 'array',
    ];

    /**
     * Get the bookings for the staff member.
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get the services this staff member specializes in.
     */
    public function services(): BelongsToMany
    {
        return $this->belongsToMany(Service::class, 'staff_service');
    }

    /**
     * Scope a query to only include active staff.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Check if staff member can perform a service.
     */
    public function canPerformService($serviceId)
    {
        return in_array($serviceId, $this->specializations ?? []);
    }

    /**
     * Get specialized services.
     */
    public function getSpecializedServicesAttribute()
    {
        if (!$this->specializations) {
            return collect();
        }
        
        return Service::whereIn('id', $this->specializations)->get();
    }
}
