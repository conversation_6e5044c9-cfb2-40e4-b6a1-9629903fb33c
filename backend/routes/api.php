<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\StaffController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\AnalyticsController;
use App\Http\Controllers\FeedbackController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\SpaConfigController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/auth/login', [AuthController::class, 'login']);
Route::post('/auth/register', [AuthController::class, 'register']);

// Admin auth routes
Route::post('/admin/login', [AuthController::class, 'adminLogin']);

// Public API routes for client
Route::get('/categories', [CategoryController::class, 'index']);
Route::get('/categories/{id}/services', [ServiceController::class, 'getByCategory']);
Route::get('/services', [ServiceController::class, 'index']);
Route::get('/services/{service}', [ServiceController::class, 'show']);
Route::get('/services/{service}/staff', [ServiceController::class, 'getStaff']);
Route::get('/staff', [StaffController::class, 'index']);
Route::get('/staff/{id}/availability', [StaffController::class, 'getAvailability']);
Route::post('/bookings', [BookingController::class, 'store']);
Route::get('/feedback/public', [FeedbackController::class, 'getPublicFeedback']);
Route::get('/contacts', [ContactController::class, 'index']);
Route::post('/contacts', [ContactController::class, 'store']);
Route::get('/spa-config', [SpaConfigController::class, 'index']);
Route::get('/translations/{locale}', [SpaConfigController::class, 'getTranslations']);

// Protected routes (require authentication)
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::get('/auth/user', [AuthController::class, 'user']);
    
    // User routes
    Route::post('/feedback', [FeedbackController::class, 'store']);
    Route::get('/bookings', [BookingController::class, 'index']);
    Route::get('/bookings/{id}', [BookingController::class, 'show']);
    Route::put('/bookings/{id}', [BookingController::class, 'update']);
    Route::delete('/bookings/{id}', [BookingController::class, 'destroy']);
    
    // Admin routes with /admin prefix
    Route::middleware(['admin'])->prefix('admin')->group(function () {
        // Admin routes for categories
        Route::get('/categories', [CategoryController::class, 'index']);
        Route::get('/categories/{id}', [CategoryController::class, 'show']);
        Route::post('/categories', [CategoryController::class, 'store']);
        Route::put('/categories/{id}', [CategoryController::class, 'update']);
        Route::delete('/categories/{id}', [CategoryController::class, 'destroy']);
        
        // Admin routes for services
        Route::get('/services', [ServiceController::class, 'index']);
        Route::get('/services/{service}', [ServiceController::class, 'show']);
        Route::post('/services', [ServiceController::class, 'store']);
        Route::put('/services/{service}', [ServiceController::class, 'update']);
        Route::delete('/services/{service}', [ServiceController::class, 'destroy']);
        
        // Admin routes for staff
        Route::get('/staff', [StaffController::class, 'index']);
        Route::get('/staff/{id}', [StaffController::class, 'show']);
        Route::post('/staff', [StaffController::class, 'store']);
        Route::put('/staff/{id}', [StaffController::class, 'update']);
        Route::delete('/staff/{id}', [StaffController::class, 'destroy']);
        Route::post('/staff/{id}/availability', [StaffController::class, 'setAvailability']);
        
        // Admin routes for bookings
        Route::get('/bookings', [BookingController::class, 'index']);
        Route::get('/bookings/{id}', [BookingController::class, 'show']);
        Route::put('/bookings/{id}', [BookingController::class, 'update']);
        Route::delete('/bookings/{id}', [BookingController::class, 'destroy']);
        Route::put('/bookings/{id}/status', [BookingController::class, 'updateStatus']);
        
        // Analytics routes
        Route::get('/analytics/dashboard', [AnalyticsController::class, 'dashboard']);
        Route::get('/analytics/revenue', [AnalyticsController::class, 'revenue']);
        Route::get('/analytics/revenue/daily', [AnalyticsController::class, 'dailyRevenue']);
        Route::get('/analytics/revenue/by-service', [AnalyticsController::class, 'revenueByService']);
        Route::get('/analytics/revenue/by-category', [AnalyticsController::class, 'revenueByCategory']);
        Route::get('/analytics/bookings', [AnalyticsController::class, 'bookings']);
        Route::get('/analytics/bookings/hourly', [AnalyticsController::class, 'hourlyBookings']);
        Route::get('/analytics/bookings/weekly', [AnalyticsController::class, 'weeklyBookings']);
        Route::get('/analytics/bookings/duration', [AnalyticsController::class, 'bookingDuration']);
        Route::get('/analytics/bookings/cancellations', [AnalyticsController::class, 'cancellationAnalysis']);
        
        // Admin routes for feedback
        Route::get('/feedback', [FeedbackController::class, 'index']);
        Route::get('/feedback/stats', [FeedbackController::class, 'getStats']);
        Route::get('/feedback/{id}', [FeedbackController::class, 'show']);
        Route::put('/feedback/{id}', [FeedbackController::class, 'update']);
        Route::delete('/feedback/{id}', [FeedbackController::class, 'destroy']);
        Route::put('/feedback/{id}/toggle-featured', [FeedbackController::class, 'toggleFeatured']);
        Route::put('/feedback/{id}/toggle-published', [FeedbackController::class, 'togglePublished']);
        
        // Admin routes for contacts
        Route::get('/contacts', [ContactController::class, 'index']);
        Route::post('/contacts', [ContactController::class, 'store']);
        Route::put('/contacts/{id}', [ContactController::class, 'update']);
        Route::delete('/contacts/{id}', [ContactController::class, 'destroy']);
        
        // Admin routes for SPA config
        Route::get('/spa-config', [SpaConfigController::class, 'index']);
        Route::post('/spa-config', [SpaConfigController::class, 'store']);
        Route::put('/spa-config/{id}', [SpaConfigController::class, 'update']);
        Route::delete('/spa-config/{id}', [SpaConfigController::class, 'destroy']);
        Route::post('/translations', [SpaConfigController::class, 'storeTranslations']);
        Route::put('/translations/{locale}', [SpaConfigController::class, 'updateTranslations']);
    });
});