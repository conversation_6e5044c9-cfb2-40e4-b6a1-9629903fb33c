<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Получаем сервис с загруженными связями
$service = App\Models\Service::with(['category', 'staff'])->first();

if ($service) {
    echo "Service found:\n";
    echo "ID: " . $service->id . "\n";
    echo "Name: " . $service->name . "\n";
    echo "Description: " . $service->description . "\n";
    echo "Price: " . $service->price . "\n";
    echo "Duration: " . $service->duration . "\n";
    echo "Image: " . $service->image . "\n";
    echo "Is Active: " . ($service->is_active ? 'true' : 'false') . "\n";
    echo "Category ID: " . $service->category_id . "\n";
    echo "\nSerialized to array:\n";
    print_r($service->toArray());
} else {
    echo "No service found\n";
}