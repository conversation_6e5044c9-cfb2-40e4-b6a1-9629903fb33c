<?php
// Test the debug endpoint to see path processing
echo "=== TESTING DEBUG ENDPOINT ===\n";

$url = 'http://localhost:3002/api/debug';
echo "Testing URL: $url\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Status Code: $httpCode\n";

if ($error) {
    echo "cURL Error: $error\n";
} else {
    echo "Response:\n";
    $decoded = json_decode($response, true);
    if ($decoded !== null) {
        echo json_encode($decoded, JSON_PRETTY_PRINT) . "\n";
    } else {
        echo "Raw response: $response\n";
    }
}
?>