<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="w-full max-w-[1170px] mx-auto px-4 py-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <router-link 
              to="/" 
              class="text-lg font-bold text-gray-900 hover:text-primary-600 transition-colors cursor-pointer"
            >
              Терем у реки
            </router-link>
          </div>
          <div class="flex items-center space-x-2">
            <button 
              @click="showPhoneModal = true"
              class="p-2 text-gray-600 hover:text-primary-600 transition-colors"
              title="Полезные телефоны"
            >
              <IconPhone class="w-5 h-5" />
            </button>
            <button 
              @click="showFeedbackModal = true"
              class="p-2 text-gray-600 hover:text-primary-600 transition-colors"
              title="Оставить отзыв"
            >
              <IconMessageSquare class="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="w-full max-w-[1170px] mx-auto px-4 py-6">
      <router-view v-slot="{ Component, route }">
        <transition 
          name="fade" 
          mode="out-in"
          appear
        >
          <component :is="Component" :key="route.path" />
        </transition>
      </router-view>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-8">
      <div class="w-full max-w-[1170px] mx-auto px-4 py-4">
        <div class="text-center space-y-3">
          <BaseButton 
            @click="callHotel"
            variant="primary"
            size="lg"
            full-width
            class="flex items-center justify-center"
          >
            <IconPhone class="w-4 h-4 mr-2" />
            Связаться с отелем
          </BaseButton>
          <p class="text-xs text-gray-500">
            © 2024 Отель "Терем у реки". Все права защищены.
          </p>
        </div>
      </div>
    </footer>

    <!-- Modals -->
    <PhoneModal 
      :is-open="showPhoneModal" 
      @close="showPhoneModal = false" 
    />
    <FeedbackModal 
      :is-open="showFeedbackModal" 
      @close="showFeedbackModal = false" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BaseButton from './components/BaseButton.vue'
import PhoneModal from './components/PhoneModal.vue'
import FeedbackModal from './components/FeedbackModal.vue'

// Icons
import IconPhone from '~icons/lucide/phone'
import IconMessageSquare from '~icons/lucide/message-square'

// State
const showPhoneModal = ref(false)
const showFeedbackModal = ref(false)

// Methods
const callHotel = () => {
  window.location.href = 'tel:+78001234567'
}
</script>