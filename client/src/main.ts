import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import './style.css'

// Import pages
import HomePage from './pages/HomePage.vue'
import CategoryPage from './pages/CategoryPage.vue'
import BookingPage from './pages/BookingPage.vue'
import ConfirmationPage from './pages/ConfirmationPage.vue'


// Router configuration
const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: HomePage
    },
    {
      path: '/category/:slug',
      name: 'Category',
      component: CategoryPage,
      props: true
    },
    {
      path: '/booking',
      name: 'Booking',
      component: BookingPage
    },
    {
      path: '/confirmation/:code',
      name: 'Confirmation',
      component: ConfirmationPage,
      props: true
    },

    {
      path: '/link',
      name: 'Link',
      beforeEnter: (to, _from, next) => {
        // Handle room parameter and save to cookies
        if (to.query.room) {
          const roomNumber = to.query.room as string
          // Save room number to cookies for 30 days
          document.cookie = `room_number=${roomNumber}; max-age=${30 * 24 * 60 * 60}; path=/`
        }
        // Redirect to home page
        next({ path: '/', replace: true })
      },
      component: () => null
    }
  ]
})

// Create and mount app
const app = createApp(App)
app.use(router)
app.mount('#app')