@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }
  
  body {
    @apply bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 text-gray-900 min-h-screen;
    line-height: 1.6;
    font-weight: 400;
  }
  
  /* Typography Hierarchy */
  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight tracking-tight;
    font-weight: 700;
    line-height: 1.1;
  }
  
  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl font-semibold text-gray-800 leading-tight;
    font-weight: 600;
    line-height: 1.2;
  }
  
  h3 {
    @apply text-2xl md:text-3xl font-semibold text-gray-800 leading-snug;
    font-weight: 600;
    line-height: 1.3;
  }
  
  h4 {
    @apply text-xl md:text-2xl font-medium text-gray-700 leading-snug;
    font-weight: 500;
    line-height: 1.4;
  }
  
  h5 {
    @apply text-lg md:text-xl font-medium text-gray-700 leading-normal;
    font-weight: 500;
    line-height: 1.5;
  }
  
  h6 {
    @apply text-base md:text-lg font-medium text-gray-600 leading-normal;
    font-weight: 500;
    line-height: 1.5;
  }
  
  p {
    @apply text-base text-gray-700 leading-relaxed;
    line-height: 1.6;
  }
  
  .text-large {
    @apply text-lg text-gray-700 leading-relaxed;
    line-height: 1.6;
  }
  
  .text-small {
    @apply text-sm text-gray-600 leading-normal;
    line-height: 1.5;
  }
  
  .text-caption {
    @apply text-xs text-gray-500 leading-normal;
    line-height: 1.4;
  }
  
  /* Enhanced contrast for better readability */
  .text-primary {
    @apply text-primary-700;
  }
  
  .text-secondary {
    @apply text-gray-600;
  }
  
  .text-muted {
    @apply text-gray-500;
  }
}

@layer components {
  .btn {
    @apply px-6 py-3 rounded-xl font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 shadow-sm hover:shadow-md;
    font-weight: 600;
    letter-spacing: 0.025em;
    line-height: 1.2;
  }
  
  .btn-sm {
    @apply px-4 py-2 text-sm rounded-lg;
    font-weight: 500;
  }
  
  .btn-lg {
    @apply px-8 py-4 text-lg rounded-2xl;
    font-weight: 600;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800;
    box-shadow: 0 4px 14px 0 rgba(125, 181, 44, 0.25);
  }
  
  .btn-primary:hover {
    box-shadow: 0 6px 20px 0 rgba(125, 181, 44, 0.35);
    transform: translateY(-1px);
  }
  
  .btn-secondary {
    @apply btn bg-white text-gray-700 border-2 border-gray-200 hover:bg-gray-50 hover:border-gray-300 focus:ring-gray-500;
  }
  
  .btn-accent {
    @apply btn bg-accent-600 text-white hover:bg-accent-700 focus:ring-accent-500 active:bg-accent-800;
    box-shadow: 0 4px 14px 0 rgba(249, 115, 22, 0.25);
  }
  
  .btn-accent:hover {
    box-shadow: 0 6px 20px 0 rgba(249, 115, 22, 0.35);
    transform: translateY(-1px);
  }
  
  .card {
    @apply bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/30 p-6 hover:shadow-xl transition-all duration-300;
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  .card:hover {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  .card-title {
    @apply text-xl font-semibold text-gray-900 mb-2 leading-tight;
    font-weight: 600;
  }
  
  .card-subtitle {
    @apply text-sm font-medium text-gray-600 mb-4 uppercase tracking-wide;
    font-weight: 500;
    letter-spacing: 0.05em;
  }
  
  .card-description {
    @apply text-gray-700 leading-relaxed;
    line-height: 1.6;
  }
  
  .input {
    @apply w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white/70 backdrop-blur-sm transition-all duration-200;
    font-weight: 400;
    line-height: 1.5;
  }
  
  .input:focus {
    box-shadow: 0 0 0 3px rgba(125, 181, 44, 0.1);
  }
  
  .input::placeholder {
    @apply text-gray-400;
    font-weight: 400;
  }
  
  .checkbox {
    @apply w-5 h-5 text-primary-600 bg-white/70 border-2 border-gray-300 rounded-lg focus:ring-primary-500 focus:ring-2 transition-all duration-200;
  }
  
  .checkbox:checked {
    @apply bg-primary-600 border-primary-600;
  }
  
  .modal-overlay {
    @apply fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50;
  }
  
  .modal-content {
    @apply bg-white/95 backdrop-blur-md rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto shadow-2xl border border-white/20;
  }
  
  .category-card {
    @apply card hover:shadow-2xl transition-all duration-300 cursor-pointer;
    border: 2px solid transparent;
  }
  
  .category-card:hover {
    border-color: rgba(125, 181, 44, 0.3);
    box-shadow: 0 12px 45px -10px rgba(125, 181, 44, 0.25), 0 25px 30px -5px rgba(0, 0, 0, 0.1);
  }
  
  .category-title {
    @apply text-xl font-semibold text-gray-900 mb-2 leading-tight;
    font-weight: 600;
  }
  
  .category-description {
    @apply text-gray-600 leading-relaxed;
    line-height: 1.6;
  }
  
  .category-icon {
    @apply w-12 h-12 text-primary-600 mb-4;
  }
  
  .service-item {
    @apply flex items-center space-x-4 p-5 rounded-xl hover:bg-gradient-to-r hover:from-primary-50 hover:to-blue-50 transition-all duration-300 cursor-pointer border border-transparent hover:border-primary-200;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
  }
  
  .service-item:hover {
    box-shadow: 0 4px 12px 0 rgba(125, 181, 44, 0.15);
    transform: translateY(-1px);
  }
  
  .service-card {
    @apply bg-white border-2 border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all duration-300 hover:border-primary-300;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
  }
  
  .service-card:hover {
    box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
  
  .service-title {
    @apply text-lg font-semibold text-gray-900 mb-2 leading-tight;
    font-weight: 600;
  }
  
  .service-description {
    @apply text-gray-600 text-sm leading-relaxed mb-3;
    line-height: 1.5;
  }
  
  .service-price {
    @apply text-xl font-bold text-primary-700;
    font-weight: 700;
  }
  
  .service-tag {
    @apply inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700;
    font-weight: 500;
  }

  /* Mobile improvements for service cards */
  @media (max-width: 640px) {
    .service-card {
      @apply border-2 border-gray-300 shadow-sm min-h-[140px] p-4;
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
    }
    
    .service-card:hover {
      @apply shadow-md border-primary-400;
      box-shadow: 0 6px 20px 0 rgba(125, 181, 44, 0.15);
    }
    
    .service-title {
      @apply text-base font-semibold;
    }
    
    .service-price {
      @apply text-lg font-bold;
    }
  }
  
  .hero-section {
    @apply bg-gradient-to-r from-primary-600 via-blue-600 to-indigo-600 text-white rounded-3xl p-8 shadow-2xl;
    box-shadow: 0 20px 60px -10px rgba(125, 181, 44, 0.3), 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }
  
  .hero-title {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 leading-tight;
    font-weight: 700;
    line-height: 1.1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .hero-subtitle {
    @apply text-xl md:text-2xl text-white/90 mb-6 leading-relaxed;
    font-weight: 400;
    line-height: 1.4;
  }
  
  .quick-action-btn {
    @apply flex flex-col items-center p-6 bg-white/60 backdrop-blur-sm rounded-2xl hover:bg-white/80 hover:shadow-xl transition-all duration-300 shadow-lg border border-white/30;
  }
  
  .quick-action-btn {
    @apply flex flex-col items-center justify-center p-6 bg-white rounded-xl border border-gray-200 hover:border-primary-300 transition-all duration-200;
    min-height: 120px;
  }
  
  .quick-action-btn:hover {
    @apply shadow-lg border-primary-300;
    transform: translateY(-1px);
  }
  
  .quick-action-icon {
    @apply w-12 h-12 rounded-xl flex items-center justify-center mb-3;
  }
  
  .quick-action-title {
    @apply text-sm font-semibold text-gray-900 text-center;
    line-height: 1.2;
  }
  
  .quick-action-description {
    @apply text-xs text-gray-500 text-center mt-1;
    line-height: 1.3;
  }

  /* Adaptive card layout */
  .flex-wrap > .w-full {
    flex: 1 1 100%;
  }
  
  @media (min-width: 768px) {
    .flex-wrap > .md\:w-1\/2 {
      flex: 1 1 calc(50% - 0.5rem);
      max-width: calc(50% - 0.5rem);
    }
    
    .flex-wrap > .md\:w-1\/3 {
      flex: 1 1 calc(33.333% - 0.667rem);
      max-width: calc(33.333% - 0.667rem);
    }
  }
  
  /* Service card specific styles */
  .service-card {
    @apply overflow-hidden;
    min-height: 160px;
  }
  
  /* Responsive grid improvements */
  .flex-wrap {
    display: flex;
    flex-wrap: wrap;
  }
  
  .flex-shrink-0 {
    flex-shrink: 0;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  /* Typography utilities */
  .font-display {
    font-family: 'Inter', system-ui, sans-serif;
    font-weight: 700;
    letter-spacing: -0.025em;
  }
  
  .font-body {
    font-family: 'Inter', system-ui, sans-serif;
    font-weight: 400;
    line-height: 1.6;
  }
  
  .tracking-tight {
    letter-spacing: -0.025em;
  }
  
  .tracking-wide {
    letter-spacing: 0.05em;
  }
  
  /* Visual hierarchy utilities */
  .elevation-1 {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }
  
  .elevation-2 {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  .elevation-3 {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  .elevation-4 {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  /* Focus states */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .focus-ring-inset {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset;
  }
  
  /* Smooth transitions */
  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .transition-bounce {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
  
  /* Page transition animations */
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease-in-out;
  }
  
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  
  .fade-enter-to,
  .fade-leave-from {
    opacity: 1;
  }
}