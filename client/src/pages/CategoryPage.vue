<template>
  <div class="space-y-8">
    <!-- Loading State -->
    <div v-if="loading" class="space-y-6">
      <!-- Back Button Skeleton -->
      <SkeletonLoader variant="button" width="120px" />
      
      <!-- Category Header Skeleton -->
      <SkeletonLoader variant="card" />
      
      <!-- Services List Skeleton -->
      <SkeletonLoader variant="service-list" :count="6" />
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-12">
      <div class="card max-w-md mx-auto">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <IconAlertCircle class="w-8 h-8 text-red-600" />
        </div>
        <p class="text-red-600 mb-4">{{ error }}</p>
        <button @click="loadCategoryData" class="btn-primary">Попробовать снова</button>
      </div>
    </div>

    <!-- Content -->
    <div v-else>
      <!-- Back Button -->
      <button 
        @click="$router.back()"
        class="inline-flex items-center space-x-2 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full shadow-md border border-white/30 text-primary-600 hover:text-primary-700 hover:bg-white/80 transition-all duration-300 mb-6"
      >
        <IconArrowLeft class="w-4 h-4" />
        <span class="font-medium">Назад</span>
      </button>

      <!-- Category Header -->
      <div class="card relative overflow-hidden mb-4" v-if="category">
        <!-- Background Gradient -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary-50/30 to-blue-50/30"></div>
        
        <div class="relative z-10 flex items-start space-x-6">
          <div class="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl shadow-lg">
            <component 
              :is="getCategoryIcon(category.icon)"
              class="w-10 h-10 text-primary-600"
            />
          </div>
          
          <div class="flex-1">
            <h1 class="text-2xl font-bold text-gray-900 mb-3">{{ category.name }}</h1>
            <p class="text-gray-600 mb-4 leading-relaxed">{{ category.description }}</p>
            
            <!-- Working Hours -->
            <div class="inline-flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium" :class="isOpenNow ? 'text-green-700 bg-green-100/50' : 'text-red-700 bg-red-100/50'">
              <IconClock class="w-4 h-4" />
              <span>{{ workingStatus }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Services List -->
      <div class="card">
        <div class="flex items-center mb-6">
          <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center mr-3">
            <IconList class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-bold text-gray-900">Доступные услуги</h2>
        </div>
        
        <div class="grid gap-4">
          <div 
            v-for="service in services" 
            :key="service.id"
            class="service-card group relative overflow-hidden min-h-[120px] sm:min-h-[140px]"
          >
            <!-- Service Image -->
            <div 
              v-if="service.image"
              class="absolute top-2 right-2 w-[135px] h-[135px] bg-cover bg-center rounded-xl opacity-70 group-hover:opacity-90 transition-opacity duration-300 shadow-lg"
              :style="{ backgroundImage: `url(${service.image})` }"
            ></div>
            
            <div class="flex items-start space-x-4">
              <input 
                :id="`service-${service.id}`"
                v-model="selectedServices"
                :value="service.id"
                type="checkbox"
                class="checkbox mt-1"
              />
              
              <label 
                :for="`service-${service.id}`"
                class="flex-1 cursor-pointer"
              >
                <div class="pr-36">
                  <h3 class="service-title group-hover:text-primary-700 transition-colors">{{ service.name }}</h3>
                  <p class="service-description mt-2">{{ service.description }}</p>
                  
                  <div class="flex flex-wrap gap-3 mt-4">
                    <span class="service-tag bg-blue-100/50 text-blue-700">
                      <IconClock class="w-4 h-4 mr-1" />
                      <span>{{ service.duration }} мин</span>
                    </span>
                    
                    <span v-if="service.max_persons > 1" class="service-tag bg-green-100/50 text-green-700">
                      <IconUsers class="w-4 h-4 mr-1" />
                      <span>до {{ service.max_persons }} чел.</span>
                    </span>
                    
                    <span v-if="service.requires_booking" class="service-tag bg-amber-100/50 text-amber-700">
                      <IconCalendar class="w-4 h-4 mr-1" />
                      <span>Требует бронирования</span>
                    </span>
                  </div>
                  
                  <div class="mt-3">
                    <div class="service-price" :class="service.is_free ? 'text-green-600' : 'text-primary-700'">
                      {{ service.is_free ? 'Бесплатно' : `${service.price}₽` }}
                    </div>
                  </div>
                </div>
              </label>
          </div>
        </div>
        
        <!-- No Services -->
        <div v-if="services.length === 0" class="text-center py-8 text-gray-500">
          <IconPackage class="w-12 h-12 mx-auto mb-2 text-gray-300" />
          <p>В данной категории пока нет доступных услуг</p>
        </div>
      </div>

      <!-- Selected Services Summary -->
      <BaseCard v-if="selectedServices.length > 0" class="bg-primary-50 border-primary-200">
        <template #header>
          <h3 class="font-semibold text-gray-900">Выбранные услуги</h3>
        </template>
        
        <div class="space-y-2 mb-4">
          <div 
            v-for="service in selectedServicesData" 
            :key="service.id"
            class="flex justify-between items-center text-sm"
          >
            <span>{{ service.name }}</span>
            <span class="font-medium">{{ service.is_free ? 'Бесплатно' : `${service.price}₽` }}</span>
          </div>
        </div>
        
        <div class="border-t border-primary-200 pt-3">
          <div class="flex justify-between items-center mb-4">
            <span class="font-semibold">Итого:</span>
            <span class="font-bold text-lg">{{ totalPrice === 0 ? 'Бесплатно' : `${totalPrice}₽` }}</span>
          </div>
          
          <div class="text-sm text-gray-600 mb-4">
            <p>Общее время: {{ totalDuration }} мин</p>
            <p v-if="requiresBooking">⚠️ Некоторые услуги требуют предварительного бронирования</p>
          </div>
          
          <BaseButton 
            @click="proceedToBooking"
            variant="primary"
            size="lg"
            full-width
            :disabled="!canProceed"
          >
            {{ totalPrice === 0 ? 'Заказать бесплатные услуги' : 'Перейти к бронированию' }}
          </BaseButton>
        </div>
      </BaseCard>

      <!-- Call to Action -->
      <div v-else class="text-center py-8">
        <IconCheckSquare class="w-12 h-12 mx-auto mb-3 text-gray-300" />
        <p class="text-gray-600">Выберите услуги для продолжения</p>
      </div>
    </div>
  </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { categoriesApi } from '../services/api'
import type { Category, Service, WorkingHours } from '../types'
import { BaseCard, BaseButton, SkeletonLoader } from '../components'

// Icons
import IconArrowLeft from '~icons/lucide/arrow-left'
import IconClock from '~icons/lucide/clock'
import IconUsers from '~icons/lucide/users'
import IconCalendar from '~icons/lucide/calendar'
import IconPackage from '~icons/lucide/package'
import IconCheckSquare from '~icons/lucide/check-square'
import IconBed from '~icons/lucide/bed'
import IconWaves from '~icons/lucide/waves'
import IconUtensils from '~icons/lucide/utensils'
import IconPlus from '~icons/lucide/plus'
import IconAlertCircle from '~icons/lucide/alert-circle'
import IconList from '~icons/lucide/list'

// Props
const props = defineProps<{
  slug: string
}>()

// Setup
const router = useRouter()

// State
const category = ref<Category | null>(null)
const services = ref<Service[]>([])
const selectedServices = ref<number[]>([])
const loading = ref(true)
const error = ref('')

// Computed
const selectedServicesData = computed(() => {
  return services.value.filter(service => selectedServices.value.includes(service.id))
})

const totalPrice = computed(() => {
  return selectedServicesData.value.reduce((sum, service) => sum + service.price, 0)
})

const totalDuration = computed(() => {
  return selectedServicesData.value.reduce((sum, service) => sum + service.duration, 0)
})

const requiresBooking = computed(() => {
  return selectedServicesData.value.some(service => service.requires_booking)
})

const canProceed = computed(() => {
  return selectedServices.value.length > 0
})

const isOpenNow = computed(() => {
  if (!category.value) return false
  return checkIfOpen(category.value.working_hours)
})

const workingStatus = computed(() => {
  if (!category.value) return ''
  return getWorkingStatus(category.value.working_hours)
})

// Methods
const loadCategoryData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // Загрузка категории и услуг
    const servicesResponse = await categoriesApi.getServices(props.slug)
    services.value = servicesResponse.data.data
    
    // Найдем категорию из списка всех категорий
    const categoriesResponse = await categoriesApi.getAll()
    category.value = categoriesResponse.data.data.find(c => c.slug === props.slug) || null
    
    if (!category.value) {
      throw new Error('Категория не найдена')
    }
  } catch (err) {
    error.value = 'Ошибка загрузки данных категории'
    console.error(err)
  } finally {
    loading.value = false
  }
}

const getCategoryIcon = (iconName?: string) => {
  const icons: Record<string, any> = {
    bed: IconBed,
    waves: IconWaves,
    utensils: IconUtensils,
    plus: IconPlus
  }
  return icons[iconName || 'plus'] || IconPlus
}

const checkIfOpen = (workingHours: WorkingHours) => {
  const now = dayjs()
  const currentDay = now.format('dddd').toLowerCase()
  const currentTime = now.format('HH:mm')
  
  const dayMapping: Record<string, keyof WorkingHours> = {
    'понедельник': 'monday',
    'вторник': 'tuesday',
    'среда': 'wednesday',
    'четверг': 'thursday',
    'пятница': 'friday',
    'суббота': 'saturday',
    'воскресенье': 'sunday'
  }
  
  const todayHours = workingHours[dayMapping[currentDay]]
  
  if (!todayHours || todayHours.is_closed) {
    return false
  }
  
  return currentTime >= todayHours.start && currentTime <= todayHours.end
}

const getWorkingStatus = (workingHours: WorkingHours) => {
  if (checkIfOpen(workingHours)) {
    return 'Открыто'
  }
  
  const now = dayjs()
  const currentDay = now.format('dddd').toLowerCase()
  
  const dayMapping: Record<string, keyof WorkingHours> = {
    'понедельник': 'monday',
    'вторник': 'tuesday',
    'среда': 'wednesday',
    'четверг': 'thursday',
    'пятница': 'friday',
    'суббота': 'saturday',
    'воскресенье': 'sunday'
  }
  
  const todayHours = workingHours[dayMapping[currentDay]]
  
  if (!todayHours || todayHours.is_closed) {
    return 'Закрыто'
  }
  
  return `Закрыто до ${todayHours.start}`
}

const proceedToBooking = () => {
  // Сохраняем выбранные услуги в localStorage для передачи на страницу бронирования
  const bookingData = {
    categorySlug: props.slug,
    selectedServices: selectedServicesData.value,
    totalPrice: totalPrice.value,
    totalDuration: totalDuration.value,
    requiresBooking: requiresBooking.value
  }
  
  localStorage.setItem('bookingData', JSON.stringify(bookingData))
  router.push('/booking')
}

// Lifecycle
onMounted(() => {
  loadCategoryData()
})
</script>