<template>
  <div class="space-y-6">
    <!-- Back Button -->
    <button 
      @click="$router.back()"
      class="flex items-center text-primary-600 hover:text-primary-700"
    >
      <IconArrowLeft class="w-4 h-4 mr-1" />
      Назад
    </button>

    <!-- Loading State -->
    <div v-if="!bookingData" class="space-y-6">
      <SkeletonLoader variant="card" />
      <SkeletonLoader variant="card" />
      <SkeletonLoader variant="button" width="200px" />
    </div>

    <!-- Multi-Step Booking Form -->
    <MultiStepBookingForm v-else :booking-data="bookingData" />
  </div>
</template>
        
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import MultiStepBookingForm from '@/components/MultiStepBookingForm.vue'
import { SkeletonLoader } from '@/components'
import IconArrowLeft from '~icons/lucide/arrow-left'

interface BookingData {
  selectedServices: Array<{
    id: string
    name: string
    price: number
    duration: number
    max_persons: number
    is_free: boolean
    requires_booking: boolean
  }>
  totalPrice: number
  totalDuration: number
  requiresBooking: boolean
}

const router = useRouter()
const bookingData = ref<BookingData | null>(null)

onMounted(() => {
  loadBookingData()
})

function loadBookingData() {
  const storedData = localStorage.getItem('bookingData')
  if (storedData) {
    bookingData.value = JSON.parse(storedData)
  } else {
    router.push('/')
  }
}
</script>