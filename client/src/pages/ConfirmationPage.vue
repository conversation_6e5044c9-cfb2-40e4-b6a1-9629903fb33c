<template>
  <div class="space-y-6">
    <!-- Success Header -->
    <div class="text-center">
      <div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
        <IconCheck class="w-8 h-8 text-green-600" />
      </div>
      <h1 class="text-2xl font-bold text-gray-900 mb-2">Заказ подтвержден!</h1>
      <p class="text-gray-600">Ваше бронирование успешно создано</p>
    </div>

    <!-- Confirmation Code -->
    <div class="card bg-green-50 border-green-200">
      <div class="text-center">
        <h2 class="text-lg font-semibold text-gray-900 mb-2">Код подтверждения</h2>
        <div class="text-3xl font-bold text-green-600 mb-2 tracking-wider">{{ confirmationCode }}</div>
        <p class="text-sm text-gray-600">Сохраните этот код для отслеживания заказа</p>
        <button 
          @click="copyCode"
          class="mt-3 text-sm text-green-600 hover:text-green-700 underline"
        >
          Скопировать код
        </button>
      </div>
    </div>

    <!-- Booking Details -->
    <div v-if="booking" class="card">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Детали заказа</h2>
      
      <div class="space-y-4">
        <!-- Services -->
        <div>
          <h3 class="font-medium text-gray-900 mb-2">Услуги:</h3>
          <div class="space-y-2">
            <div 
              v-for="service in booking.services" 
              :key="service.service_id"
              class="flex justify-between items-center py-2 px-3 bg-gray-50 rounded-lg"
            >
              <div>
                <div class="font-medium">{{ service.service_name }}</div>
                <div class="text-sm text-gray-600">{{ service.duration }} мин</div>
              </div>
              <div class="text-right">
                <div class="font-medium">{{ service.price === 0 ? 'Бесплатно' : `${service.price}₽` }}</div>
                <div class="text-sm text-gray-600">× {{ service.quantity }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Date and Time -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <h3 class="font-medium text-gray-900 mb-1">Дата:</h3>
            <p class="text-gray-700">{{ formatDate(booking.date) }}</p>
          </div>
          <div>
            <h3 class="font-medium text-gray-900 mb-1">Время:</h3>
            <p class="text-gray-700">{{ booking.time }}</p>
          </div>
        </div>

        <!-- Persons -->
        <div v-if="booking.persons > 1">
          <h3 class="font-medium text-gray-900 mb-1">Количество персон:</h3>
          <p class="text-gray-700">{{ booking.persons }} {{ getPersonsText(booking.persons) }}</p>
        </div>

        <!-- Guest Info -->
        <div>
          <h3 class="font-medium text-gray-900 mb-2">Контактная информация:</h3>
          <div class="space-y-1 text-sm">
            <div><span class="font-medium">Имя:</span> {{ booking.guest_name }}</div>
            <div v-if="booking.room_number"><span class="font-medium">Номер комнаты:</span> {{ booking.room_number }}</div>
            <div v-if="booking.guest_phone"><span class="font-medium">Телефон:</span> {{ booking.guest_phone }}</div>
            <div v-if="booking.notes"><span class="font-medium">Примечания:</span> {{ booking.notes }}</div>
          </div>
        </div>

        <!-- Total -->
        <div class="border-t border-gray-200 pt-4">
          <div class="flex justify-between items-center">
            <div>
              <div class="font-semibold text-lg">Итого:</div>
              <div class="text-sm text-gray-600">{{ booking.total_duration }} мин</div>
            </div>
            <div class="text-right">
              <div class="font-semibold text-lg">{{ booking.total_price === 0 ? 'Бесплатно' : `${booking.total_price}₽` }}</div>
              <div class="text-sm text-gray-600">{{ getStatusText(booking.status) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Next Steps -->
    <div class="card bg-blue-50 border-blue-200">
      <h2 class="text-lg font-semibold text-gray-900 mb-3">Что дальше?</h2>
      
      <div class="space-y-3 text-sm">
        <div v-if="booking?.total_price === 0" class="flex items-start">
          <IconClock class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <div class="font-medium">Ожидайте выполнения</div>
            <div class="text-gray-600">Наши сотрудники выполнят заказ в указанное время</div>
          </div>
        </div>
        
        <div v-else class="flex items-start">
          <IconCheck class="w-4 h-4 text-green-600 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <div class="font-medium">Бронирование подтверждено</div>
            <div class="text-gray-600">Администратор получил уведомление о вашем бронировании и свяжется с вами для подтверждения деталей</div>
          </div>
        </div>
        
        <div class="flex items-start">
          <IconMessageSquare class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <div class="font-medium">Изменения и отмена</div>
            <div class="text-gray-600">Для изменения или отмены бронирования обратитесь на ресепшн или по телефону +7 (800) 123-45-67</div>
          </div>
        </div>
        
        <div class="flex items-start">
          <IconStar class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <div class="font-medium">Оценка качества</div>
            <div class="text-gray-600">После выполнения услуг мы будем рады получить ваш отзыв</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Information -->
    <div class="card">
      <h2 class="text-lg font-semibold text-gray-900 mb-3">Контакты отеля</h2>
      
      <div class="space-y-2 text-sm">
        <div class="flex items-center">
          <IconPhone class="w-4 h-4 text-gray-500 mr-2" />
          <span>Ресепшн: +7 (800) 123-45-67</span>
        </div>
        <div class="flex items-center">
          <IconClock class="w-4 h-4 text-gray-500 mr-2" />
          <span>Круглосуточно</span>
        </div>
        <div class="flex items-center">
          <IconMapPin class="w-4 h-4 text-gray-500 mr-2" />
          <span>Отель "Терем у реки"</span>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="space-y-3">
      <button 
        @click="$router.push('/')"
        class="btn-primary w-full"
      >
        Вернуться на главную
      </button>
      
      <button 
        @click="shareBooking"
        class="btn-secondary w-full"
      >
        <IconShare2 class="w-4 h-4 mr-2" />
        Поделиться деталями заказа
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import 'dayjs/locale/ru'
import { bookingsApi } from '../services/api'
import type { Booking } from '../types'

// Icons
import IconCheck from '~icons/lucide/check'
import IconClock from '~icons/lucide/clock'
import IconPhone from '~icons/lucide/phone'
import IconMessageSquare from '~icons/lucide/message-square'
import IconStar from '~icons/lucide/star'
import IconMapPin from '~icons/lucide/map-pin'
import IconShare2 from '~icons/lucide/share-2'

// Setup
dayjs.locale('ru')
const route = useRoute()
const router = useRouter()

// State
const booking = ref<Booking | null>(null)
const loading = ref(true)
const error = ref<string | null>(null)

// Computed
const confirmationCode = route.params.code as string

// Methods
const loadBooking = async () => {
  try {
    loading.value = true
    error.value = null
    
    // Сначала пытаемся загрузить из API
    try {
      const response = await bookingsApi.getByConfirmationCode(confirmationCode)
      booking.value = response.data.data
    } catch (apiError) {
      // Если API недоступно, пытаемся загрузить из localStorage
      const stored = localStorage.getItem(`booking-${confirmationCode}`)
      if (stored) {
        booking.value = JSON.parse(stored)
      } else {
        throw new Error('Заказ не найден')
      }
    }
  } catch (err) {
    console.error('Ошибка загрузки заказа:', err)
    error.value = 'Заказ не найден или произошла ошибка при загрузке'
  } finally {
    loading.value = false
  }
}

const formatDate = (date: string) => {
  return dayjs(date).format('DD MMMM YYYY')
}

const getPersonsText = (count: number) => {
  if (count === 1) return 'человек'
  if (count >= 2 && count <= 4) return 'человека'
  return 'человек'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'Ожидает подтверждения',
    confirmed: 'Подтверждено',
    in_progress: 'Выполняется',
    completed: 'Выполнено',
    cancelled: 'Отменено'
  }
  return statusMap[status] || 'Неизвестный статус'
}

const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(confirmationCode)
    alert('Код скопирован в буфер обмена')
  } catch (err) {
    // Fallback для старых браузеров
    const textArea = document.createElement('textarea')
    textArea.value = confirmationCode
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    alert('Код скопирован в буфер обмена')
  }
}

const shareBooking = async () => {
  if (!booking.value) return
  
  const shareText = `Заказ в отеле "Терем у реки"\n` +
    `Код: ${confirmationCode}\n` +
    `Дата: ${formatDate(booking.value.date)}\n` +
    `Время: ${booking.value.time}\n` +
    `Стоимость: ${booking.value.total_price === 0 ? 'Бесплатно' : `${booking.value.total_price}₽`}`
  
  if (navigator.share) {
    try {
      await navigator.share({
        title: 'Детали заказа',
        text: shareText
      })
    } catch (err) {
      // Пользователь отменил шаринг
    }
  } else {
    // Fallback - копируем в буфер обмена
    try {
      await navigator.clipboard.writeText(shareText)
      alert('Детали заказа скопированы в буфер обмена')
    } catch (err) {
      alert('Не удалось поделиться деталями заказа')
    }
  }
}

// Lifecycle
onMounted(() => {
  if (!confirmationCode) {
    router.push('/')
    return
  }
  loadBooking()
})
</script>