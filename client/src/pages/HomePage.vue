<template>
  <div class="space-y-8">
    <!-- Hero Section -->
    <BaseCard variant="flat" padding="xl" class="hero-section text-center relative overflow-hidden">
      <!-- Background Image -->
      <div 
        v-if="heroSection.background_image"
        class="absolute inset-0 bg-cover bg-center"
        :style="{ backgroundImage: `url(${heroSection.background_image})` }"
        @error="handleImageError"
      ></div>
      
      <!-- Fallback Background -->
      <div 
        v-else
        class="absolute inset-0 bg-gradient-to-br from-primary-500 to-blue-600"
      ></div>
      
      <!-- Gradient Overlay -->
      <div class="absolute inset-0 bg-gradient-to-br from-primary-600/60 to-blue-600/60"></div>
      
      <!-- Content -->
      <div class="relative z-10 text-white">
        <div class="mb-4">
          <div class="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-4">
            <IconStar class="w-8 h-8 text-white" />
          </div>
        </div>
        <h1 class="hero-title">{{ heroSection.title }}</h1>
        <p class="hero-subtitle">{{ heroSection.subtitle }}</p>
        <div 
          v-if="heroSection.show_working_hours"
          class="mt-6 flex items-center justify-center space-x-2 text-white/80"
        >
          <IconClock class="w-4 h-4" />
          <span class="text-sm">{{ heroSection.working_hours_text || 'Работаем круглосуточно' }}</span>
        </div>
      </div>
    </BaseCard>

    <!-- Categories Section -->
    <BaseCard class="mb-6">
      <template #header>
        <div class="flex items-center">
          <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-blue-500 rounded-lg flex items-center justify-center mr-3">
            <IconGrid class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-bold text-gray-900">Категории услуг</h2>
        </div>
      </template>
      
      <!-- Loading State -->
      <SkeletonLoader v-if="loading" variant="category-grid" :count="6" />
      
      <!-- Categories Content -->
      <div v-else class="flex flex-wrap gap-4">
        <BaseCard 
          v-for="category in categories" 
          :key="category.id"
          :width="category.card_width || 'full'"
          clickable
          hover
          class="category-card group relative overflow-hidden cursor-pointer flex-shrink-0"
          @click="() => router.push(`/category/${category.slug}`)"
        >
        <!-- Background Image -->
        <div 
          class="absolute inset-0 bg-cover bg-center opacity-40 group-hover:opacity-50 transition-opacity duration-300"
          :style="{ backgroundImage: `url(${category.image})` }"
        ></div>
        
        <!-- Background Gradient Overlay -->
        <div class="absolute inset-0 bg-gradient-to-br from-white/70 to-primary-50/70 group-hover:from-white/60 group-hover:to-primary-50/60 transition-all duration-300"></div>
        
        <!-- Content -->
        <div class="relative z-10">
          <!-- Category Icon -->
          <div class="flex items-center justify-center category-icon bg-white/95 backdrop-blur-md rounded-2xl mb-6 group-hover:shadow-xl transition-all duration-300 shadow-lg border border-white/50">
            <component 
              :is="getCategoryIcon(category.icon)"
              class="w-8 h-8 text-primary-600"
            />
          </div>
          
          <!-- Category Info -->
          <div class="space-y-3">
            <h3 class="category-title group-hover:text-primary-700 transition-colors text-gray-900 font-semibold drop-shadow-sm">{{ category.name }}</h3>
            <p class="category-description line-clamp-2 text-gray-700 drop-shadow-sm">{{ category.description }}</p>
            
            <!-- Stats Row -->
            <div class="flex items-center justify-between pt-2">
              <!-- Services Count -->
              <div class="flex items-center text-xs text-gray-600 bg-white/80 backdrop-blur-sm px-2 py-1 rounded-full border border-white/30">
                <IconList class="w-3 h-3 mr-1" />
                {{ category.services_count }} {{ getServicesText(category.services_count) }}
              </div>
              
              <!-- Working Hours Status -->
              <div class="flex items-center text-xs px-2 py-1 rounded-full backdrop-blur-sm border border-white/30" :class="isOpenNow(category.working_hours) ? 'text-green-700 bg-green-100/80' : 'text-red-700 bg-red-100/80'">
                <IconClock class="w-3 h-3 mr-1" />
                {{ getWorkingStatus(category.working_hours) }}
              </div>
            </div>
          </div>
          
          <!-- Arrow -->
          <div class="absolute top-6 right-6">
            <IconChevronRight class="w-6 h-6 text-gray-400 group-hover:text-primary-600 group-hover:translate-x-1 transition-all duration-300" />
          </div>
          </div>
        </BaseCard>
      </div>
    </BaseCard>

    <!-- Services Section -->
    <BaseCard class="mb-6">
      <template #header>
        <div class="flex items-center">
          <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mr-3">
            <IconList class="w-5 h-5 text-white" />
          </div>
          <h2 class="text-xl font-bold text-gray-900">Доступные услуги</h2>
        </div>
      </template>
      
      <div class="space-y-4">
        <!-- Loading State -->
        <SkeletonLoader v-if="servicesLoading" variant="service-list" :count="4" />
        
        <div v-else-if="services.length === 0" class="text-center py-8 text-gray-500">
          <p>Услуги не найдены</p>
        </div>
        
        <div v-else class="flex flex-wrap gap-4">
          <BaseCard 
            v-for="service in services" 
            :key="service.id"
            :width="service.card_width || 'half'"
            clickable
            hover
            class="group cursor-pointer flex-shrink-0"
            @click="() => router.push(`/booking?service=${service.id}`)"
          >
          <div class="flex p-3">
            <!-- Service Image -->
            <div v-if="service.image" class="flex-shrink-0 mr-3">
              <img 
                :src="service.image" 
                :alt="service.name"
                class="w-[135px] h-[135px] object-cover rounded-lg"
              />
            </div>
            
            <!-- Service Content -->
            <div class="flex-1 min-w-0 flex flex-col justify-between">
              <div>
                <h3 class="service-title group-hover:text-primary-600 transition-colors">{{ service.name }}</h3>
                <p class="service-description mt-1">{{ service.description }}</p>
                
                <div class="flex flex-wrap items-center gap-2 mt-2">
                  <span class="service-tag">
                    <IconClock class="w-4 h-4 mr-1" />
                    {{ service.duration }} мин
                  </span>
                  
                  <span v-if="service.max_persons > 1" class="service-tag">
                    <IconUsers class="w-4 h-4 mr-1" />
                    до {{ service.max_persons }} чел
                  </span>
                  
                  <span v-if="service.requires_booking" class="service-tag bg-orange-100 text-orange-700">
                    <IconCalendar class="w-4 h-4 mr-1" />
                    Требует бронирования
                  </span>
                </div>
              </div>
              
              <!-- Price and Arrow -->
              <div class="flex items-center justify-between mt-3">
                <div class="service-price">{{ service.price }} ₽</div>
                <IconArrowRight class="w-5 h-5 text-gray-400 group-hover:text-primary-600 transition-colors" />
              </div>
            </div>
          </div>
          </BaseCard>
        </div>
      </div>
    </BaseCard>

    <!-- Quick Actions -->
    <BaseCard>
      <template #header>
        <div class="flex items-center">
          <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-blue-500 rounded-lg flex items-center justify-center mr-3">
            <IconZap class="w-5 h-5 text-white" />
          </div>
          <h3 class="text-xl font-bold text-gray-900">Быстрые действия</h3>
        </div>
      </template>
      
      <div class="grid grid-cols-2 gap-4">
        <button 
          class="quick-action-btn"
          @click="showPhones = true"
        >
          <div class="quick-action-icon bg-gradient-to-br from-green-100 to-emerald-100">
            <IconPhone class="w-6 h-6 text-green-600" />
          </div>
          <span class="quick-action-title">Телефоны</span>
          <span class="quick-action-description">Связаться с нами</span>
        </button>
        
        <button 
          class="quick-action-btn"
          @click="showFeedback = true"
        >
          <div class="quick-action-icon bg-gradient-to-br from-blue-100 to-indigo-100">
            <IconMessage class="w-6 h-6 text-blue-600" />
          </div>
          <span class="quick-action-title">Отзыв</span>
          <span class="quick-action-description">Оставить отзыв</span>
        </button>
      </div>
    </BaseCard>

    <!-- Current Time -->
    <div class="text-center">
      <div class="inline-flex items-center space-x-2 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full shadow-md border border-white/30">
        <IconClock class="w-4 h-4 text-primary-600" />
        <span class="text-sm font-medium text-gray-700">{{ currentTime }}</span>
      </div>
    </div>

    <!-- Modals -->
    <PhoneModal :is-open="showPhones" @close="showPhones = false" />
    <FeedbackModal :is-open="showFeedback" @close="showFeedback = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import 'dayjs/locale/ru'
import { categoriesApi, servicesApi } from '../services/api'
import type { Category, Service, WorkingHours, HeroSection } from '../types'

// Icons
import IconBed from '~icons/lucide/bed'
import IconWaves from '~icons/lucide/waves'
import IconUtensils from '~icons/lucide/utensils'
import IconPlus from '~icons/lucide/plus'
import IconList from '~icons/lucide/list'
import IconClock from '~icons/lucide/clock'
import IconChevronRight from '~icons/lucide/chevron-right'
import IconPhone from '~icons/lucide/phone'
import IconStar from '~icons/lucide/star'
import IconZap from '~icons/lucide/zap'
import IconMessage from '~icons/lucide/message-circle'
import IconUsers from '~icons/lucide/users'
import IconCalendar from '~icons/lucide/calendar'
import IconArrowRight from '~icons/lucide/arrow-right'
import IconGrid from '~icons/lucide/grid-3x3'

// Components
import { BaseCard, SkeletonLoader } from '../components'
import PhoneModal from '../components/PhoneModal.vue'
import FeedbackModal from '../components/FeedbackModal.vue'

// Setup
dayjs.locale('ru')
const router = useRouter()

// State
const categories = ref<Category[]>([])
const services = ref<Service[]>([])
const loading = ref(true)
const servicesLoading = ref(false)
const error = ref('')
const currentTime = ref(dayjs().format('HH:mm'))
const showPhones = ref(false)
const showFeedback = ref(false)

// Hero Section Data (можно загружать с бэкенда)
const heroSection = ref<HeroSection>({
  title: 'Добро пожаловать!',
  subtitle: 'Выберите категорию услуг для бронирования',
  background_image: '', // Можно установить URL изображения
  show_working_hours: true,
  working_hours_text: 'Работаем круглосуточно'
})

// Timer for current time
let timeInterval: number

// Methods
const loadHeroSection = async () => {
  try {
    const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
    const response = await fetch(`${API_BASE_URL}/hero`)
    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data) {
        heroSection.value = result.data
        console.log('Hero section loaded:', result.data)
      }
    }
  } catch (err) {
    console.error('Ошибка загрузки hero-секции:', err)
    // Используем значения по умолчанию
  }
}

const handleImageError = () => {
  console.warn('Ошибка загрузки изображения hero секции')
  heroSection.value.background_image = ''
}

const loadCategories = async () => {
  try {
    loading.value = true
    // Используем getAll вместо getFeatured, так как API поддерживает getAll
    const response = await categoriesApi.getAll()
    categories.value = response.data.data
  } catch (err) {
    error.value = 'Ошибка загрузки категорий'
    console.error(err)
    // Fallback на mock данные уже встроен в categoriesApi.getAll()
  } finally {
    loading.value = false
  }
}

const getCategoryIcon = (iconName?: string) => {
  const icons: Record<string, any> = {
    bed: IconBed,
    waves: IconWaves,
    utensils: IconUtensils,
    plus: IconPlus
  }
  return icons[iconName || 'plus'] || IconPlus
}

const getServicesText = (count: number) => {
  if (count === 1) return 'услуга'
  if (count >= 2 && count <= 4) return 'услуги'
  return 'услуг'
}

const isOpenNow = (workingHours: WorkingHours) => {
  const now = dayjs()
  const currentDay = now.format('dddd').toLowerCase()
  const currentTime = now.format('HH:mm')
  
  const dayMapping: Record<string, keyof WorkingHours> = {
    'понедельник': 'monday',
    'вторник': 'tuesday',
    'среда': 'wednesday',
    'четверг': 'thursday',
    'пятница': 'friday',
    'суббота': 'saturday',
    'воскресенье': 'sunday'
  }
  
  const todayHours = workingHours[dayMapping[currentDay]]
  
  if (!todayHours || todayHours.is_closed) {
    return false
  }
  
  return currentTime >= todayHours.start && currentTime <= todayHours.end
}

const getWorkingStatus = (workingHours: WorkingHours) => {
  if (isOpenNow(workingHours)) {
    return 'Открыто'
  }
  
  const now = dayjs()
  const currentDay = now.format('dddd').toLowerCase()
  
  const dayMapping: Record<string, keyof WorkingHours> = {
    'понедельник': 'monday',
    'вторник': 'tuesday',
    'среда': 'wednesday',
    'четверг': 'thursday',
    'пятница': 'friday',
    'суббота': 'saturday',
    'воскресенье': 'sunday'
  }
  
  const todayHours = workingHours[dayMapping[currentDay]]
  
  if (!todayHours || todayHours.is_closed) {
    return 'Закрыто'
  }
  
  return `Закрыто до ${todayHours.start}`
}

const loadServices = async () => {
  try {
    servicesLoading.value = true
    const response = await servicesApi.getAll()
    services.value = response.data.data
  } catch (err) {
    console.error('Ошибка загрузки услуг:', err)
    services.value = []
  } finally {
    servicesLoading.value = false
  }
}

const updateCurrentTime = () => {
  currentTime.value = dayjs().format('HH:mm')
}

// Lifecycle
onMounted(() => {
  loadHeroSection()
  loadCategories()
  loadServices() // Автоматически загружаем услуги
  timeInterval = setInterval(updateCurrentTime, 60000) // Обновляем каждую минуту
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>