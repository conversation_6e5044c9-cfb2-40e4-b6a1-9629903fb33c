import axios from 'axios'
import type { 
  Category, 
  Service, 
  Booking, 
  AvailabilitySlot, 
  ApiResponse, 
  FeedbackData,
  Contact
} from '../types'

// Базовая конфигурация API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'

const api = axios.create({
  baseURL: `${API_BASE_URL}/api/v1/good`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// Функция для проверки доступности API (закомментирована, так как не используется)
// const isApiAvailable = async (): Promise<boolean> => {
//   try {
//     await api.get('/categories')
//     return true
//   } catch (error) {
//     console.warn('API недоступен')
//     return false
//   }
// }

// Интерсепторы для обработки ошибок
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

// Categories API
export const categoriesApi = {
  getAll: () => api.get<ApiResponse<Category[]>>('/categories'),
  getFeatured: () => api.get<ApiResponse<Category[]>>('/categories/featured'),
  getBySlug: (slug: string) => api.get<ApiResponse<Category>>(`/categories/${slug}`),
  getServices: (slug: string) => api.get<ApiResponse<Service[]>>(`/categories/${slug}/services`)
}

// Services API
export const servicesApi = {
  getAll: () => api.get<ApiResponse<Service[]>>('/services'),
  getFeatured: () => api.get<ApiResponse<Service[]>>('/services/featured'),
  getBySlug: (slug: string) => api.get<ApiResponse<Service>>(`/services/by-slug/${slug}`),
  getById: (id: number) => api.get<ApiResponse<Service>>(`/services/${id}`),
  checkAvailability: (id: number, date: string) => 
    api.get<ApiResponse<boolean>>(`/services/${id}/availability?date=${date}`)
}

// Availability API
export const availabilityApi = {
  getServiceSlots: (serviceId: number, date: string) => 
    api.get<ApiResponse<AvailabilitySlot[]>>(`/availability/services/${serviceId}?date=${date}`),
  getCalendar: (serviceId: number, month: string) => 
    api.get<ApiResponse<any>>(`/availability/services/${serviceId}/calendar?month=${month}`),
  getNextAvailable: (serviceId: number) => 
    api.get<ApiResponse<string>>(`/availability/services/${serviceId}/next-available`),
  bulkCheck: (data: { service_ids: number[], date: string }) => 
    api.post<ApiResponse<any>>('/availability/bulk-check', data)
}

// Bookings API
export const bookingsApi = {
  create: (booking: Booking) => api.post<ApiResponse<Booking>>('/bookings', booking),
  getByCode: (code: string) => api.get<ApiResponse<Booking>>(`/bookings/${code}`),
  getByConfirmationCode: (code: string) => api.get<ApiResponse<Booking>>(`/bookings/${code}`),
  update: (code: string, booking: Partial<Booking>) => 
    api.put<ApiResponse<Booking>>(`/bookings/${code}`, booking),
  cancel: (code: string) => api.post<ApiResponse<void>>(`/bookings/${code}/cancel`),
  getByEmail: (email: string) => 
    api.get<ApiResponse<Booking[]>>(`/bookings/by-email?email=${email}`)
}

// SPA API
export const spaApi = {
  getConfig: () => api.get<ApiResponse<any>>('/spa/config'),
  getTranslations: () => api.get<ApiResponse<any>>('/spa/translations'),
  callComponent: (component: string, method: string, data?: any) => 
    api.post<ApiResponse<any>>(`/spa/component/${component}/${method}`, data)
}



// Feedback API
export const feedbackApi = {
  send: async (feedback: FeedbackData): Promise<boolean> => {
    const response = await api.post('/feedback', feedback)
    return response.data.success
  }
}

// Contacts API
export const contactsApi = {
  async getAll(): Promise<Contact[]> {
    const response = await api.get('/contacts')
    return response.data.data
  },
  
  async getActive(): Promise<Contact[]> {
    const response = await api.get('/contacts')
    return response.data.data
  }
}