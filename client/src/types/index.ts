export interface Category {
  id: number
  name: string
  slug: string
  description: string
  image?: string
  working_hours: WorkingHours
  is_active: boolean
  services_count: number
  icon?: string
  card_width?: 'third' | 'half' | 'full'
  services?: Service[]
}

export interface Service {
  id: number
  name: string
  slug: string
  description: string
  price: number
  duration: number // в минутах
  category_id: number
  is_active: boolean
  is_free: boolean
  requires_booking: boolean
  max_persons: number
  time_slot_step?: number // шаг временного слота в минутах (15, 30, 60)
  image?: string
  card_width?: 'third' | 'half' | 'full'
}

export interface WorkingHours {
  monday: TimeSlot | null
  tuesday: TimeSlot | null
  wednesday: TimeSlot | null
  thursday: TimeSlot | null
  friday: TimeSlot | null
  saturday: TimeSlot | null
  sunday: TimeSlot | null
}

export interface TimeSlot {
  start: string // HH:mm
  end: string // HH:mm
  is_closed?: boolean
}

export interface AvailabilitySlot {
  id: string
  date: string // YYYY-MM-DD
  time: string // HH:mm
  is_available: boolean
}

export interface Contact {
  id: number
  name: string
  description: string
  phone: string
  icon: string
  is_active: boolean
  sort_order: number
  service_id: number
}

export interface Booking {
  id?: number
  code?: string
  confirmation_code?: string
  services: BookingService[]
  date: string // YYYY-MM-DD
  time: string // HH:mm
  persons: number
  total_price: number
  total_duration: number
  guest_name?: string
  guest_phone?: string
  guest_email?: string
  phone?: string
  room_number?: string
  notes?: string
  status: 'pending' | 'confirmed' | 'cancelled'
  created_at?: string
}

export interface BookingService {
  id?: number
  service_id?: number
  service_name?: string
  name?: string
  price: number
  duration: number
  quantity?: number
}

export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  current_page: number
  last_page: number
  per_page: number
  total: number
}

export interface ContactInfo {
  name: string
  phone: string
  description?: string
  is_emergency?: boolean
}

export interface FeedbackData {
  name?: string
  email?: string
  rating: number
  message: string
  room_number?: string
  category?: string
  guest_name?: string
  contact?: string
  anonymous?: boolean
  timestamp?: string
}

export interface HeroSection {
  title: string
  subtitle: string
  background_image?: string
  show_working_hours?: boolean
  working_hours_text?: string
}