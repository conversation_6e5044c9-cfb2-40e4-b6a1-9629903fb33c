<template>
  <div :class="containerClasses">
    <div v-if="type === 'spinner'" :class="spinnerClasses">
      <svg class="animate-spin" fill="none" viewBox="0 0 24 24">
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        />
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </div>
    
    <div v-else-if="type === 'dots'" :class="dotsClasses">
      <div class="dot" />
      <div class="dot" />
      <div class="dot" />
    </div>
    
    <div v-else-if="type === 'pulse'" :class="pulseClasses" />
    
    <div v-else-if="type === 'bars'" :class="barsClasses">
      <div class="bar" />
      <div class="bar" />
      <div class="bar" />
      <div class="bar" />
    </div>
    
    <p v-if="text" :class="textClasses">
      {{ text }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type?: 'spinner' | 'dots' | 'pulse' | 'bars'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'secondary' | 'white' | 'gray'
  text?: string
  overlay?: boolean
  fullscreen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'spinner',
  size: 'md',
  color: 'primary',
  overlay: false,
  fullscreen: false
})

const containerClasses = computed(() => {
  const baseClasses = ['flex', 'flex-col', 'items-center', 'justify-center']
  
  if (props.overlay) {
    baseClasses.push(
      'absolute',
      'inset-0',
      'bg-white',
      'bg-opacity-80',
      'z-50'
    )
  }
  
  if (props.fullscreen) {
    baseClasses.push(
      'fixed',
      'inset-0',
      'bg-white',
      'bg-opacity-90',
      'z-50'
    )
  }
  
  return baseClasses.join(' ')
})

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12'
}

const colorClasses = {
  primary: 'text-primary-600',
  secondary: 'text-gray-600',
  white: 'text-white',
  gray: 'text-gray-400'
}

const spinnerClasses = computed(() => {
  return [
    sizeClasses[props.size],
    colorClasses[props.color]
  ].join(' ')
})

const dotsClasses = computed(() => {
  const baseClasses = ['flex', 'space-x-1']
  return baseClasses.join(' ')
})

const pulseClasses = computed(() => {
  const baseClasses = ['animate-pulse', 'rounded-full']
  const bgClasses = {
    primary: 'bg-primary-600',
    secondary: 'bg-gray-600',
    white: 'bg-white',
    gray: 'bg-gray-400'
  }
  
  return [
    ...baseClasses,
    sizeClasses[props.size],
    bgClasses[props.color]
  ].join(' ')
})

const barsClasses = computed(() => {
  const baseClasses = ['flex', 'space-x-1', 'items-end']
  return baseClasses.join(' ')
})

const textClasses = computed(() => {
  const baseClasses = ['mt-3', 'text-sm', 'font-medium']
  const textColorClasses = {
    primary: 'text-primary-600',
    secondary: 'text-gray-600',
    white: 'text-white',
    gray: 'text-gray-500'
  }
  
  return [
    ...baseClasses,
    textColorClasses[props.color]
  ].join(' ')
})
</script>

<style scoped>
.dot {
  width: 0.5rem;
  height: 0.5rem;
  background-color: currentColor;
  border-radius: 9999px;
  animation: bounce 1s infinite;
}

.dot:nth-child(1) {
  animation-delay: 0ms;
}

.dot:nth-child(2) {
  animation-delay: 150ms;
}

.dot:nth-child(3) {
  animation-delay: 300ms;
}

.bar {
  width: 0.25rem;
  background-color: currentColor;
  border-radius: 9999px;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  height: 1rem;
}

.bar:nth-child(1) {
  animation-delay: 0ms;
  height: 0.5rem;
}

.bar:nth-child(2) {
  animation-delay: 150ms;
  height: 1rem;
}

.bar:nth-child(3) {
  animation-delay: 300ms;
  height: 0.75rem;
}

.bar:nth-child(4) {
  animation-delay: 450ms;
  height: 0.5rem;
}
</style>