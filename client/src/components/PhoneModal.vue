<template>
  <BaseModal 
    :model-value="isOpen"
    @close="closeModal"
    size="md"
    :closable="true"
  >
    <template #header>
      <h3 class="text-lg font-semibold text-gray-900">
        Полезные телефоны
      </h3>
    </template>
        
        <!-- Phone list -->
        <div class="space-y-3">
          <!-- Loading state -->
          <div v-if="isLoading" class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span class="ml-2 text-gray-600">Загрузка контактов...</span>
          </div>
          
          <!-- Error state -->
          <div v-else-if="error" class="text-center py-4">
            <div class="text-red-600 mb-2">{{ error }}</div>
            <BaseButton @click="loadContacts" variant="outline" size="sm">
              Попробовать снова
            </BaseButton>
          </div>
          
          <!-- Contacts list -->
          <div 
            v-else
            v-for="contact in contactsWithIcons" 
            :key="contact.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <div class="flex items-center">
              <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                <component :is="contact.iconComponent" class="w-5 h-5 text-primary-600" />
              </div>
              <div>
                <div class="font-medium text-gray-900">{{ contact.name }}</div>
                <div class="text-sm text-gray-600">{{ contact.description }}</div>
              </div>
            </div>
            <BaseButton 
              @click="callPhone(contact.phone)"
              variant="outline"
              size="sm"
              class="text-primary-600 bg-primary-50 hover:bg-primary-100 flex items-center"
            >
              <IconPhone class="w-4 h-4 mr-1" />
              Позвонить
            </BaseButton>
          </div>
        </div>
        
        <!-- Emergency notice -->
        <div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div class="flex items-start">
            <IconAlertTriangle class="w-5 h-5 text-red-600 mt-0.5 mr-2 flex-shrink-0" />
            <div class="text-sm">
              <div class="font-medium text-red-800">Экстренные службы</div>
              <div class="text-red-700 mt-1">
                При чрезвычайных ситуациях звоните: 
                <BaseButton 
                  @click="callPhone('112')"
                  variant="ghost"
                  size="sm"
                  class="font-semibold underline hover:no-underline p-0 h-auto"
                >
                  112
                </BaseButton>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Working hours -->
        <div class="mt-4 text-xs text-gray-500 text-center">
          Ресепшн работает круглосуточно • Остальные службы: 08:00 - 22:00
        </div>
  </BaseModal>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { BaseModal, BaseButton } from './index'
import { contactsApi } from '../services/api'
import type { Contact } from '../types'

// Icons
import IconPhone from '~icons/lucide/phone'
import IconHeadphones from '~icons/lucide/headphones'
import IconShield from '~icons/lucide/shield'
import IconWrench from '~icons/lucide/wrench'
import IconCar from '~icons/lucide/car'
import IconUtensils from '~icons/lucide/utensils'
import IconSpa from '~icons/lucide/sparkles'
import IconAlertTriangle from '~icons/lucide/alert-triangle'

// Props
interface Props {
  isOpen: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// Reactive data
const contacts = ref<Contact[]>([])
const isLoading = ref(false)
const error = ref<string | null>(null)

// Computed
const isOpen = computed(() => props.isOpen)

// Icon mapping
const iconMap: Record<string, any> = {
  'headphones': IconHeadphones,
  'shield': IconShield,
  'wrench': IconWrench,
  'car': IconCar,
  'utensils': IconUtensils,
  'spa': IconSpa
}

// Computed contacts with icons
const contactsWithIcons = computed(() => 
  contacts.value.map(contact => ({
    ...contact,
    iconComponent: iconMap[contact.icon] || IconHeadphones
  }))
)

// Load contacts from API
const loadContacts = async () => {
  try {
    isLoading.value = true
    error.value = null
    const response = await contactsApi.getActive()
    contacts.value = response
  } catch (err) {
    console.error('Ошибка загрузки контактов:', err)
    error.value = 'Не удалось загрузить контакты'
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadContacts()
})

// Methods
const closeModal = () => {
  emit('close')
}

const callPhone = (phone: string) => {
  window.location.href = `tel:${phone}`
}
</script>