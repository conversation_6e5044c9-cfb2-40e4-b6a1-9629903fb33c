<template>
  <BaseModal 
    :model-value="isOpen"
    @close="closeModal"
    size="md"
    :closable="true"
  >
    <template #header>
      <h3 class="text-lg font-semibold text-gray-900">
        Оставить отзыв
      </h3>
    </template>
        
        <!-- Success message -->
        <div v-if="submitted" class="text-center py-8">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <IconCheck class="w-8 h-8 text-green-600" />
          </div>
          <h4 class="text-lg font-semibold text-gray-900 mb-2">Спасибо за отзыв!</h4>
          <p class="text-gray-600 mb-4">Ваше мнение очень важно для нас</p>
          <BaseButton 
            @click="resetForm"
            variant="primary"
          >
            Оставить еще отзыв
          </BaseButton>
        </div>
        
        <!-- Feedback form -->
        <form v-else @submit.prevent="submitFeedback" class="space-y-4">
          <!-- Rating -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Оценка сервиса
            </label>
            <div class="flex items-center space-x-1">
              <button
                v-for="star in 5"
                :key="star"
                type="button"
                @click="rating = star"
                class="p-1 transition-colors"
                :class="{
                  'text-yellow-400': star <= rating,
                  'text-gray-300 hover:text-yellow-300': star > rating
                }"
              >
                <IconStar 
                  class="w-6 h-6" 
                  :class="{ 'fill-current': star <= rating }"
                />
              </button>
            </div>
            <p class="text-xs text-gray-500 mt-1">{{ getRatingText(rating) }}</p>
          </div>
          
          <!-- Category -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Категория отзыва
            </label>
            <select 
              v-model="category"
              class="input"
              required
            >
              <option value="">Выберите категорию</option>
              <option value="service">Качество обслуживания</option>
              <option value="room">Номер</option>
              <option value="food">Питание</option>
              <option value="spa">SPA услуги</option>
              <option value="staff">Персонал</option>
              <option value="booking">Бронирование услуг</option>
              <option value="other">Другое</option>
            </select>
          </div>
          
          <!-- Guest info -->
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Имя
              </label>
              <input 
                v-model="guestName"
                type="text"
                class="input"
                placeholder="Ваше имя"
                required
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Номер комнаты
              </label>
              <input 
                v-model="roomNumber"
                type="text"
                class="input"
                placeholder="123"
              />
            </div>
          </div>
          
          <!-- Contact -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Email или телефон (необязательно)
            </label>
            <input 
              v-model="contact"
              type="text"
              class="input"
              placeholder="<EMAIL> или +7 (999) 123-45-67"
            />
          </div>
          
          <!-- Message -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Ваш отзыв
            </label>
            <textarea 
              v-model="message"
              class="input"
              rows="4"
              placeholder="Расскажите о вашем опыте пребывания в отеле..."
              required
            ></textarea>
            <p class="text-xs text-gray-500 mt-1">{{ message.length }}/500 символов</p>
          </div>
          
          <!-- Anonymous option -->
          <div class="flex items-center">
            <input 
              v-model="anonymous"
              type="checkbox"
              id="anonymous"
              class="checkbox"
            />
            <label for="anonymous" class="ml-2 text-sm text-gray-700">
              Отправить анонимно
            </label>
          </div>
          
          <!-- Submit button -->
          <div class="flex space-x-3 pt-4">
            <BaseButton 
              @click="closeModal"
              type="button"
              variant="secondary"
              class="flex-1"
            >
              Отмена
            </BaseButton>
            <BaseButton 
              type="submit"
              :disabled="submitting || !canSubmit"
              variant="primary"
              class="flex-1"
              :loading="submitting"
            >
              Отправить отзыв
            </BaseButton>
          </div>
        </form>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { feedbackApi } from '../services/api'
import type { FeedbackData } from '../types'
import { BaseModal, BaseButton } from './index'

// Icons
import IconStar from '~icons/lucide/star'
import IconCheck from '~icons/lucide/check'

// Props
interface Props {
  isOpen: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// Computed
const isOpen = computed(() => props.isOpen)

// State
const rating = ref(5)
const category = ref('')
const guestName = ref('')
const roomNumber = ref('')
const contact = ref('')
const message = ref('')
const anonymous = ref(false)
const submitting = ref(false)
const submitted = ref(false)

// Computed
const canSubmit = computed(() => {
  return rating.value > 0 && 
         category.value && 
         guestName.value.trim() && 
         message.value.trim() && 
         message.value.length <= 500 &&
         !submitting.value
})

// Methods
const closeModal = () => {
  if (!submitting.value) {
    emit('close')
  }
}

const getRatingText = (rating: number) => {
  const texts = {
    1: 'Очень плохо',
    2: 'Плохо', 
    3: 'Удовлетворительно',
    4: 'Хорошо',
    5: 'Отлично'
  }
  return texts[rating as keyof typeof texts] || ''
}

const resetForm = () => {
  rating.value = 5
  category.value = ''
  guestName.value = ''
  roomNumber.value = ''
  contact.value = ''
  message.value = ''
  anonymous.value = false
  submitted.value = false
}

const submitFeedback = async () => {
  if (!canSubmit.value) return
  
  submitting.value = true
  
  try {
    const feedbackData: FeedbackData = {
      rating: rating.value,
      category: category.value,
      guest_name: anonymous.value ? 'Анонимный гость' : guestName.value,
      room_number: roomNumber.value,
      contact: contact.value,
      message: message.value,
      anonymous: anonymous.value,
      timestamp: new Date().toISOString()
    }
    
    try {
      await feedbackApi.send(feedbackData)
    } catch (error) {
      // Fallback - сохраняем локально
      const feedbacks = JSON.parse(localStorage.getItem('feedbacks') || '[]')
      feedbacks.push({ ...feedbackData, id: Date.now() })
      localStorage.setItem('feedbacks', JSON.stringify(feedbacks))
    }
    
    submitted.value = true
  } catch (error) {
    console.error('Ошибка отправки отзыва:', error)
    alert('Произошла ошибка при отправке отзыва. Пожалуйста, попробуйте еще раз.')
  } finally {
    submitting.value = false
  }
}
</script>