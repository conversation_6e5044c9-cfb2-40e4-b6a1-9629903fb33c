<template>
  <button
    :type="type"
    :disabled="disabled || loading"
    :class="buttonClasses"
    @click="$emit('click', $event)"
  >
    <span v-if="loading" class="animate-spin mr-2">
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
      </svg>
    </span>
    <slot />
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  type?: 'button' | 'submit' | 'reset'
  disabled?: boolean
  loading?: boolean
  fullWidth?: boolean
  rounded?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  type: 'button',
  disabled: false,
  loading: false,
  fullWidth: false,
  rounded: false
})

defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClasses = computed(() => {
  const classes = ['btn']

  // Размеры
  const sizeClasses = {
    sm: 'btn-sm',
    md: '', // базовый размер
    lg: 'btn-lg',
    xl: 'btn-lg' // используем lg для xl
  }

  // Варианты стилей
  const variantClasses = {
    primary: 'btn-primary',
    secondary: 'btn-secondary',
    outline: 'btn-secondary', // используем secondary для outline
    ghost: 'btn-secondary', // используем secondary для ghost
    danger: 'btn-accent' // используем accent для danger
  }

  // Добавляем размер
  if (sizeClasses[props.size]) {
    classes.push(sizeClasses[props.size])
  }

  // Добавляем вариант
  classes.push(variantClasses[props.variant])

  // Дополнительные классы
  if (props.fullWidth) {
    classes.push('w-full')
  }

  if (props.rounded) {
    classes.push('rounded-full')
  }

  if (props.disabled || props.loading) {
    classes.push('opacity-50', 'cursor-not-allowed')
  }

  return classes.join(' ')
})
</script>