<template>
  <div class="animate-pulse">
    <!-- Card Skeleton -->
    <div v-if="variant === 'card'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-start space-x-4">
        <div class="w-12 h-12 bg-gray-200 rounded-lg flex-shrink-0"></div>
        <div class="flex-1 space-y-3">
          <div class="h-4 bg-gray-200 rounded w-3/4"></div>
          <div class="h-3 bg-gray-200 rounded w-1/2"></div>
          <div class="h-3 bg-gray-200 rounded w-2/3"></div>
        </div>
      </div>
    </div>

    <!-- Category Grid Skeleton -->
    <div v-else-if="variant === 'category-grid'" class="flex flex-wrap gap-4">
      <div v-for="i in count" :key="i" class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/30 p-6 group relative overflow-hidden cursor-pointer flex-shrink-0 category-card h-[288px]">
        <!-- Background placeholder -->
        <div class="absolute inset-0 bg-gray-100 opacity-20"></div>
        <div class="absolute inset-0 bg-gradient-to-br from-primary-50/80 to-blue-50/80"></div>
        
        <!-- Content -->
        <div class="relative z-10">
          <!-- Category Icon placeholder (48x48px как в реальных блоках) -->
          <div class="flex items-center justify-center w-12 h-12 bg-gray-200 rounded-2xl mb-6 shadow-lg"></div>
          
          <!-- Category Info -->
          <div class="space-y-3">
            <div class="h-5 bg-gray-200 rounded w-3/4"></div>
            <div class="h-3 bg-gray-200 rounded w-full"></div>
            <div class="h-3 bg-gray-200 rounded w-2/3"></div>
            
            <!-- Stats Row -->
            <div class="flex items-center justify-between pt-2">
              <div class="h-6 bg-gray-200 rounded-full w-20"></div>
              <div class="h-6 bg-gray-200 rounded-full w-16"></div>
            </div>
          </div>
        </div>
        
        <!-- Arrow placeholder -->
        <div class="absolute top-6 right-6 w-6 h-6 bg-gray-200 rounded"></div>
      </div>
    </div>

    <!-- Service List Skeleton -->
    <div v-else-if="variant === 'service-list'" class="space-y-4">
      <div v-for="i in count" :key="i" class="bg-white border-2 border-gray-200 rounded-xl p-6 service-card group relative overflow-hidden min-h-[120px] sm:min-h-[140px]">
        <!-- Service Image placeholder (135x135px как в реальных блоках) -->
        <div class="absolute top-2 right-2 w-[135px] h-[135px] bg-gray-200 rounded-xl shadow-lg"></div>
        
        <div class="flex items-start space-x-4">
          <div class="w-5 h-5 bg-gray-200 rounded mt-1 flex-shrink-0"></div>
          
          <div class="flex-1 pr-36">
            <div class="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div class="h-4 bg-gray-200 rounded w-full mb-1"></div>
            <div class="h-4 bg-gray-200 rounded w-2/3 mb-3"></div>
            
            <!-- Tags placeholder -->
            <div class="flex flex-wrap gap-3 mt-4">
              <div class="h-6 bg-gray-200 rounded-full w-16"></div>
              <div class="h-6 bg-gray-200 rounded-full w-20"></div>
              <div class="h-6 bg-gray-200 rounded-full w-24"></div>
            </div>
            
            <!-- Price placeholder -->
            <div class="mt-3">
              <div class="h-6 bg-gray-200 rounded w-20"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Hero Skeleton -->
    <div v-else-if="variant === 'hero'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
      <div class="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-4"></div>
      <div class="h-8 bg-gray-200 rounded w-2/3 mx-auto mb-4"></div>
      <div class="h-4 bg-gray-200 rounded w-1/2 mx-auto mb-6"></div>
      <div class="h-3 bg-gray-200 rounded w-1/3 mx-auto"></div>
    </div>

    <!-- Text Lines Skeleton -->
    <div v-else-if="variant === 'text'" class="space-y-3">
      <div v-for="i in count" :key="i" class="h-4 bg-gray-200 rounded" :class="{
        'w-full': i === 1,
        'w-5/6': i === 2,
        'w-4/6': i === 3,
        'w-3/6': i > 3
      }"></div>
    </div>

    <!-- Button Skeleton -->
    <div v-else-if="variant === 'button'" class="h-10 bg-gray-200 rounded-lg" :class="{
      'w-full': size === 'full',
      'w-32': size === 'medium',
      'w-24': size === 'small'
    }"></div>

    <!-- Default Rectangle Skeleton -->
    <div v-else class="h-4 bg-gray-200 rounded" :class="{
      'w-full': !width,
      [`w-${width}`]: width
    }"></div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  variant?: 'card' | 'category-grid' | 'service-list' | 'hero' | 'text' | 'button' | 'default'
  count?: number
  width?: string
  size?: 'small' | 'medium' | 'full'
}

withDefaults(defineProps<Props>(), {
  variant: 'default',
  count: 3,
  size: 'medium'
})
</script>

<style scoped>
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>