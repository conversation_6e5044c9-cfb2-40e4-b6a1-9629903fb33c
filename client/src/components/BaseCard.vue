<template>
  <div :class="cardClasses">
    <div v-if="$slots.header" :class="headerClasses">
      <slot name="header" />
    </div>
    
    <div :class="bodyClasses">
      <slot />
    </div>
    
    <div v-if="$slots.footer" :class="footerClasses">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  variant?: 'default' | 'outlined' | 'elevated' | 'flat'
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  hover?: boolean
  clickable?: boolean
  width?: 'third' | 'half' | 'full'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  padding: 'md',
  rounded: 'lg',
  shadow: 'sm',
  hover: false,
  clickable: false,
  width: 'full'
})

const cardClasses = computed(() => {
  const baseClasses = ['bg-white', 'transition-all', 'duration-200']

  // Варианты стилей
  const variantClasses = {
    default: ['border', 'border-gray-200'],
    outlined: ['border-2', 'border-gray-300'],
    elevated: [],
    flat: ['border-0']
  }

  // Скругления
  const roundedClasses = {
    none: [],
    sm: ['rounded-sm'],
    md: ['rounded-md'],
    lg: ['rounded-lg'],
    xl: ['rounded-xl'],
    full: ['rounded-full']
  }

  // Тени
  const shadowClasses = {
    none: [],
    sm: ['shadow-sm'],
    md: ['shadow-md'],
    lg: ['shadow-lg'],
    xl: ['shadow-xl']
  }

  const classes = [
    ...baseClasses,
    ...variantClasses[props.variant],
    ...roundedClasses[props.rounded],
    ...shadowClasses[props.shadow]
  ]

  // Эффекты при наведении
  if (props.hover) {
    classes.push('hover:shadow-lg', 'hover:-translate-y-1')
  }

  // Кликабельность
  if (props.clickable) {
    classes.push('cursor-pointer', 'hover:bg-gray-50')
  }

  // Ширина карточки
  const widthClasses = {
    third: ['w-full', 'md:w-1/3'],
    half: ['w-full', 'md:w-1/2'],
    full: ['w-full']
  }
  
  classes.push(...widthClasses[props.width])

  return classes.join(' ')
})

const headerClasses = computed(() => {
  const paddingClasses = {
    none: [],
    sm: ['px-3', 'py-2'],
    md: ['px-4', 'py-3'],
    lg: ['px-6', 'py-4'],
    xl: ['px-8', 'py-6']
  }

  return [
    'border-b',
    'border-gray-200',
    'bg-gray-50',
    ...paddingClasses[props.padding]
  ].join(' ')
})

const bodyClasses = computed(() => {
  const paddingClasses = {
    none: [],
    sm: ['p-3'],
    md: ['p-4'],
    lg: ['p-6'],
    xl: ['p-8']
  }

  return paddingClasses[props.padding].join(' ')
})

const footerClasses = computed(() => {
  const paddingClasses = {
    none: [],
    sm: ['px-3', 'py-2'],
    md: ['px-4', 'py-3'],
    lg: ['px-6', 'py-4'],
    xl: ['px-8', 'py-6']
  }

  return [
    'border-t',
    'border-gray-200',
    'bg-gray-50',
    ...paddingClasses[props.padding]
  ].join(' ')
})
</script>