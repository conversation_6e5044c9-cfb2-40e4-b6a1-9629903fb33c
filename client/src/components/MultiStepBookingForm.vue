<template>
  <div class="space-y-6">
    <!-- Progress Indicator -->
    <BaseCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h1 class="text-xl font-bold text-gray-900">Бронирование услуг</h1>
          <span class="text-sm text-gray-500">Шаг {{ displayStep }} из {{ displayTotalSteps }}</span>
        </div>
      </template>
      
      <!-- Progress Bar -->
      <div class="flex items-center space-x-2">
        <div 
          v-for="step in displayTotalSteps" 
          :key="step"
          class="flex-1 h-2 rounded-full transition-colors duration-300"
          :class="{
            'bg-primary-600': step <= displayStep,
            'bg-gray-200': step > displayStep
          }"
        ></div>
      </div>
      
      <!-- Step Labels -->
      <div class="flex justify-between mt-2 text-xs text-gray-600">
        <span :class="{ 'text-primary-600 font-medium': displayStep >= 1 }">Дата</span>
        <span :class="{ 'text-primary-600 font-medium': displayStep >= 2 }">Время</span>
        <span v-if="shouldShowPersonsStep" :class="{ 'text-primary-600 font-medium': displayStep >= 3 }">Персоны</span>
        <span :class="{ 'text-primary-600 font-medium': displayStep >= (shouldShowPersonsStep ? 4 : 3) }">Контакты</span>
        <span :class="{ 'text-primary-600 font-medium': displayStep >= (shouldShowPersonsStep ? 5 : 4) }">Подтверждение</span>
      </div>
    </BaseCard>

    <!-- Selected Services Summary -->
    <BaseCard class="bg-gray-50">
      <template #header>
        <h3 class="font-medium text-gray-900">Выбранные услуги:</h3>
      </template>
      
      <div class="space-y-1">
        <div 
          v-for="service in bookingData?.selectedServices" 
          :key="service.id"
          class="flex justify-between text-sm"
        >
          <span>{{ service.name }}</span>
          <span>{{ service.is_free ? 'Бесплатно' : `${service.price}₽` }}</span>
        </div>
      </div>
      <div class="border-t border-gray-200 mt-2 pt-2 flex justify-between font-semibold">
        <span>Итого:</span>
        <span>{{ finalPrice === 0 ? 'Бесплатно' : `${finalPrice}₽` }}</span>
      </div>
    </BaseCard>

    <!-- Step 1: Date Selection -->
    <div v-if="currentStep === 1" class="card">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Выберите дату</h2>
      
      <!-- Calendar -->
      <div class="max-w-sm mx-auto lg:max-w-xs">
        <div class="grid grid-cols-7 gap-1 mb-4">
          <!-- Days of week -->
          <div 
            v-for="day in daysOfWeek" 
            :key="day"
            class="text-center text-xs font-medium text-gray-500 py-1 lg:py-1"
          >
            {{ day }}
          </div>
          
          <!-- Calendar Days -->
          <button
            v-for="date in calendarDays"
            :key="date.date"
            @click="selectDate(date)"
            :disabled="!date.isSelectable"
            class="aspect-square flex items-center justify-center text-sm rounded-lg transition-colors lg:text-xs"
            :class="{
              'text-gray-400': !date.isCurrentMonth,
              'text-gray-900': date.isCurrentMonth && date.isSelectable,
              'text-gray-300 cursor-not-allowed': !date.isSelectable,
              'bg-primary-600 text-white': date.isSelected,
              'bg-gray-100 hover:bg-gray-200': date.isCurrentMonth && date.isSelectable && !date.isSelected,
              'bg-red-100 text-red-600': date.isToday
            }"
          >
            {{ date.day }}
          </button>
        </div>
      </div>
      
      <!-- Selected Date Display -->
      <div v-if="formData.selectedDate" class="text-center text-sm text-gray-600 mb-4">
        Выбрана дата: {{ formatDate(formData.selectedDate) }}
      </div>
      
      <!-- Validation Error -->
      <div v-if="errors.date" class="text-red-600 text-sm mb-4">
        {{ errors.date }}
      </div>
    </div>

    <!-- Step 2: Time Selection -->
    <div v-if="currentStep === 2" class="card">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Выберите время</h2>
      
      <div class="mb-4 text-sm text-gray-600">
        Дата: <span class="font-medium">{{ formatDate(formData.selectedDate!) }}</span>
      </div>
      
      <div class="grid grid-cols-3 gap-2">
        <button
          v-for="slot in availableTimeSlots"
          :key="slot.time"
          @click="selectTime(slot.time)"
          :disabled="!slot.available"
          class="py-2 px-3 text-sm rounded-lg border transition-colors"
          :class="{
            'border-primary-600 bg-primary-600 text-white': formData.selectedTime === slot.time,
            'border-gray-300 hover:border-primary-300 hover:bg-primary-50': slot.available && formData.selectedTime !== slot.time,
            'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed': !slot.available
          }"
        >
          {{ slot.time }}
        </button>
      </div>
      
      <div v-if="availableTimeSlots.length === 0" class="text-center py-4 text-gray-500">
        На выбранную дату нет доступных временных слотов
      </div>
      
      <!-- Validation Error -->
      <div v-if="errors.time" class="text-red-600 text-sm mt-4">
        {{ errors.time }}
      </div>
    </div>

    <!-- Step 3: Persons Count -->
    <div v-if="currentStep === 3 && shouldShowPersonsStep" class="card">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Количество персон</h2>
      
      <div class="mb-4 text-sm text-gray-600">
        <div>Дата: <span class="font-medium">{{ formatDate(formData.selectedDate!) }}</span></div>
        <div>Время: <span class="font-medium">{{ formData.selectedTime }}</span></div>
      </div>
      
      <div class="flex items-center space-x-4">
        <button 
          @click="decreasePersons"
          :disabled="formData.persons <= 1"
          class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <IconMinus class="w-4 h-4" />
        </button>
        
        <div class="flex-1 text-center">
          <span class="text-2xl font-semibold">{{ formData.persons }}</span>
          <p class="text-sm text-gray-600">{{ getPersonsText(formData.persons) }}</p>
        </div>
        
        <button 
          @click="increasePersons"
          :disabled="formData.persons >= maxPersons"
          class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <IconPlus class="w-4 h-4" />
        </button>
      </div>
      
      <p class="text-xs text-gray-500 text-center mt-2">
        Максимум {{ maxPersons }} {{ getPersonsText(maxPersons) }}
      </p>
    </div>

    <!-- Step 4: Guest Information -->
    <div v-if="currentStep === 4" class="card">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Контактная информация</h2>
      
      <div class="mb-4 text-sm text-gray-600">
        <div>Дата: <span class="font-medium">{{ formatDate(formData.selectedDate!) }}</span></div>
        <div>Время: <span class="font-medium">{{ formData.selectedTime }}</span></div>
        <div v-if="maxPersons > 1">Персон: <span class="font-medium">{{ formData.persons }}</span></div>
      </div>
      
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Имя *</label>
          <input 
            v-model="formData.guestInfo.name"
            type="text"
            class="input"
            :class="{ 'border-red-500': errors.name }"
            placeholder="Ваше имя"
          />
          <div v-if="errors.name" class="text-red-600 text-sm mt-1">
            {{ errors.name }}
          </div>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Номер комнаты</label>
          <input 
            v-model="formData.guestInfo.roomNumber"
            type="text"
            class="input"
            placeholder="Номер вашей комнаты"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Телефон <span class="text-red-500">*</span></label>
          <input 
            v-model="formData.guestInfo.phone"
            @input="handlePhoneInput"
            type="tel"
            class="input"
            :class="{ 'border-red-500': errors.phone }"
            placeholder="+7 (999) 123-45-67"
            maxlength="18"
          />
          <p v-if="errors.phone" class="text-red-500 text-sm mt-1">{{ errors.phone }}</p>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Примечания (необязательно)</label>
          <textarea 
            v-model="formData.guestInfo.notes"
            class="input"
            rows="3"
            placeholder="Дополнительные пожелания или комментарии"
          ></textarea>
        </div>
      </div>
    </div>

    <!-- Step 5: Confirmation -->
    <div v-if="(currentStep === 5 && shouldShowPersonsStep) || (currentStep === 4 && !shouldShowPersonsStep)" class="card bg-primary-50 border-primary-200">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Подтверждение заказа</h2>
      
      <!-- Services Section -->
      <div class="mb-4">
        <h3 class="font-medium text-gray-900 mb-2">Услуги:</h3>
        <div class="space-y-1">
          <div 
            v-for="service in bookingData?.selectedServices" 
            :key="service.id"
            class="flex justify-between text-sm bg-white rounded-lg p-2 border border-primary-100"
          >
            <div>
              <div class="font-medium">{{ service.name }}</div>
              <div class="text-gray-600">{{ service.duration }} мин</div>
            </div>
            <div class="text-right">
              <div class="font-medium">{{ service.is_free ? 'Бесплатно' : `${service.price}₽` }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="space-y-2 text-sm mb-4">
        <div class="flex justify-between">
          <span>Дата:</span>
          <span class="font-medium">{{ formatDate(formData.selectedDate!) }}</span>
        </div>
        <div class="flex justify-between">
          <span>Время:</span>
          <span class="font-medium">{{ formData.selectedTime }}</span>
        </div>
        <div v-if="maxPersons > 1" class="flex justify-between">
          <span>Количество персон:</span>
          <span class="font-medium">{{ formData.persons }}</span>
        </div>
        <div class="flex justify-between">
          <span>Имя:</span>
          <span class="font-medium">{{ formData.guestInfo.name }}</span>
        </div>
        <div v-if="formData.guestInfo.roomNumber" class="flex justify-between">
          <span>Номер комнаты:</span>
          <span class="font-medium">{{ formData.guestInfo.roomNumber }}</span>
        </div>
        <div v-if="formData.guestInfo.phone" class="flex justify-between">
          <span>Телефон:</span>
          <span class="font-medium">{{ formData.guestInfo.phone }}</span>
        </div>
        <div v-if="formData.guestInfo.notes" class="flex justify-between">
          <span>Примечания:</span>
          <span class="font-medium">{{ formData.guestInfo.notes }}</span>
        </div>
        <div class="flex justify-between">
          <span>Общее время:</span>
          <span class="font-medium">{{ bookingData?.totalDuration }} мин</span>
        </div>
        <div class="flex justify-between font-semibold text-base border-t border-primary-200 pt-2">
          <span>Итоговая стоимость:</span>
          <span>{{ finalPrice === 0 ? 'Бесплатно' : `${finalPrice}₽` }}</span>
        </div>
      </div>
      
      <BaseButton 
        @click="submitBooking"
        :disabled="submitting"
        variant="primary"
        size="lg"
        full-width
        :loading="submitting"
      >
        {{ finalPrice === 0 ? 'Подтвердить бесплатный заказ' : 'Подтвердить бронирование' }}
      </BaseButton>
      
      <p v-if="finalPrice > 0" class="text-xs text-gray-600 text-center mt-2">
        После подтверждения вы получите код бронирования и уведомление поступит администратору
      </p>
    </div>

    <!-- Navigation Buttons -->
    <div class="flex justify-between">
      <BaseButton 
        v-if="currentStep > 1"
        @click="previousStep"
        variant="secondary"
        class="flex items-center"
      >
        <IconArrowLeft class="w-4 h-4 mr-2" />
        <span>Назад</span>
      </BaseButton>
      
      <div v-else></div>
      
      <BaseButton 
        v-if="currentStep < totalSteps"
        @click="nextStep"
        :disabled="!canProceedToNextStep"
        variant="primary"
        class="flex items-center"
      >
        <span>Далее</span>
        <IconArrowRight class="w-4 h-4 ml-2" />
      </BaseButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import 'dayjs/locale/ru'
import { bookingsApi } from '../services/api'
import type { Booking } from '../types'
import { BaseCard, BaseButton } from './index'

// Icons
import IconArrowLeft from '~icons/lucide/arrow-left'
import IconArrowRight from '~icons/lucide/arrow-right'
import IconMinus from '~icons/lucide/minus'
import IconPlus from '~icons/lucide/plus'

// Setup
dayjs.locale('ru')
const router = useRouter()

// Props
interface Props {
  bookingData?: any
}

const props = defineProps<Props>()

// State
const currentStep = ref(1)
const totalSteps = computed(() => shouldShowPersonsStep.value ? 5 : 4)
const submitting = ref(false)
const currentMonth = ref(dayjs())

const formData = ref({
  selectedDate: null as string | null,
  selectedTime: null as string | null,
  persons: 1,
  guestInfo: {
    name: '',
    roomNumber: '',
    phone: '',
    notes: ''
  }
})

const errors = ref({
  date: '',
  time: '',
  name: '',
  phone: ''
})

// Calendar data
const daysOfWeek = ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Вс']

// Computed
const maxPersons = computed(() => {
  if (!props.bookingData?.selectedServices) return 1
  return Math.max(...props.bookingData.selectedServices.map((s: any) => s.max_persons))
})

const finalPrice = computed(() => {
  console.log('finalPrice computed - bookingData:', props.bookingData)
  console.log('finalPrice computed - totalPrice:', props.bookingData?.totalPrice)
  console.log('finalPrice computed - persons:', formData.value.persons)
  
  if (!props.bookingData || !props.bookingData.selectedServices) {
    console.log('finalPrice: no bookingData or selectedServices, returning 0')
    return 0
  }
  
  // Вычисляем цену на основе выбранных услуг, если totalPrice не определен
  let basePrice = props.bookingData.totalPrice
  if (basePrice === undefined || basePrice === null) {
    basePrice = props.bookingData.selectedServices.reduce((sum: number, service: any) => {
      return sum + (service.is_free ? 0 : service.price)
    }, 0)
    console.log('finalPrice: calculated basePrice from services:', basePrice)
  }
  
  const result = basePrice * formData.value.persons
  console.log('finalPrice result:', result)
  return result
})

const calendarDays = computed(() => {
  const startOfMonth = currentMonth.value.startOf('month')
  const endOfMonth = currentMonth.value.endOf('month')
  const startOfCalendar = startOfMonth.startOf('week').add(1, 'day') // Начинаем с понедельника
  const endOfCalendar = endOfMonth.endOf('week').add(1, 'day')
  
  const days = []
  let current = startOfCalendar
  
  while (current.isBefore(endOfCalendar)) {
    const isCurrentMonth = current.month() === currentMonth.value.month()
    const isToday = current.isSame(dayjs(), 'day')
    const isPast = current.isBefore(dayjs(), 'day')
    const isSelected = formData.value.selectedDate === current.format('YYYY-MM-DD')
    
    days.push({
      date: current.format('YYYY-MM-DD'),
      day: current.date(),
      isCurrentMonth,
      isToday,
      isSelectable: isCurrentMonth && !isPast,
      isSelected
    })
    
    current = current.add(1, 'day')
  }
  
  return days
})

const availableTimeSlots = computed(() => {
  if (!formData.value.selectedDate) return []
  
  // Определяем минимальный шаг временного слота из выбранных услуг
  let timeSlotStep = 30 // по умолчанию 30 минут
  if (props.bookingData?.selectedServices && props.bookingData.selectedServices.length > 0) {
    // Используем минимальный шаг из всех выбранных услуг для максимальной гибкости
    const steps = props.bookingData.selectedServices
      .map((service: any) => service.time_slot_step || 30)
      .filter((step: number) => step > 0)
    
    if (steps.length > 0) {
      timeSlotStep = Math.min(...steps)
    }
  }
  
  // Генерируем временные слоты с 9:00 до 21:00 с динамическим интервалом
  const slots = []
  let current = dayjs().hour(9).minute(0)
  const end = dayjs().hour(21).minute(0)
  
  // Проверяем, является ли выбранная дата текущим днем
  const isToday = dayjs(formData.value.selectedDate).isSame(dayjs(), 'day')
  // Получаем текущее время
  const now = dayjs()
  
  while (current.isBefore(end)) {
    // Если сегодня и текущее время больше времени слота, делаем слот недоступным
    const isAvailable = !isToday || current.isAfter(now)
    
    slots.push({
      time: current.format('HH:mm'),
      available: isAvailable // Слот недоступен, если сегодня и время уже прошло
    })
    current = current.add(timeSlotStep, 'minute')
  }
  
  return slots
})

const canProceedToNextStep = computed(() => {
  switch (currentStep.value) {
    case 1:
      return !!formData.value.selectedDate
    case 2:
      return !!formData.value.selectedTime
    case 3:
      return formData.value.persons >= 1 && formData.value.persons <= maxPersons.value
    case 4:
      return !!formData.value.guestInfo.name.trim() && !!formData.value.guestInfo.phone.trim()
    default:
      return false
  }
})

// Methods
const clearErrors = () => {
  errors.value = {
    date: '',
    time: '',
    name: '',
    phone: ''
  }
}

const validateCurrentStep = () => {
  clearErrors()
  
  switch (currentStep.value) {
    case 1:
      if (!formData.value.selectedDate) {
        errors.value.date = 'Пожалуйста, выберите дату'
        return false
      }
      break
    case 2:
      if (!formData.value.selectedTime) {
        errors.value.time = 'Пожалуйста, выберите время'
        return false
      }
      break
    case 3:
      // Проверяем только если шаг с персонами отображается
      if (shouldShowPersonsStep.value) {
        if (formData.value.persons < 1 || formData.value.persons > maxPersons.value) {
          return false
        }
      }
      break
    case 4:
      console.log('validateCurrentStep case 4 - checking name:', formData.value.guestInfo.name)
      if (!formData.value.guestInfo.name.trim()) {
        errors.value.name = 'Пожалуйста, введите ваше имя'
        console.log('validateCurrentStep case 4 - name validation failed')
        return false
      }
      console.log('validateCurrentStep case 4 - checking phone:', formData.value.guestInfo.phone)
      if (!formData.value.guestInfo.phone.trim()) {
        errors.value.phone = 'Пожалуйста, введите номер телефона'
        console.log('validateCurrentStep case 4 - phone validation failed')
        return false
      }
      // Если это финальный шаг (когда shouldShowPersonsStep = false), выполняем полную валидацию
      if (!shouldShowPersonsStep.value) {
        console.log('validateCurrentStep case 4 - checking date and time:', formData.value.selectedDate, formData.value.selectedTime)
        if (!formData.value.selectedDate || !formData.value.selectedTime) {
          console.log('validateCurrentStep case 4 - date/time validation failed')
          return false
        }
      }
      console.log('validateCurrentStep case 4 - validation passed')
      break
    case 5:
      // Финальная проверка перед подтверждением заказа (когда shouldShowPersonsStep = true)
      if (!formData.value.selectedDate || !formData.value.selectedTime || !formData.value.guestInfo.name.trim() || !formData.value.guestInfo.phone.trim()) {
        return false
      }
      if (shouldShowPersonsStep.value && (formData.value.persons < 1 || formData.value.persons > maxPersons.value)) {
        return false
      }
      break
  }
  
  return true
}

const nextStep = () => {
  console.log('nextStep called, current step:', currentStep.value)
  console.log('formData:', formData.value)
  console.log('canProceedToNextStep:', canProceedToNextStep.value)
  
  if (!validateCurrentStep()) {
    console.log('Validation failed')
    return
  }
  
  console.log('Validation passed, proceeding to next step')
  
  if (currentStep.value < totalSteps.value) {
    currentStep.value++
    
    // Пропускаем шаг с количеством персон, если он не нужен
    if (currentStep.value === 3 && !shouldShowPersonsStep.value) {
      currentStep.value++
    }
  }
}

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
    
    // Пропускаем шаг с количеством персон при возврате, если он не нужен
    if (currentStep.value === 3 && !shouldShowPersonsStep.value) {
      currentStep.value--
    }
  }
}

const selectDate = (date: any) => {
  console.log('selectDate called with:', date)
  if (date.isSelectable) {
    formData.value.selectedDate = date.date
    console.log('Selected date set to:', formData.value.selectedDate)
    console.log('canProceedToNextStep:', canProceedToNextStep.value)
    clearErrors()
    // Автоматически переходим к следующему шагу
    nextStep()
  }
}

const selectTime = (time: string) => {
  console.log('selectTime called with:', time)
  formData.value.selectedTime = time
  console.log('Selected time set to:', formData.value.selectedTime)
  console.log('currentStep before nextStep:', currentStep.value)
  clearErrors()
  // Автоматически переходим к следующему шагу
  nextStep()
  console.log('currentStep after nextStep:', currentStep.value)
}

// Маска для российского мобильного телефона
const formatPhoneNumber = (value: string) => {
  // Удаляем все символы кроме цифр
  const cleaned = value.replace(/\D/g, '')
  
  // Если начинается с 8, заменяем на 7
  let phone = cleaned
  if (phone.startsWith('8')) {
    phone = '7' + phone.slice(1)
  }
  
  // Если не начинается с 7, добавляем 7
  if (!phone.startsWith('7')) {
    phone = '7' + phone
  }
  
  // Ограничиваем до 11 цифр
  phone = phone.slice(0, 11)
  
  // Форматируем: +7 (999) 123-45-67
  if (phone.length >= 1) {
    let formatted = '+7'
    if (phone.length > 1) {
      formatted += ' (' + phone.slice(1, 4)
      if (phone.length > 4) {
        formatted += ') ' + phone.slice(4, 7)
        if (phone.length > 7) {
          formatted += '-' + phone.slice(7, 9)
          if (phone.length > 9) {
            formatted += '-' + phone.slice(9, 11)
          }
        }
      } else {
        formatted += phone.slice(4)
      }
    }
    return formatted
  }
  
  return value
}

const handlePhoneInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const formatted = formatPhoneNumber(target.value)
  formData.value.guestInfo.phone = formatted
  target.value = formatted
}

// Функция для получения номера комнаты из куки
const getRoomNumberFromCookie = () => {
  const cookies = document.cookie.split(';')
  for (let cookie of cookies) {
    const [name, value] = cookie.trim().split('=')
    if (name === 'room_number') {
      return value
    }
  }
  return ''
}

const formatDate = (date: string) => {
  return dayjs(date).format('DD MMMM YYYY')
}

const increasePersons = () => {
  if (formData.value.persons < maxPersons.value) {
    formData.value.persons++
  }
}

const decreasePersons = () => {
  if (formData.value.persons > 1) {
    formData.value.persons--
  }
}

const getPersonsText = (count: number) => {
  if (count === 1) return 'человек'
  if (count >= 2 && count <= 4) return 'человека'
  return 'человек'
}

const submitBooking = async () => {
  console.log('submitBooking: начало выполнения')
  console.log('submitBooking: currentStep =', currentStep.value)
  console.log('submitBooking: shouldShowPersonsStep =', shouldShowPersonsStep.value)
  console.log('submitBooking: formData =', formData.value)
  
  if (!validateCurrentStep()) {
    console.log('submitBooking: валидация не прошла')
    return
  }
  
  console.log('submitBooking: валидация прошла успешно')
  submitting.value = true
  
  try {
    const booking: Booking = {
      services: props.bookingData.selectedServices.map((service: any) => ({
        service_id: service.id,
        service_name: service.name,
        price: service.price,
        duration: service.duration,
        quantity: 1
      })),
      date: formData.value.selectedDate!,
      time: formData.value.selectedTime!,
      persons: formData.value.persons,
      total_price: finalPrice.value,
      total_duration: props.bookingData.totalDuration,
      guest_name: formData.value.guestInfo.name,
      guest_phone: formData.value.guestInfo.phone,
      room_number: formData.value.guestInfo.roomNumber,
      notes: formData.value.guestInfo.notes,
      status: 'pending'
    }
    
    console.log('submitBooking: объект booking создан', booking)
    
    // Создаем бронирование для всех услуг (бесплатных и платных)
    try {
      const response = await bookingsApi.create(booking)
      console.log('submitBooking: API запрос успешен', response)
      const confirmationCode = response.data.data.confirmation_code
      console.log('submitBooking: переход на страницу подтверждения через API', confirmationCode)
      router.push(`/confirmation/${confirmationCode}`)
    } catch (error) {
      console.log('submitBooking: ошибка API, используем fallback', error)
      // Fallback - генерируем код локально
      const prefix = finalPrice.value === 0 ? 'FREE-' : 'PAID-'
      const confirmationCode = prefix + Date.now().toString(36).toUpperCase()
      localStorage.setItem(`booking-${confirmationCode}`, JSON.stringify(booking))
      console.log('submitBooking: fallback - переход на страницу подтверждения', confirmationCode)
      router.push(`/confirmation/${confirmationCode}`)
    }
  } catch (error) {
    console.error('submitBooking: критическая ошибка', error)
    alert('Произошла ошибка. Пожалуйста, попробуйте еще раз или обратитесь к администратору.')
  } finally {
    submitting.value = false
  }
}

// Computed для определения нужно ли показывать шаг с персонами
const shouldShowPersonsStep = computed(() => maxPersons.value > 1)

// Computed для правильного отображения номера шага
const displayStep = computed(() => {
  if (!shouldShowPersonsStep.value && currentStep.value > 3) {
    return currentStep.value - 1
  }
  return currentStep.value
})

// Computed для общего количества шагов
const displayTotalSteps = computed(() => {
  return shouldShowPersonsStep.value ? 5 : 4
})

// Lifecycle
onMounted(() => {
  console.log('MultiStepBookingForm mounted')
  console.log('bookingData prop:', props.bookingData)
  console.log('totalPrice:', props.bookingData?.totalPrice)
  console.log('selectedServices:', props.bookingData?.selectedServices)
  
  // Автоматически загружаем номер комнаты из URL или куки
  const roomNumber = getRoomNumberFromCookie()
  if (roomNumber) {
    formData.value.guestInfo.roomNumber = roomNumber
    console.log('Room number loaded from URL/cookie:', roomNumber)
  }
})
</script>