# Hotel Service Booking App - "Терем у реки"

Мобильное веб-приложение для бронирования внутренних услуг отеля "Терем у реки" с категоризацией услуг, множественным выбором подуслуг, календарным планированием и учетом рабочих часов.

## 🚀 Технологический стек

### Frontend
- **Vue.js 3** с Composition API
- **TypeScript** для типобезопасности
- **Tailwind CSS** для стилизации
- **Vue Router** для навигации
- **Axios** для HTTP запросов
- **Day.js** для работы с датами
- **Vite** как сборщик
- **Unplugin Icons** для иконок

## 📱 Основные функции

- ✅ Главная страница с сеткой категорий услуг
- ✅ Отображение рабочих часов для каждой категории
- ✅ Страницы категорий с описанием и списком подуслуг
- ✅ Множественный выбор услуг через чекбоксы
- ✅ Календарь бронирования с выбором даты
- ✅ Выбор времени и количества персон
- ✅ Расчет итоговой стоимости заказа
- ✅ Модальное окно "Полезные телефоны"
- ✅ Форма обратной связи "Отзыв"
- ✅ Кнопка "Связаться с отелем"
- ✅ Отображение статуса работы (рабочие/нерабочие часы)
- ✅ Валидация доступности временных слотов
- ✅ Уведомления о подтверждении заказа

## 🛠 Установка и запуск

### Предварительные требования
- Node.js 16+ 
- npm или yarn

### Установка зависимостей
```bash
npm install
# или
yarn install
```

### Запуск в режиме разработки
```bash
npm run dev
# или
yarn dev
```

Приложение будет доступно по адресу: `http://localhost:5173`

### Сборка для продакшена
```bash
npm run build
# или
yarn build
```

### Предварительный просмотр продакшен сборки
```bash
npm run preview
# или
yarn preview
```

## 📁 Структура проекта

```
src/
├── components/          # Переиспользуемые компоненты
│   ├── FeedbackModal.vue
│   └── PhoneModal.vue
├── pages/              # Страницы приложения
│   ├── HomePage.vue
│   ├── CategoryPage.vue
│   ├── BookingPage.vue
│   └── ConfirmationPage.vue
├── services/           # API сервисы
│   └── api.ts
├── types/              # TypeScript типы
│   └── index.ts
├── App.vue             # Главный компонент
├── main.ts             # Точка входа
└── style.css           # Глобальные стили
```

## 🎨 Дизайн и UI

- **Адаптивный дизайн** оптимизированный для мобильных устройств
- **Современный интерфейс** с использованием Tailwind CSS
- **Интуитивная навигация** с четкой структурой
- **Анимации и переходы** для улучшения UX
- **Темная/светлая тема** (опционально)

## 🔧 API Integration

Приложение готово для интеграции с October CMS backend через REST API endpoints:

- `GET /api/v1/good/categories` - Получить все категории
- `GET /api/v1/good/services` - Получить все услуги
- `POST /api/v1/good/bookings` - Создать бронирование
- И другие endpoints согласно спецификации

## 📱 Мобильная оптимизация

- **PWA готовность** для установки как нативное приложение
- **Touch-friendly интерфейс** с удобными кнопками
- **Быстрая загрузка** благодаря оптимизации Vite
- **Офлайн поддержка** (опционально)

## 🔒 Безопасность

- **Валидация данных** на клиентской стороне
- **Санитизация пользовательского ввода**
- **HTTPS обязательно** для продакшена
- **Rate limiting** готовность для API

## 📞 Контакты и поддержка

Для вопросов по разработке и поддержке обращайтесь к команде разработки.

---

© 2024 Отель "Терем у реки". Все права защищены.