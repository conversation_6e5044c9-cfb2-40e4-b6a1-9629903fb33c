<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation Sidebar - только для авторизованных пользователей -->
    <div v-if="authStore.isAuthenticated" class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0" :class="{ '-translate-x-full': !sidebarOpen }">
      <div class="flex items-center justify-center h-16 px-4 bg-primary-600">
        <h1 class="text-xl font-bold text-white">Админ-панель отеля</h1>
      </div>
      
      <nav class="mt-8">
        <div class="px-4 space-y-2">
          <router-link
            v-for="route in navigationRoutes"
            :key="route.name"
            :to="route.path"
            class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
            :class="[
              $route.name === route.name
                ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
                : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
            ]"
          >
            <component :is="getIcon(route.meta?.icon)" class="w-5 h-5 mr-3" />
            {{ route.meta?.title }}
          </router-link>
        </div>
      </nav>
    </div>

    <!-- Mobile sidebar overlay - только для авторизованных пользователей -->
    <div 
      v-if="authStore.isAuthenticated && sidebarOpen" 
      class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
      @click="sidebarOpen = false"
    ></div>

    <!-- Main Content -->
    <div :class="{ 'lg:pl-64': authStore.isAuthenticated }">
      <!-- Top Header - только для авторизованных пользователей -->
      <header v-if="authStore.isAuthenticated" class="bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center justify-between px-4 py-4 sm:px-6 lg:px-8">
          <div class="flex items-center">
            <button
              @click="sidebarOpen = !sidebarOpen"
              class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 lg:hidden"
            >
              <MenuIcon class="w-6 h-6" />
            </button>
            <h2 class="ml-4 text-2xl font-semibold text-gray-900 lg:ml-0">
              {{ $route.meta?.title || 'Админ-панель' }}
            </h2>
          </div>
          
          <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <button class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full">
              <BellIcon class="w-6 h-6" />
            </button>
            
            <!-- User Menu -->
            <div class="relative">
              <button 
                @click="handleLogout"
                class="flex items-center p-2 text-sm rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
                title="Выйти"
              >
                <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                  <span class="text-white font-medium">А</span>
                </div>
                <span class="ml-2 text-gray-700 hidden sm:block">Администратор</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- Page Content -->
      <main class="flex-1">
        <div class="py-6">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <router-view />
          </div>
        </div>
      </main>
    </div>

    <!-- Global Notifications -->
    <NotificationContainer />
    
    <!-- Global Modals -->
    <ModalContainer />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import NotificationContainer from '@/components/NotificationContainer.vue'
import ModalContainer from '@/components/ModalContainer.vue'

const router = useRouter()
const authStore = useAuthStore()
const sidebarOpen = ref(false)

// Navigation routes
const navigationRoutes = computed(() => {
  return router.getRoutes().filter(route => route.meta?.title && route.path !== '/')
})

// Icon components
const DashboardIcon = () => h('svg', {
  class: 'w-5 h-5',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z'
  }),
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z'
  })
])

const CalendarIcon = () => h('svg', {
  class: 'w-5 h-5',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('rect', {
    x: '3',
    y: '4',
    width: '18',
    height: '18',
    rx: '2',
    ry: '2',
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2'
  }),
  h('line', {
    x1: '16',
    y1: '2',
    x2: '16',
    y2: '6',
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2'
  }),
  h('line', {
    x1: '8',
    y1: '2',
    x2: '8',
    y2: '6',
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2'
  }),
  h('line', {
    x1: '3',
    y1: '10',
    x2: '21',
    y2: '10',
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2'
  })
])

const ServicesIcon = () => h('svg', {
  class: 'w-5 h-5',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10'
  })
])

const UsersIcon = () => h('svg', {
  class: 'w-5 h-5',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z'
  })
])

const ChartIcon = () => h('svg', {
  class: 'w-5 h-5',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
  })
])

const DocumentIcon = () => h('svg', {
  class: 'w-5 h-5',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
  })
])

const MenuIcon = () => h('svg', {
  class: 'w-6 h-6',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M4 6h16M4 12h16M4 18h16'
  })
])

const BellIcon = () => h('svg', {
  class: 'w-6 h-6',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9'
  })
])

// Icon mapping
const iconMap: Record<string, any> = {
  dashboard: DashboardIcon,
  calendar: CalendarIcon,
  services: ServicesIcon,
  users: UsersIcon,
  chart: ChartIcon,
  document: DocumentIcon
}

const getIcon = (iconName?: string) => {
  return iconMap[iconName || 'dashboard'] || DashboardIcon
}

// Функция выхода из системы
const handleLogout = () => {
  authStore.logout()
  router.push('/login')
}
</script>