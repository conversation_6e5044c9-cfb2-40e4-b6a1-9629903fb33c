@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-50 font-sans antialiased;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700;
  }

  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300;
  }

  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700;
  }

  .btn-success {
    @apply btn bg-green-600 text-white hover:bg-green-700;
  }

  .btn-sm {
    @apply h-8 px-3 text-xs;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .select {
    @apply input appearance-none bg-white;
  }

  .textarea {
    @apply input min-h-[80px] resize-none;
  }

  .label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }

  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }

  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }

  .badge-danger {
    @apply badge bg-red-100 text-red-800;
  }

  .badge-info {
    @apply badge bg-blue-100 text-blue-800;
  }

  .modal-overlay {
    @apply fixed inset-0 z-50 bg-black/50 backdrop-blur-sm;
  }

  .modal-content {
    @apply fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white p-6 shadow-lg duration-200 rounded-lg;
  }

  .table {
    @apply w-full caption-bottom text-sm;
  }

  .table-header {
    @apply border-b;
  }

  .table-row {
    @apply border-b transition-colors hover:bg-gray-50;
  }

  .table-head {
    @apply h-12 px-4 text-left align-middle font-medium text-gray-500;
  }

  .table-cell {
    @apply p-4 align-middle;
  }
}

/* FullCalendar customizations */
.fc {
  @apply font-sans;
}

.fc-toolbar-title {
  @apply text-xl font-semibold text-gray-900;
}

.fc-button {
  @apply bg-primary-600 border-primary-600 text-white hover:bg-primary-700 hover:border-primary-700;
}

.fc-button-primary:not(:disabled):active,
.fc-button-primary:not(:disabled).fc-button-active {
  @apply bg-primary-800 border-primary-800;
}

.fc-event {
  @apply border-0 rounded-md shadow-sm;
}

.fc-event-spa {
  @apply bg-spa-500 text-white;
}

.fc-event-food {
  @apply bg-food-500 text-white;
}

.fc-event-fitness {
  @apply bg-fitness-500 text-white;
}

.fc-event-comfort {
  @apply bg-gray-500 text-white;
}

/* Chart.js customizations */
.chart-container {
  @apply relative h-64 w-full;
}

/* Drag and drop styles */
.sortable-ghost {
  @apply opacity-50;
}

.sortable-chosen {
  @apply transform scale-105;
}

.sortable-drag {
  @apply transform rotate-3 shadow-lg;
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
}

/* Transitions */
.fade-enter-active,
.fade-leave-active {
  @apply transition-opacity duration-300;
}

.fade-enter-from,
.fade-leave-to {
  @apply opacity-0;
}

.slide-enter-active,
.slide-leave-active {
  @apply transition-transform duration-300;
}

.slide-enter-from {
  @apply transform translate-x-full;
}

.slide-leave-to {
  @apply transform translate-x-full;
}
