import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { pinia } from '@/stores'
import { useAuthStore } from '@/stores/auth'
import { ApiClient } from '@/utils/api'
import App from './App.vue'
import './style.css'

// Import pages
import Dashboard from '@/pages/Dashboard.vue'
import Calendar from '@/pages/Calendar.vue'
import Services from '@/pages/Services.vue'
import Staff from '@/pages/Staff.vue'
import Analytics from '@/pages/Analytics.vue'
import Reports from '@/pages/Reports.vue'
import Login from '@/pages/Login.vue'

// Router configuration
const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/login',
      name: 'Login',
      component: Login,
      meta: {
        title: 'Авторизация',
        requiresAuth: false
      }
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      component: Dashboard,
      meta: {
        title: 'Панель управления',
        icon: 'dashboard',
        requiresAuth: true
      }
    },
    {
      path: '/calendar',
      name: 'Calendar',
      component: Calendar,
      meta: {
        title: 'Календарь услуг',
        icon: 'calendar',
        requiresAuth: true
      }
    },
    {
      path: '/services',
      name: 'Services',
      component: Services,
      meta: {
        title: 'Управление услугами',
        icon: 'services',
        requiresAuth: true
      }
    },
    {
      path: '/staff',
      name: 'Staff',
      component: Staff,
      meta: {
        title: 'Персонал',
        icon: 'users',
        requiresAuth: true
      }
    },
    {
      path: '/analytics',
      name: 'Analytics',
      component: Analytics,
      meta: {
        title: 'Аналитика',
        icon: 'chart',
        requiresAuth: true
      }
    },
    {
      path: '/reports',
      name: 'Reports',
      component: Reports,
      meta: {
        title: 'Отчеты',
        icon: 'document',
        requiresAuth: true
      }
    }
  ]
})

// Global navigation guard for setting page titles and auth check
router.beforeEach(async (to, _, next) => {
  // Устанавливаем заголовок страницы
  if (to.meta?.title) {
    document.title = `${to.meta.title} - Админ-панель отеля`
  }
  
  // Получаем auth store
  const authStore = useAuthStore()
  
  // Проверка авторизации для защищенных маршрутов
  if (to.meta?.requiresAuth === true) {
    // Если пользователь не авторизован, проверяем токен
    if (!authStore.isAuthenticated) {
      // Пытаемся восстановить сессию из localStorage
      const isValid = await authStore.checkAuth()
      
      if (!isValid) {
        // Токен недействителен или отсутствует - перенаправляем на логин
        console.log('Пользователь не авторизован, перенаправление на /login')
        next({ name: 'Login', query: { redirect: to.fullPath } })
        return
      }
    }
  }
  
  // Если пользователь авторизован и пытается попасть на страницу логина
  if (to.name === 'Login' && authStore.isAuthenticated) {
    // Перенаправляем на главную страницу
    next({ name: 'Dashboard' })
    return
  }
  
  // Если пользователь переходит на корневую страницу
  if (to.path === '/' && authStore.isAuthenticated) {
    next({ name: 'Dashboard' })
    return
  }
  
  // Если пользователь не авторизован и переходит на корневую страницу
  if (to.path === '/' && !authStore.isAuthenticated) {
    next({ name: 'Login' })
    return
  }
  
  next()
})

// Create and mount the app
const app = createApp(App)

// Инициализация API клиента с роутером
ApiClient.init(router)

app.use(pinia)
app.use(router)

// Инициализация авторизации после создания pinia store
const authStore = useAuthStore()
// Вызываем initializeAuth для восстановления сессии из localStorage
authStore.initializeAuth()

app.mount('#app')
