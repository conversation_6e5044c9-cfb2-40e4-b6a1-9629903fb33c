<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Отчеты и аналитика</h1>
          <p class="text-gray-600 mt-1">Генерация отчетов по услугам и бронированиям</p>
        </div>
        <div class="mt-4 sm:mt-0">
          <router-link
            to="/analytics"
            class="btn btn-secondary"
          >
            <ChartIcon class="w-4 h-4 mr-2" />
            Аналитика
          </router-link>
        </div>
      </div>
    </div>

    <!-- Report Generator -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-6">Генератор отчетов</h2>

      <form @submit.prevent="generateReport" class="space-y-6">
        <!-- Date Range -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="label text-gray-700 mb-2">Дата начала</label>
            <input
              v-model="reportConfig.dateRange.start"
              type="date"
              class="input"
              required
            />
          </div>
          <div>
            <label class="label text-gray-700 mb-2">Дата окончания</label>
            <input
              v-model="reportConfig.dateRange.end"
              type="date"
              class="input"
              required
            />
          </div>
        </div>

        <!-- Report Type and Format -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="label text-gray-700 mb-2">Тип отчета</label>
            <select v-model="reportConfig.type" class="select">
              <option value="excel">Excel (CSV)</option>
              <option value="pdf">PDF</option>
            </select>
          </div>
          <div>
            <label class="label text-gray-700 mb-2">Формат</label>
            <select v-model="reportConfig.format" class="select">
              <option value="summary">Краткий</option>
              <option value="detailed">Подробный</option>
            </select>
          </div>
        </div>

        <!-- Categories Filter -->
        <div>
          <label class="label text-gray-700 mb-2">Категории услуг</label>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <label
              v-for="category in availableCategories"
              :key="category.id"
              class="flex items-center space-x-2 cursor-pointer"
            >
              <input
                type="checkbox"
                :value="category.id"
                v-model="selectedCategories"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="text-sm text-gray-700">{{ category.name }}</span>
            </label>
          </div>
        </div>

        <!-- Options -->
        <div class="space-y-3">
          <label class="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              v-model="reportConfig.includeAnalytics"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm text-gray-700">Включить аналитику</span>
          </label>
          <label class="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              v-model="reportConfig.includeCancelled"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm text-gray-700">Включить отмененные бронирования</span>
          </label>
        </div>

        <!-- Generate Button -->
        <div class="flex justify-end">
          <button
            type="submit"
            :disabled="generating"
            class="btn btn-primary"
          >
            <DownloadIcon class="w-4 h-4 mr-2" />
            <span v-if="generating">Генерация...</span>
            <span v-else>Сгенерировать отчет</span>
          </button>
        </div>
      </form>
    </div>

    <!-- Quick Reports -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-6">Быстрые отчеты</h2>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <button
          @click="generateQuickReport('today')"
          :disabled="generating"
          class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
        >
          <div class="flex items-center space-x-3">
            <CalendarIcon class="w-8 h-8 text-blue-600" />
            <div>
              <h3 class="font-medium text-gray-900">Сегодня</h3>
              <p class="text-sm text-gray-500">Отчет за сегодняшний день</p>
            </div>
          </div>
        </button>

        <button
          @click="generateQuickReport('week')"
          :disabled="generating"
          class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
        >
          <div class="flex items-center space-x-3">
            <CalendarIcon class="w-8 h-8 text-green-600" />
            <div>
              <h3 class="font-medium text-gray-900">Эта неделя</h3>
              <p class="text-sm text-gray-500">Отчет за текущую неделю</p>
            </div>
          </div>
        </button>

        <button
          @click="generateQuickReport('month')"
          :disabled="generating"
          class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
        >
          <div class="flex items-center space-x-3">
            <CalendarIcon class="w-8 h-8 text-purple-600" />
            <div>
              <h3 class="font-medium text-gray-900">Этот месяц</h3>
              <p class="text-sm text-gray-500">Отчет за текущий месяц</p>
            </div>
          </div>
        </button>

        <button
          @click="generateQuickReport('spa')"
          :disabled="generating"
          class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
        >
          <div class="flex items-center space-x-3">
            <SpaIcon class="w-8 h-8 text-blue-600" />
            <div>
              <h3 class="font-medium text-gray-900">SPA услуги</h3>
              <p class="text-sm text-gray-500">Отчет по SPA за месяц</p>
            </div>
          </div>
        </button>

        <button
          @click="generateQuickReport('food')"
          :disabled="generating"
          class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
        >
          <div class="flex items-center space-x-3">
            <FoodIcon class="w-8 h-8 text-yellow-600" />
            <div>
              <h3 class="font-medium text-gray-900">Питание</h3>
              <p class="text-sm text-gray-500">Отчет по питанию за месяц</p>
            </div>
          </div>
        </button>

        <button
          @click="generateQuickReport('fitness')"
          :disabled="generating"
          class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
        >
          <div class="flex items-center space-x-3">
            <FitnessIcon class="w-8 h-8 text-green-600" />
            <div>
              <h3 class="font-medium text-gray-900">Фитнес</h3>
              <p class="text-sm text-gray-500">Отчет по фитнесу за месяц</p>
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- Recent Reports -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-6">История отчетов</h2>

      <div v-if="recentReports.length === 0" class="text-center py-8 text-gray-500">
        Нет сгенерированных отчетов
      </div>

      <div v-else class="space-y-3">
        <div
          v-for="report in recentReports"
          :key="report.id"
          class="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <DocumentIcon class="w-6 h-6 text-gray-400" />
            </div>
            <div>
              <h3 class="font-medium text-gray-900">{{ report.name }}</h3>
              <p class="text-sm text-gray-500">
                {{ report.type.toUpperCase() }} • {{ formatDate(report.createdAt) }}
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-500">{{ report.size }}</span>
            <button
              @click="downloadReport(report)"
              class="btn btn-secondary btn-sm"
            >
              <DownloadIcon class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div
      v-if="generating"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
        <div class="spinner w-6 h-6"></div>
        <span class="text-gray-700">Генерация отчета...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useAnalyticsStore, useNotificationStore } from '@/stores'
import type { ReportConfig } from '@/types'

// Stores
const analyticsStore = useAnalyticsStore()
const notificationStore = useNotificationStore()

// State
const generating = ref(false)
const selectedCategories = ref<number[]>([])
const reportConfig = ref<ReportConfig>({
  type: 'excel',
  format: 'detailed',
  dateRange: {
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  },
  categories: [],
  staff: [],
  includeAnalytics: true,
  includeCancelled: true
})

// Mock data for recent reports
const recentReports = ref([
  {
    id: 1,
    name: 'Отчет за декабрь 2024',
    type: 'excel',
    size: '2.3 MB',
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
  },
  {
    id: 2,
    name: 'SPA услуги - ноябрь 2024',
    type: 'pdf',
    size: '1.8 MB',
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
  },
  {
    id: 3,
    name: 'Еженедельный отчет',
    type: 'excel',
    size: '856 KB',
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  }
])

// Computed
const availableCategories = computed(() => [
  { id: 1, name: 'SPA услуги' },
  { id: 2, name: 'Питание' },
  { id: 3, name: 'Фитнес' },
  { id: 4, name: 'Прочие услуги' }
])

// Methods
const generateReport = async () => {
  generating.value = true

  try {
    // Update config with selected categories
    const config = {
      ...reportConfig.value,
      categories: selectedCategories.value.length > 0 ? selectedCategories.value : undefined
    }

    const success = await analyticsStore.generateReport(config)

    if (success) {
      notificationStore.success('Успешно', 'Отчет сгенерирован и загружен')

      // Add to recent reports
      const newReport = {
        id: Date.now(),
        name: `Отчет ${config.dateRange.start} - ${config.dateRange.end}`,
        type: config.type,
        size: config.type === 'excel' ? '1.2 MB' : '890 KB',
        createdAt: new Date()
      }
      recentReports.value.unshift(newReport)

      // Keep only last 10 reports
      if (recentReports.value.length > 10) {
        recentReports.value = recentReports.value.slice(0, 10)
      }
    } else {
      notificationStore.error('Ошибка', 'Не удалось сгенерировать отчет')
    }
  } catch (error) {
    console.error('Error generating report:', error)
    notificationStore.error('Ошибка', 'Произошла ошибка при генерации отчета')
  } finally {
    generating.value = false
  }
}

const generateQuickReport = async (type: string) => {
  generating.value = true

  try {
    let config: ReportConfig
    const today = new Date()

    switch (type) {
      case 'today':
        config = {
          ...reportConfig.value,
          dateRange: {
            start: today.toISOString().split('T')[0],
            end: today.toISOString().split('T')[0]
          }
        }
        break

      case 'week':
        const weekStart = new Date(today)
        weekStart.setDate(today.getDate() - today.getDay() + 1) // Monday
        config = {
          ...reportConfig.value,
          dateRange: {
            start: weekStart.toISOString().split('T')[0],
            end: today.toISOString().split('T')[0]
          }
        }
        break

      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)
        config = {
          ...reportConfig.value,
          dateRange: {
            start: monthStart.toISOString().split('T')[0],
            end: today.toISOString().split('T')[0]
          }
        }
        break

      case 'spa':
        config = {
          ...reportConfig.value,
          categories: [1],
          dateRange: {
            start: new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0],
            end: today.toISOString().split('T')[0]
          }
        }
        break

      case 'food':
        config = {
          ...reportConfig.value,
          categories: [2],
          dateRange: {
            start: new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0],
            end: today.toISOString().split('T')[0]
          }
        }
        break

      case 'fitness':
        config = {
          ...reportConfig.value,
          categories: [3],
          dateRange: {
            start: new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0],
            end: today.toISOString().split('T')[0]
          }
        }
        break

      default:
        return
    }

    const success = await analyticsStore.generateReport(config)

    if (success) {
      notificationStore.success('Успешно', 'Быстрый отчет сгенерирован')
    } else {
      notificationStore.error('Ошибка', 'Не удалось сгенерировать отчет')
    }
  } catch (error) {
    console.error('Error generating quick report:', error)
    notificationStore.error('Ошибка', 'Произошла ошибка при генерации отчета')
  } finally {
    generating.value = false
  }
}

const downloadReport = (report: any) => {
  try {
    // Create a mock download for stored reports
    // In a real app, this would fetch the actual stored report file
    const mockReportContent = `Отчет: ${report.name}
Тип: ${report.type.toUpperCase()}
Размер: ${report.size}
Дата создания: ${formatDate(report.createdAt)}

Это демонстрационный файл отчета.
В реальном приложении здесь был бы фактический контент отчета.`

    const blob = new Blob([mockReportContent], { type: 'text/plain;charset=utf-8' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // Set filename based on report type
    const extension = report.type === 'excel' ? 'xlsx' : 'pdf'
    const filename = `${report.name.replace(/\s+/g, '_')}.${extension}`
    link.download = filename

    // Trigger download
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // Clean up
    window.URL.revokeObjectURL(url)

    notificationStore.success('Успешно', 'Отчет загружен')
  } catch (error) {
    console.error('Error downloading report:', error)
    notificationStore.error('Ошибка', 'Не удалось загрузить отчет')
  }
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('ru-RU', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Icon components
const ChartIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
  })
])

const DownloadIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
  })
])

const CalendarIcon = () => h('svg', {
  class: 'w-8 h-8',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('rect', { x: '3', y: '4', width: '18', height: '18', rx: '2', ry: '2' }),
  h('line', { x1: '16', y1: '2', x2: '16', y2: '6' }),
  h('line', { x1: '8', y1: '2', x2: '8', y2: '6' }),
  h('line', { x1: '3', y1: '10', x2: '21', y2: '10' })
])

const DocumentIcon = () => h('svg', {
  class: 'w-6 h-6',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
  })
])

const SpaIcon = () => h('svg', {
  class: 'w-8 h-8',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z'
  })
])

const FoodIcon = () => h('svg', {
  class: 'w-8 h-8',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01'
  })
])

const FitnessIcon = () => h('svg', {
  class: 'w-8 h-8',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M13 10V3L4 14h7v7l9-11h-7z'
  })
])

// Lifecycle
onMounted(() => {
  // Initialize default date range
  const today = new Date()
  const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)

  reportConfig.value.dateRange = {
    start: thirtyDaysAgo.toISOString().split('T')[0],
    end: today.toISOString().split('T')[0]
  }
})
</script>
