<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Календарь услуг</h1>
          <p class="text-gray-600 mt-1">Управление расписанием и бронированиями</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <!-- View Selection -->
          <div class="flex bg-gray-100 rounded-lg p-1">
            <button
              @click="setCalendarView('day')"
              :class="[
                'px-3 py-1 text-sm font-medium rounded-md transition-colors',
                currentView === 'day' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              ]"
            >
              День
            </button>
            <button
              @click="setCalendarView('week')"
              :class="[
                'px-3 py-1 text-sm font-medium rounded-md transition-colors',
                currentView === 'week' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              ]"
            >
              Неделя
            </button>
            <button
              @click="setCalendarView('month')"
              :class="[
                'px-3 py-1 text-sm font-medium rounded-md transition-colors',
                currentView === 'month' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              ]"
            >
              Месяц
            </button>
          </div>
          <button
            @click="refreshBookings"
            :disabled="loading"
            class="btn btn-secondary"
          >
            <RefreshIcon class="w-4 h-4 mr-2" />
            Обновить
          </button>
          <button
            @click="openReportGenerator"
            class="btn btn-primary"
          >
            <DocumentIcon class="w-4 h-4 mr-2" />
            Отчет
          </button>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="label text-gray-700 mb-2">Начальная дата</label>
          <input
            v-model="startDate"
            type="date"
            class="input"
            @change="loadBookings"
          />
        </div>
        <div>
          <label class="label text-gray-700 mb-2">Категория услуг</label>
          <select
            v-model="selectedCategory"
            class="select"
            @change="loadBookings"
          >
            <option
              v-for="category in categories"
              :key="category.id"
              :value="category.id"
            >
              {{ category.name }}
            </option>
          </select>
        </div>
        <div>
          <label class="label text-gray-700 mb-2">Статус</label>
          <select
            v-model="selectedStatus"
            class="select"
            @change="loadBookings"
          >
            <option value="">Все статусы</option>
            <option value="confirmed">Подтверждено</option>
            <option value="pending">Ожидает</option>
            <option value="cancelled">Отменено</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Calendar Grid -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h2 class="text-lg font-semibold text-gray-900">Расписание</h2>
            <!-- Navigation Controls -->
            <div class="flex items-center space-x-2">
              <button
                @click="navigatePrevious"
                class="p-1 rounded-md hover:bg-gray-100 transition-colors"
                title="Предыдущий период"
              >
                <ChevronLeftIcon class="w-5 h-5 text-gray-600" />
              </button>
              <button
                @click="navigateToday"
                class="px-3 py-1 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
              >
                Сегодня
              </button>
              <button
                @click="navigateNext"
                class="p-1 rounded-md hover:bg-gray-100 transition-colors"
                title="Следующий период"
              >
                <ChevronRightIcon class="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>
          <div class="text-sm text-gray-600">
            {{ formatDateRange() }}
          </div>
        </div>
      </div>

      <div class="overflow-auto max-h-[70vh]">
        <table class="w-full border-collapse min-w-[800px]">
          <thead class="bg-gray-50 sticky top-0 z-20">
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky left-0 bg-gray-50 z-30 border-r border-gray-200 min-w-[100px]">
                Время
              </th>
              <th
                v-for="date in dateRange"
                :key="date"
                class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[150px] border-r border-gray-200"
              >
                <div class="text-sm">{{ formatDateHeader(date) }}</div>
                <div class="text-xs text-gray-400 font-normal">{{ formatDayOfWeek(date) }}</div>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white">
            <tr
              v-for="time in timeSlots"
              :key="time"
              class="border-b border-gray-100 hover:bg-gray-50"
            >
              <td class="px-4 py-3 whitespace-nowrap sticky left-0 bg-gray-100 z-10 border-r border-gray-200 font-medium text-sm text-gray-900">
                {{ time }}
              </td>
              <td
                v-for="date in dateRange"
                :key="`${time}-${date}`"
                class="px-2 py-3 text-center relative border-r border-gray-100 min-h-[60px] cursor-pointer hover:bg-gray-50 transition-colors"
                @drop="handleDrop($event, date, time)"
                @dragover.prevent
                @dragenter.prevent
                @click="handleCellClick(date, time)"
              >
                <div class="min-h-[40px] flex flex-col space-y-1">
                  <div
                    v-for="booking in getBookingsForSlot(date, time)"
                    :key="booking.id"
                    class="booking-card p-2 rounded text-xs cursor-move transition-all hover:shadow-md relative group"
                    :class="[
                      getBookingStatusClass(booking.status),
                      getServiceTypeColor(booking)
                    ]"
                    @dragstart="handleDragStart($event, booking)"
                    @click.stop="showBookingDetails(booking)"
                    draggable="true"
                  >
                    <div class="font-medium truncate">{{ booking.guest_name }}</div>
                    <div class="text-xs opacity-90 truncate">{{ getBookingServiceNames(booking) }}</div>
                    <div class="text-xs opacity-75">{{ booking.room_number }}</div>

                    <!-- Action buttons -->
                    <div class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                      <button
                        @click.stop="editBooking(booking)"
                        class="w-5 h-5 bg-blue-100 hover:bg-blue-200 text-blue-600 rounded-full flex items-center justify-center"
                        title="Редактировать"
                      >
                        <EditIcon class="w-3 h-3" />
                      </button>
                      <button
                        @click.stop="cancelBooking(booking)"
                        class="w-5 h-5 bg-red-100 hover:bg-red-200 text-red-600 rounded-full flex items-center justify-center"
                        title="Отменить"
                      >
                        <XIcon class="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div
      v-if="loading"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
        <div class="spinner w-6 h-6"></div>
        <span class="text-gray-700">Загрузка...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useBookingStore, useModalStore, useNotificationStore } from '@/stores'
import type { Booking } from '@/types'

// Stores
const bookingStore = useBookingStore()
const modalStore = useModalStore()
const notificationStore = useNotificationStore()

// State
const loading = ref(false)
const startDate = ref(new Date().toISOString().split('T')[0])
const selectedCategory = ref(0)
const selectedStatus = ref('')
const draggedBooking = ref<Booking | null>(null)
const currentView = ref<'day' | 'week' | 'month'>('week')

// Computed
const categories = computed(() => {
  return [
    { id: 0, name: 'Все категории' },
    { id: 1, name: 'SPA услуги' },
    { id: 2, name: 'Питание' },
    { id: 3, name: 'Фитнес' },
    { id: 4, name: 'Прочие услуги' }
  ]
})

const dateRange = computed(() => {
  const dates = []
  const start = new Date(startDate.value)

  let daysToShow = 7 // default week view

  if (currentView.value === 'day') {
    daysToShow = 1
  } else if (currentView.value === 'week') {
    daysToShow = 7
  } else if (currentView.value === 'month') {
    // For month view, show from start of month to end of month
    const startOfMonth = new Date(start.getFullYear(), start.getMonth(), 1)
    const endOfMonth = new Date(start.getFullYear(), start.getMonth() + 1, 0)

    for (let d = new Date(startOfMonth); d <= endOfMonth; d.setDate(d.getDate() + 1)) {
      dates.push(d.toISOString().split('T')[0])
    }
    return dates
  }

  for (let i = 0; i < daysToShow; i++) {
    const date = new Date(start)
    date.setDate(start.getDate() + i)
    dates.push(date.toISOString().split('T')[0])
  }

  return dates
})

const timeSlots = computed(() => {
  const slots = []
  for (let hour = 9; hour <= 21; hour++) {
    slots.push(`${hour.toString().padStart(2, '0')}:00`)
    if (hour < 21) {
      slots.push(`${hour.toString().padStart(2, '0')}:30`)
    }
  }
  return slots
})

// Methods
const formatDateRange = () => {
  const start = new Date(startDate.value)

  if (currentView.value === 'day') {
    return start.toLocaleDateString('ru-RU', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
  } else if (currentView.value === 'week') {
    const end = new Date(start)
    end.setDate(start.getDate() + 6)
    return `${start.toLocaleDateString('ru-RU')} - ${end.toLocaleDateString('ru-RU')}`
  } else if (currentView.value === 'month') {
    return start.toLocaleDateString('ru-RU', { 
      year: 'numeric', 
      month: 'long' 
    })
  }

  return start.toLocaleDateString('ru-RU')
}

const formatDateHeader = (date: string) => {
  return new Date(date).toLocaleDateString('ru-RU', {
    day: '2-digit',
    month: '2-digit'
  })
}

const formatDayOfWeek = (date: string) => {
  return new Date(date).toLocaleDateString('ru-RU', {
    weekday: 'short'
  })
}

const getBookingsForSlot = (date: string, time: string) => {
  return bookingStore.getBookingsForSlot(date, time)
}

const getBookingStatusClass = (status: string) => {
  switch (status) {
    case 'confirmed':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'cancelled':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getServiceTypeColor = (booking: Booking) => {
  if (!booking.services || booking.services.length === 0) {
    return 'bg-gray-100'
  }

  const serviceType = booking.services[0].service_name || booking.services[0].name || ''

  if (serviceType.includes('Массаж') || serviceType.includes('СПА') || serviceType.includes('Сауна')) {
    return 'bg-blue-100'
  } else if (serviceType.includes('Завтрак') || serviceType.includes('Обед') || serviceType.includes('Ужин')) {
    return 'bg-yellow-100'
  } else if (serviceType.includes('Фитнес') || serviceType.includes('Тренировка')) {
    return 'bg-green-100'
  }

  return 'bg-gray-100'
}

const getBookingServiceNames = (booking: Booking) => {
  return booking.services.map(s => s.service_name || s.name).join(', ')
}

const loadBookings = async () => {
  loading.value = true

  try {
    // Update filters
    bookingStore.updateFilters({
      dateRange: {
        start: startDate.value,
        end: dateRange.value[dateRange.value.length - 1]
      },
      category: selectedCategory.value || undefined,
      status: selectedStatus.value || undefined
    })

    // Fetch bookings
    await bookingStore.fetchBookings({
      start: startDate.value,
      end: dateRange.value[dateRange.value.length - 1]
    })
  } catch (error) {
    console.error('Error loading bookings:', error)
    notificationStore.error('Ошибка', 'Не удалось загрузить бронирования')
  } finally {
    loading.value = false
  }
}

const refreshBookings = () => {
  loadBookings()
}

const handleDragStart = (event: DragEvent, booking: Booking) => {
  draggedBooking.value = booking
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', booking.id.toString())
  }
}

const handleDrop = async (event: DragEvent, newDate: string, newTime: string) => {
  event.preventDefault()

  if (!draggedBooking.value) return

  const booking = draggedBooking.value
  const oldDate = booking.date
  const oldTime = booking.time

  if (oldDate === newDate && oldTime === newTime) {
    draggedBooking.value = null
    return
  }

  try {
    const success = await bookingStore.rescheduleBooking(booking.id, newDate, newTime)

    if (success) {
      notificationStore.success('Успешно', 'Бронирование перенесено')
      await loadBookings() // Refresh data
    } else {
      notificationStore.error('Ошибка', 'Не удалось перенести бронирование')
    }
  } catch (error) {
    console.error('Error rescheduling booking:', error)
    notificationStore.error('Ошибка', 'Произошла ошибка при переносе бронирования')
  }

  draggedBooking.value = null
}

const handleCellClick = (date: string, time: string) => {
  modalStore.openBookingForm(date, time)
}

const showBookingDetails = (booking: Booking) => {
  modalStore.openBookingDetails(booking.id)
}

const editBooking = (booking: Booking) => {
  modalStore.openBookingReschedule(booking.id)
}

const cancelBooking = async (booking: Booking) => {
  modalStore.openBookingCancel(booking.id)
}

const openReportGenerator = () => {
  modalStore.openReportGenerator()
}

// Calendar view and navigation methods
const setCalendarView = (view: 'day' | 'week' | 'month') => {
  currentView.value = view
  loadBookings()
}

const navigatePrevious = () => {
  const current = new Date(startDate.value)

  if (currentView.value === 'day') {
    current.setDate(current.getDate() - 1)
  } else if (currentView.value === 'week') {
    current.setDate(current.getDate() - 7)
  } else if (currentView.value === 'month') {
    current.setMonth(current.getMonth() - 1)
  }

  startDate.value = current.toISOString().split('T')[0]
  loadBookings()
}

const navigateNext = () => {
  const current = new Date(startDate.value)

  if (currentView.value === 'day') {
    current.setDate(current.getDate() + 1)
  } else if (currentView.value === 'week') {
    current.setDate(current.getDate() + 7)
  } else if (currentView.value === 'month') {
    current.setMonth(current.getMonth() + 1)
  }

  startDate.value = current.toISOString().split('T')[0]
  loadBookings()
}

const navigateToday = () => {
  startDate.value = new Date().toISOString().split('T')[0]
  loadBookings()
}

// Icon components
const RefreshIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
  })
])

const DocumentIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
  })
])

const EditIcon = () => h('svg', {
  class: 'w-3 h-3',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z'
  })
])

const XIcon = () => h('svg', {
  class: 'w-3 h-3',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M6 18L18 6M6 6l12 12'
  })
])

const ChevronLeftIcon = () => h('svg', {
  class: 'w-5 h-5',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M15 19l-7-7 7-7'
  })
])

const ChevronRightIcon = () => h('svg', {
  class: 'w-5 h-5',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M9 5l7 7-7 7'
  })
])

// Lifecycle
onMounted(() => {
  loadBookings()
})
</script>

<style scoped>
.booking-card {
  border: 1px solid;
  min-height: 50px;
}

.booking-card:hover {
  transform: translateY(-1px);
}

.booking-card.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}
</style>
