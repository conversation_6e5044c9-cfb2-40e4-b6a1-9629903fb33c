<template>
  <div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Добро пожаловать в админ-панель</h1>
          <p class="text-gray-600 mt-1">Управление услугами отеля "Терем у реки"</p>
        </div>
        <div class="text-right">
          <p class="text-sm text-gray-500">Сегодня</p>
          <p class="text-lg font-semibold text-gray-900">{{ currentDate }}</p>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <CalendarIcon class="w-8 h-8 text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Бронирований сегодня</p>
            <p class="text-2xl font-bold text-gray-900">{{ todayBookings }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <CurrencyIcon class="w-8 h-8 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Выручка сегодня</p>
            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(todayRevenue) }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <UsersIcon class="w-8 h-8 text-purple-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Активных сотрудников</p>
            <p class="text-2xl font-bold text-gray-900">{{ activeStaffCount }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <ChartIcon class="w-8 h-8 text-orange-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Загрузка</p>
            <p class="text-2xl font-bold text-gray-900">{{ occupancyRate }}%</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity & Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Bookings -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Последние бронирования</h3>
        </div>
        <div class="p-6">
          <div v-if="loading" class="text-center py-4">
            <div class="spinner w-8 h-8 mx-auto"></div>
            <p class="text-gray-500 mt-2">Загрузка...</p>
          </div>
          <div v-else-if="recentBookings.length === 0" class="text-center py-8 text-gray-500">
            Нет недавних бронирований
          </div>
          <div v-else class="space-y-4">
            <div 
              v-for="booking in recentBookings" 
              :key="booking.id"
              class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
            >
              <div class="flex-1">
                <p class="font-medium text-gray-900">{{ booking.guest_name }}</p>
                <p class="text-sm text-gray-500">
                  {{ booking.services[0]?.name }} • {{ booking.date }} в {{ booking.time }}
                </p>
              </div>
              <div class="text-right">
                <p class="font-medium text-gray-900">{{ formatCurrency(booking.total_price) }}</p>
                <span 
                  class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                  :class="getStatusClass(booking.status)"
                >
                  {{ getStatusText(booking.status) }}
                </span>
              </div>
            </div>
          </div>
          <div class="mt-4 text-center">
            <router-link 
              to="/calendar" 
              class="text-blue-600 hover:text-blue-700 font-medium text-sm"
            >
              Посмотреть все бронирования →
            </router-link>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Быстрые действия</h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-2 gap-4">
            <router-link
              to="/calendar"
              class="flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
            >
              <CalendarIcon class="w-8 h-8 text-blue-600 mb-2" />
              <span class="text-sm font-medium text-blue-900">Календарь</span>
            </router-link>

            <router-link
              to="/services"
              class="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
            >
              <ServicesIcon class="w-8 h-8 text-green-600 mb-2" />
              <span class="text-sm font-medium text-green-900">Услуги</span>
            </router-link>

            <router-link
              to="/staff"
              class="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
            >
              <UsersIcon class="w-8 h-8 text-purple-600 mb-2" />
              <span class="text-sm font-medium text-purple-900">Персонал</span>
            </router-link>

            <router-link
              to="/reports"
              class="flex flex-col items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors"
            >
              <DocumentIcon class="w-8 h-8 text-orange-600 mb-2" />
              <span class="text-sm font-medium text-orange-900">Отчеты</span>
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Today's Schedule Preview -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">Расписание на сегодня</h3>
          <router-link 
            to="/calendar" 
            class="text-blue-600 hover:text-blue-700 font-medium text-sm"
          >
            Открыть календарь
          </router-link>
        </div>
      </div>
      <div class="p-6">
        <div v-if="loading" class="text-center py-4">
          <div class="spinner w-8 h-8 mx-auto"></div>
          <p class="text-gray-500 mt-2">Загрузка расписания...</p>
        </div>
        <div v-else-if="todaySchedule.length === 0" class="text-center py-8 text-gray-500">
          На сегодня нет запланированных услуг
        </div>
        <div v-else class="space-y-3">
          <div 
            v-for="booking in todaySchedule" 
            :key="booking.id"
            class="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
          >
            <div class="flex items-center space-x-4">
              <div class="text-center">
                <p class="text-sm font-medium text-gray-900">{{ booking.time }}</p>
                <p class="text-xs text-gray-500">{{ booking.total_duration }}мин</p>
              </div>
              <div>
                <p class="font-medium text-gray-900">{{ booking.guest_name }}</p>
                <p class="text-sm text-gray-500">{{ booking.services[0]?.name }}</p>
                <p class="text-xs text-gray-400">Номер {{ booking.room_number }}</p>
              </div>
            </div>
            <div class="text-right">
              <p class="font-medium text-gray-900">{{ formatCurrency(booking.total_price) }}</p>
              <span 
                class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                :class="getStatusClass(booking.status)"
              >
                {{ getStatusText(booking.status) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useBookingStore, useStaffStore } from '@/stores'
import type { Booking } from '@/types'

// Stores
const bookingStore = useBookingStore()
const staffStore = useStaffStore()

// State
const loading = ref(false)
const recentBookings = ref<Booking[]>([])
const todaySchedule = ref<Booking[]>([])

// Computed
const currentDate = computed(() => {
  return new Date().toLocaleDateString('ru-RU', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

const todayBookings = computed(() => {
  return todaySchedule.value.length
})

const todayRevenue = computed(() => {
  return todaySchedule.value.reduce((sum, booking) => sum + booking.total_price, 0)
})

const activeStaffCount = computed(() => {
  return staffStore.activeStaff.length || 3 // Fallback to mock data
})

const occupancyRate = computed(() => {
  // Mock calculation - in real app, calculate based on actual capacity
  const totalSlots = 48 // 8 hours * 6 slots per hour
  const bookedSlots = todaySchedule.value.length
  return Math.round((bookedSlots / totalSlots) * 100)
})

// Methods
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('ru-RU', {
    style: 'currency',
    currency: 'RUB',
    minimumFractionDigits: 0
  }).format(amount)
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'confirmed':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'cancelled':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'confirmed':
      return 'Подтверждено'
    case 'pending':
      return 'Ожидает'
    case 'cancelled':
      return 'Отменено'
    default:
      return 'Неизвестно'
  }
}

const loadDashboardData = async () => {
  loading.value = true
  
  try {
    // Load today's bookings
    const today = new Date().toISOString().split('T')[0]
    
    // Сбрасываем все фильтры и устанавливаем диапазон дат
    bookingStore.clearFilters()
    bookingStore.updateFilters({ dateRange: { start: today, end: today } })
    
    // Загружаем бронирования для сегодняшнего дня
    await bookingStore.fetchBookings({ start: today, end: today })
    
    console.log('Dashboard: filteredBookings:', bookingStore.filteredBookings)
    
    todaySchedule.value = bookingStore.filteredBookings.slice().sort((a, b) => a.time.localeCompare(b.time))
    recentBookings.value = bookingStore.filteredBookings.slice(0, 5)
    
    // Initialize staff store
    staffStore.initializeMockStaff()
  } catch (error) {
    console.error('Error loading dashboard data:', error)
  } finally {
    loading.value = false
  }
}

// Icon components
const CalendarIcon = () => h('svg', {
  class: 'w-8 h-8',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('rect', { x: '3', y: '4', width: '18', height: '18', rx: '2', ry: '2' }),
  h('line', { x1: '16', y1: '2', x2: '16', y2: '6' }),
  h('line', { x1: '8', y1: '2', x2: '8', y2: '6' }),
  h('line', { x1: '3', y1: '10', x2: '21', y2: '10' })
])

const CurrencyIcon = () => h('svg', {
  class: 'w-8 h-8',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('line', { x1: '12', y1: '1', x2: '12', y2: '23' }),
  h('path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6' })
])

const UsersIcon = () => h('svg', {
  class: 'w-8 h-8',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', { d: 'M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2' }),
  h('circle', { cx: '9', cy: '7', r: '4' }),
  h('path', { d: 'M23 21v-2a4 4 0 0 0-3-3.87' }),
  h('path', { d: 'M16 3.13a4 4 0 0 1 0 7.75' })
])

const ChartIcon = () => h('svg', {
  class: 'w-8 h-8',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', { d: 'M9 19v-6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2zm0 0V9a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v10m-6 0a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2m0 0V5a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2z' })
])

const ServicesIcon = () => h('svg', {
  class: 'w-8 h-8',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', { d: 'M19 11H5m14 0a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-6a2 2 0 0 1 2-2m14 0V9a2 2 0 0 0-2-2M5 11V9a2 2 0 0 1 2-2m0 0V5a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2M7 7h10' })
])

const DocumentIcon = () => h('svg', {
  class: 'w-8 h-8',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', { d: 'M9 12h6m-6 4h6m2 5H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5.586a1 1 0 0 1 .707.293l5.414 5.414a1 1 0 0 1 .293.707V19a2 2 0 0 1-2 2z' })
])

// Lifecycle
onMounted(() => {
  loadDashboardData()
})
</script>