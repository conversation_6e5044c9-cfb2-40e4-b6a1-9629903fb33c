<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Управление персоналом</h1>
          <p class="text-gray-600 mt-1">Управление сотрудниками и их расписанием</p>
        </div>
        <div class="mt-4 sm:mt-0">
          <button
            @click="openStaffForm"
            class="btn btn-primary"
          >
            <PlusIcon class="w-4 h-4 mr-2" />
            Добавить сотрудника
          </button>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Поиск сотрудников..."
            class="input"
          />
        </div>
        <div class="w-full sm:w-48">
          <select v-model="departmentFilter" class="select">
            <option value="">Все отделы</option>
            <option
              v-for="department in departments"
              :key="department"
              :value="department"
            >
              {{ department }}
            </option>
          </select>
        </div>
        <div class="w-full sm:w-32">
          <select v-model="statusFilter" class="select">
            <option value="">Все статусы</option>
            <option value="active">Активные</option>
            <option value="inactive">Неактивные</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Staff Cards -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-6">Сотрудники</h2>
      
      <div v-if="loading" class="flex justify-center py-8">
        <div class="spinner w-8 h-8"></div>
      </div>
      
      <div v-else-if="filteredStaff.length === 0" class="text-center py-8 text-gray-500">
        Нет сотрудников, соответствующих критериям поиска
      </div>
      
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div
          v-for="member in paginatedStaff"
          :key="member.id"
          class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
        >
          <!-- Staff Header -->
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center">
                <span class="text-white font-medium text-lg">
                  {{ getInitials(member.name) }}
                </span>
              </div>
              <div>
                <h3 class="font-medium text-gray-900">{{ member.name }}</h3>
                <p class="text-sm text-gray-500">{{ member.position }}</p>
              </div>
            </div>
            <div class="flex space-x-2">
              <button
                @click="viewSchedule(member)"
                class="p-2 text-gray-400 hover:text-blue-600 rounded-md hover:bg-blue-50"
                title="Расписание"
              >
                <CalendarIcon class="w-4 h-4" />
              </button>
              <button
                @click="editStaff(member)"
                class="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
                title="Редактировать"
              >
                <EditIcon class="w-4 h-4" />
              </button>
              <button
                @click="deleteStaff(member)"
                class="p-2 text-gray-400 hover:text-red-600 rounded-md hover:bg-red-50"
                title="Удалить"
              >
                <TrashIcon class="w-4 h-4" />
              </button>
            </div>
          </div>

          <!-- Staff Info -->
          <div class="space-y-3">
            <div class="flex items-center space-x-2">
              <BuildingIcon class="w-4 h-4 text-gray-400" />
              <span class="text-sm text-gray-600">{{ member.department }}</span>
            </div>
            
            <div class="flex items-center space-x-2">
              <PhoneIcon class="w-4 h-4 text-gray-400" />
              <span class="text-sm text-gray-600">{{ member.phone }}</span>
            </div>
            
            <div class="flex items-center space-x-2">
              <MailIcon class="w-4 h-4 text-gray-400" />
              <span class="text-sm text-gray-600">{{ member.email }}</span>
            </div>

            <!-- Specializations -->
            <div>
              <div class="flex items-center space-x-2 mb-2">
                <StarIcon class="w-4 h-4 text-gray-400" />
                <span class="text-sm text-gray-600">Специализации:</span>
              </div>
              <div class="flex flex-wrap gap-1">
                <span
                  v-for="spec in member.specializations"
                  :key="spec"
                  class="badge badge-info text-xs"
                >
                  {{ spec }}
                </span>
              </div>
            </div>

            <!-- Status -->
            <div class="flex items-center justify-between pt-3 border-t border-gray-200">
              <span
                class="badge"
                :class="member.is_active ? 'badge-success' : 'badge-danger'"
              >
                {{ member.is_active ? 'Активен' : 'Неактивен' }}
              </span>
              <button
                @click="viewWorkload(member)"
                class="text-sm text-blue-600 hover:text-blue-800"
              >
                Загруженность
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between mt-6">
        <div class="text-sm text-gray-700">
          Показано {{ (currentPage - 1) * itemsPerPage + 1 }} - 
          {{ Math.min(currentPage * itemsPerPage, filteredStaff.length) }} 
          из {{ filteredStaff.length }} сотрудников
        </div>
        <div class="flex space-x-2">
          <button
            @click="currentPage = Math.max(1, currentPage - 1)"
            :disabled="currentPage === 1"
            class="btn btn-secondary btn-sm"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
          >
            Назад
          </button>
          <span class="flex items-center px-3 py-1 text-sm text-gray-700">
            {{ currentPage }} из {{ totalPages }}
          </span>
          <button
            @click="currentPage = Math.min(totalPages, currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="btn btn-secondary btn-sm"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
          >
            Далее
          </button>
        </div>
      </div>
    </div>

    <!-- Department Statistics -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-6">Статистика по отделам</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div
          v-for="(count, department) in departmentStats"
          :key="department"
          class="bg-gray-50 rounded-lg p-4"
        >
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">{{ department }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ count }}</p>
            </div>
            <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
              <UsersIcon class="w-5 h-5 text-primary-600" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Working Hours Overview -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-6">Рабочие часы</h2>
      
      <div class="overflow-x-auto">
        <table class="table">
          <thead class="table-header">
            <tr>
              <th class="table-head">Сотрудник</th>
              <th class="table-head">Пн</th>
              <th class="table-head">Вт</th>
              <th class="table-head">Ср</th>
              <th class="table-head">Чт</th>
              <th class="table-head">Пт</th>
              <th class="table-head">Сб</th>
              <th class="table-head">Вс</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="member in activeStaff.slice(0, 5)"
              :key="member.id"
              class="table-row"
            >
              <td class="table-cell">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                    <span class="text-white font-medium text-sm">
                      {{ getInitials(member.name) }}
                    </span>
                  </div>
                  <div>
                    <div class="font-medium text-gray-900">{{ member.name }}</div>
                    <div class="text-sm text-gray-500">{{ member.position }}</div>
                  </div>
                </div>
              </td>
              <td class="table-cell">
                <span
                  class="text-xs px-2 py-1 rounded"
                  :class="member.working_hours.monday.is_working ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'"
                >
                  {{ member.working_hours.monday.is_working ? 
                    `${member.working_hours.monday.start}-${member.working_hours.monday.end}` : 
                    'Выходной' }}
                </span>
              </td>
              <td class="table-cell">
                <span
                  class="text-xs px-2 py-1 rounded"
                  :class="member.working_hours.tuesday.is_working ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'"
                >
                  {{ member.working_hours.tuesday.is_working ? 
                    `${member.working_hours.tuesday.start}-${member.working_hours.tuesday.end}` : 
                    'Выходной' }}
                </span>
              </td>
              <td class="table-cell">
                <span
                  class="text-xs px-2 py-1 rounded"
                  :class="member.working_hours.wednesday.is_working ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'"
                >
                  {{ member.working_hours.wednesday.is_working ? 
                    `${member.working_hours.wednesday.start}-${member.working_hours.wednesday.end}` : 
                    'Выходной' }}
                </span>
              </td>
              <td class="table-cell">
                <span
                  class="text-xs px-2 py-1 rounded"
                  :class="member.working_hours.thursday.is_working ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'"
                >
                  {{ member.working_hours.thursday.is_working ? 
                    `${member.working_hours.thursday.start}-${member.working_hours.thursday.end}` : 
                    'Выходной' }}
                </span>
              </td>
              <td class="table-cell">
                <span
                  class="text-xs px-2 py-1 rounded"
                  :class="member.working_hours.friday.is_working ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'"
                >
                  {{ member.working_hours.friday.is_working ? 
                    `${member.working_hours.friday.start}-${member.working_hours.friday.end}` : 
                    'Выходной' }}
                </span>
              </td>
              <td class="table-cell">
                <span
                  class="text-xs px-2 py-1 rounded"
                  :class="member.working_hours.saturday.is_working ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'"
                >
                  {{ member.working_hours.saturday.is_working ? 
                    `${member.working_hours.saturday.start}-${member.working_hours.saturday.end}` : 
                    'Выходной' }}
                </span>
              </td>
              <td class="table-cell">
                <span
                  class="text-xs px-2 py-1 rounded"
                  :class="member.working_hours.sunday.is_working ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'"
                >
                  {{ member.working_hours.sunday.is_working ? 
                    `${member.working_hours.sunday.start}-${member.working_hours.sunday.end}` : 
                    'Выходной' }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div v-if="activeStaff.length > 5" class="mt-4 text-center">
        <button class="text-sm text-blue-600 hover:text-blue-800">
          Показать всех сотрудников ({{ activeStaff.length }})
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useStaffStore, useNotificationStore, useModalStore } from '@/stores'
import type { Staff } from '@/types'

// Stores
const staffStore = useStaffStore()
const notificationStore = useNotificationStore()
const modalStore = useModalStore()

// State
const searchQuery = ref('')
const departmentFilter = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const itemsPerPage = ref(9)

// Computed
const { staff, loading, activeStaff } = staffStore

const departments = computed(() => {
  const depts = new Set(staff.map(member => member.department))
  return Array.from(depts).sort()
})

const filteredStaff = computed(() => {
  let result = [...staff]

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(member =>
      member.name.toLowerCase().includes(query) ||
      member.email.toLowerCase().includes(query) ||
      member.phone.includes(query) ||
      member.position.toLowerCase().includes(query) ||
      member.specializations.some(spec => spec.toLowerCase().includes(query))
    )
  }

  // Filter by department
  if (departmentFilter.value) {
    result = result.filter(member => member.department === departmentFilter.value)
  }

  // Filter by status
  if (statusFilter.value) {
    if (statusFilter.value === 'active') {
      result = result.filter(member => member.is_active)
    } else if (statusFilter.value === 'inactive') {
      result = result.filter(member => !member.is_active)
    }
  }

  return result
})

const totalPages = computed(() => {
  return Math.ceil(filteredStaff.value.length / itemsPerPage.value)
})

const paginatedStaff = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  return filteredStaff.value.slice(start, end)
})

const departmentStats = computed(() => {
  const stats: Record<string, number> = {}
  staff.forEach(member => {
    if (member.is_active) {
      stats[member.department] = (stats[member.department] || 0) + 1
    }
  })
  return stats
})

// Methods
const openStaffForm = () => {
  modalStore.openStaffForm()
}

const editStaff = (member: Staff) => {
  modalStore.openStaffForm(member.id)
}

const deleteStaff = (member: Staff) => {
  modalStore.openConfirmDialog(
    'Удалить сотрудника',
    `Вы уверены, что хотите удалить сотрудника "${member.name}"? Это действие нельзя отменить.`,
    async () => {
      try {
        const success = await staffStore.deleteStaff(member.id)
        if (success) {
          notificationStore.success('Успешно', 'Сотрудник удален')
        } else {
          notificationStore.error('Ошибка', 'Не удалось удалить сотрудника')
        }
      } catch (error) {
        console.error('Error deleting staff:', error)
        notificationStore.error('Ошибка', 'Произошла ошибка при удалении сотрудника')
      }
    }
  )
}

const viewSchedule = (member: Staff) => {
  const today = new Date().toISOString().split('T')[0]
  modalStore.openStaffSchedule(member.id, today)
}

const viewWorkload = async (member: Staff) => {
  try {
    const today = new Date()
    const weekStart = new Date(today)
    weekStart.setDate(today.getDate() - today.getDay() + 1) // Monday
    const weekEnd = new Date(weekStart)
    weekEnd.setDate(weekStart.getDate() + 6) // Sunday

    const workload = await staffStore.getStaffWorkload(member.id, {
      start: weekStart.toISOString().split('T')[0],
      end: weekEnd.toISOString().split('T')[0]
    })

    if (workload) {
      notificationStore.info(
        `Загруженность: ${member.name}`,
        `За эту неделю: ${workload.total_bookings} бронирований, ${workload.total_hours} часов, ${workload.total_revenue} ₽`
      )
    } else {
      notificationStore.error('Ошибка', 'Не удалось загрузить данные о загруженности')
    }
  } catch (error) {
    console.error('Error fetching workload:', error)
    notificationStore.error('Ошибка', 'Произошла ошибка при загрузке данных')
  }
}

const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2)
}

// Icon components
const PlusIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M12 4v16m8-8H4'
  })
])

const EditIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z'
  })
])

const TrashIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16'
  })
])

const CalendarIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('rect', { x: '3', y: '4', width: '18', height: '18', rx: '2', ry: '2' }),
  h('line', { x1: '16', y1: '2', x2: '16', y2: '6' }),
  h('line', { x1: '8', y1: '2', x2: '8', y2: '6' }),
  h('line', { x1: '3', y1: '10', x2: '21', y2: '10' })
])

const BuildingIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4'
  })
])

const PhoneIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z'
  })
])

const MailIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z'
  })
])

const StarIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z'
  })
])

const UsersIcon = () => h('svg', {
  class: 'w-5 h-5',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z'
  })
])

// Lifecycle
onMounted(async () => {
  // Initialize mock staff if none exist
  staffStore.initializeMockStaff()
  
  // Fetch staff data
  await staffStore.fetchStaff()
})
</script>