<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Управление услугами</h1>
          <p class="text-gray-600 mt-1">Создание и редактирование услуг и категорий</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button
            @click="openCategoryForm"
            class="btn btn-secondary"
          >
            <FolderIcon class="w-4 h-4 mr-2" />
            Добавить категорию
          </button>
          <button
            @click="openServiceForm"
            class="btn btn-primary"
          >
            <PlusIcon class="w-4 h-4 mr-2" />
            Добавить услугу
          </button>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Поиск услуг..."
            class="input"
          />
        </div>
        <div class="w-full sm:w-48">
          <select v-model="selectedCategory" class="select">
            <option value="">Все категории</option>
            <option
              v-for="category in categories"
              :key="category.id"
              :value="category.slug"
            >
              {{ category.name }}
            </option>
          </select>
        </div>
        <div class="w-full sm:w-32">
          <select v-model="statusFilter" class="select">
            <option value="">Все статусы</option>
            <option value="active">Активные</option>
            <option value="inactive">Неактивные</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Categories Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-6">Категории услуг</h2>

      <div v-if="loading" class="flex justify-center py-8">
        <div class="spinner w-8 h-8"></div>
      </div>

      <div v-else-if="categories.length === 0" class="text-center py-8 text-gray-500">
        Нет доступных категорий
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          v-for="category in filteredCategories"
          :key="category.id"
          class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="font-medium text-gray-900">{{ category.name }}</h3>
              <p class="text-sm text-gray-500 mt-1">{{ category.description }}</p>
              <div class="flex items-center mt-2 space-x-4">
                <span class="text-sm text-gray-500">
                  {{ category.services_count }} услуг
                </span>
                <span
                  class="badge"
                  :class="category.is_active ? 'badge-success' : 'badge-danger'"
                >
                  {{ category.is_active ? 'Активна' : 'Неактивна' }}
                </span>
              </div>
            </div>
            <div class="flex space-x-2">
              <button
                @click="editCategory(category)"
                class="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
              >
                <EditIcon class="w-4 h-4" />
              </button>
              <button
                @click="deleteCategory(category)"
                class="p-2 text-gray-400 hover:text-red-600 rounded-md hover:bg-red-50"
              >
                <TrashIcon class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Services Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-6">Услуги</h2>

      <div v-if="loading" class="flex justify-center py-8">
        <div class="spinner w-8 h-8"></div>
      </div>

      <div v-else-if="filteredServices.length === 0" class="text-center py-8 text-gray-500">
        Нет услуг, соответствующих критериям поиска
      </div>

      <div v-else class="overflow-x-auto">
        <table class="table">
          <thead class="table-header">
            <tr>
              <th class="table-head">Название</th>
              <th class="table-head">Категория</th>
              <th class="table-head">Цена</th>
              <th class="table-head">Длительность</th>
              <th class="table-head">Макс. человек</th>
              <th class="table-head">Статус</th>
              <th class="table-head">Действия</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="service in paginatedServices"
              :key="service.id"
              class="table-row"
            >
              <td class="table-cell">
                <div class="flex items-center">
                  <div v-if="service.image" class="w-10 h-10 rounded-lg bg-gray-200 mr-3 flex-shrink-0">
                    <img
                      :src="service.image"
                      :alt="service.name"
                      class="w-full h-full object-cover rounded-lg"
                    />
                  </div>
                  <div>
                    <div class="font-medium text-gray-900">{{ service.name }}</div>
                    <div v-if="service.description" class="text-sm text-gray-500">
                      {{ truncateText(service.description, 50) }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="table-cell">
                <span class="badge badge-info">
                  {{ getCategoryName(service.category) }}
                </span>
              </td>
              <td class="table-cell">
                <span v-if="service.is_free" class="text-green-600 font-medium">
                  Бесплатно
                </span>
                <span v-else class="font-medium">
                  {{ formatPrice(service.price) }}
                </span>
              </td>
              <td class="table-cell">
                {{ formatDuration(service.duration) }}
              </td>
              <td class="table-cell">
                {{ service.max_persons }}
              </td>
              <td class="table-cell">
                <span
                  class="badge"
                  :class="service.is_active !== false ? 'badge-success' : 'badge-danger'"
                >
                  {{ service.is_active !== false ? 'Активна' : 'Неактивна' }}
                </span>
              </td>
              <td class="table-cell">
                <div class="flex space-x-2">
                  <button
                    @click="editService(service)"
                    class="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
                  >
                    <EditIcon class="w-4 h-4" />
                  </button>
                  <button
                    @click="duplicateService(service)"
                    class="p-2 text-gray-400 hover:text-blue-600 rounded-md hover:bg-blue-50"
                  >
                    <CopyIcon class="w-4 h-4" />
                  </button>
                  <button
                    @click="deleteService(service)"
                    class="p-2 text-gray-400 hover:text-red-600 rounded-md hover:bg-red-50"
                  >
                    <TrashIcon class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between mt-6">
        <div class="text-sm text-gray-700">
          Показано {{ (currentPage - 1) * itemsPerPage + 1 }} - 
          {{ Math.min(currentPage * itemsPerPage, filteredServices.length) }} 
          из {{ filteredServices.length }} услуг
        </div>
        <div class="flex space-x-2">
          <button
            @click="currentPage = Math.max(1, currentPage - 1)"
            :disabled="currentPage === 1"
            class="btn btn-secondary btn-sm"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
          >
            Назад
          </button>
          <span class="flex items-center px-3 py-1 text-sm text-gray-700">
            {{ currentPage }} из {{ totalPages }}
          </span>
          <button
            @click="currentPage = Math.min(totalPages, currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="btn btn-secondary btn-sm"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
          >
            Далее
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useServiceStore, useNotificationStore, useModalStore } from '@/stores'
import type { Service, Category } from '@/types'

// Stores
const serviceStore = useServiceStore()
const notificationStore = useNotificationStore()
const modalStore = useModalStore()

// State
const searchQuery = ref('')
const selectedCategory = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const itemsPerPage = ref(10)

// Computed
const { services, categories, loading } = serviceStore

const filteredCategories = computed(() => {
  return categories.filter(category => category.id !== 0) // Exclude "All categories"
})

const filteredServices = computed(() => {
  let result = [...services]

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(service =>
      service.name.toLowerCase().includes(query) ||
      (service.description && service.description.toLowerCase().includes(query))
    )
  }

  // Filter by category
  if (selectedCategory.value) {
    result = result.filter(service => service.category === selectedCategory.value)
  }

  // Filter by status
  if (statusFilter.value) {
    if (statusFilter.value === 'active') {
      result = result.filter(service => service.is_active !== false)
    } else if (statusFilter.value === 'inactive') {
      result = result.filter(service => service.is_active === false)
    }
  }

  return result
})

const totalPages = computed(() => {
  return Math.ceil(filteredServices.value.length / itemsPerPage.value)
})

const paginatedServices = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  return filteredServices.value.slice(start, end)
})

// Methods
const openServiceForm = () => {
  modalStore.openServiceForm()
}

const openCategoryForm = () => {
  modalStore.openCategoryForm()
}

const editService = (service: Service) => {
  modalStore.openServiceForm(service.id)
}

const editCategory = (category: Category) => {
  modalStore.openCategoryForm(category.id)
}

const duplicateService = async (service: Service) => {
  try {
    const duplicatedService = {
      ...service,
      name: `${service.name} (копия)`,
      id: undefined as any
    }
    delete duplicatedService.id

    const result = await serviceStore.createService(duplicatedService)
    if (result) {
      notificationStore.success('Успешно', 'Услуга скопирована')
    } else {
      notificationStore.error('Ошибка', 'Не удалось скопировать услугу')
    }
  } catch (error) {
    console.error('Error duplicating service:', error)
    notificationStore.error('Ошибка', 'Произошла ошибка при копировании услуги')
  }
}

const deleteService = (service: Service) => {
  modalStore.openConfirmDialog(
    'Удалить услугу',
    `Вы уверены, что хотите удалить услугу "${service.name}"? Это действие нельзя отменить.`,
    async () => {
      try {
        const success = await serviceStore.deleteService(service.id)
        if (success) {
          notificationStore.success('Успешно', 'Услуга удалена')
        } else {
          notificationStore.error('Ошибка', 'Не удалось удалить услугу')
        }
      } catch (error) {
        console.error('Error deleting service:', error)
        notificationStore.error('Ошибка', 'Произошла ошибка при удалении услуги')
      }
    }
  )
}

const deleteCategory = (category: Category) => {
  modalStore.openConfirmDialog(
    'Удалить категорию',
    `Вы уверены, что хотите удалить категорию "${category.name}"? Все услуги в этой категории также будут удалены.`,
    async () => {
      try {
        const success = await serviceStore.deleteCategory(category.id)
        if (success) {
          notificationStore.success('Успешно', 'Категория удалена')
        } else {
          notificationStore.error('Ошибка', 'Не удалось удалить категорию')
        }
      } catch (error) {
        console.error('Error deleting category:', error)
        notificationStore.error('Ошибка', 'Произошла ошибка при удалении категории')
      }
    }
  )
}

const getCategoryName = (categorySlug?: string): string => {
  if (!categorySlug) return 'Без категории'
  const category = categories.find(c => c.slug === categorySlug)
  return category?.name || categorySlug
}

const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('ru-RU', {
    style: 'currency',
    currency: 'RUB',
    minimumFractionDigits: 0
  }).format(price)
}

const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes} мин`
  }
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  if (remainingMinutes === 0) {
    return `${hours} ч`
  }
  return `${hours} ч ${remainingMinutes} мин`
}

const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// Icon components
const PlusIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M12 4v16m8-8H4'
  })
])

const FolderIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z'
  }),
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z'
  })
])

const EditIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z'
  })
])

const TrashIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16'
  })
])

const CopyIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z'
  })
])

// Lifecycle
onMounted(async () => {
  // Initialize default categories if none exist
  serviceStore.initializeDefaultCategories()

  // Fetch data
  await Promise.all([
    serviceStore.fetchCategories(),
    serviceStore.fetchServices()
  ])
})
</script>
