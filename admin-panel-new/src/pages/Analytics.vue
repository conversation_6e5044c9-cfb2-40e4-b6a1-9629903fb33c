<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Аналитика и статистика</h1>
          <p class="text-gray-600 mt-1">Анализ эффективности услуг и доходности</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <div class="flex items-center space-x-2">
            <label class="text-sm text-gray-600">Период:</label>
            <select v-model="selectedPeriod" @change="updateDateRange" class="select text-sm">
              <option value="week">Неделя</option>
              <option value="month">Месяц</option>
              <option value="quarter">Квартал</option>
              <option value="year">Год</option>
              <option value="custom">Произвольный</option>
            </select>
          </div>
          <router-link
            to="/reports"
            class="btn btn-primary"
          >
            <DocumentIcon class="w-4 h-4 mr-2" />
            Отчеты
          </router-link>
        </div>
      </div>

      <!-- Custom Date Range -->
      <div v-if="selectedPeriod === 'custom'" class="mt-4 flex space-x-4">
        <div>
          <label class="label text-sm text-gray-600">От:</label>
          <input
            v-model="customDateRange.start"
            type="date"
            class="input"
            @change="fetchAnalytics"
          />
        </div>
        <div>
          <label class="label text-sm text-gray-600">До:</label>
          <input
            v-model="customDateRange.end"
            type="date"
            class="input"
            @change="fetchAnalytics"
          />
        </div>
      </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Всего бронирований</p>
            <p class="text-3xl font-bold text-gray-900">
              {{ analyticsData?.totalBookings || 0 }}
            </p>
            <p class="text-sm text-green-600 mt-1">
              +12% от прошлого периода
            </p>
          </div>
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <CalendarIcon class="w-6 h-6 text-blue-600" />
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Общая выручка</p>
            <p class="text-3xl font-bold text-gray-900">
              {{ formatCurrency(analyticsData?.totalRevenue || 0) }}
            </p>
            <p class="text-sm text-green-600 mt-1">
              +8% от прошлого периода
            </p>
          </div>
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <CurrencyIcon class="w-6 h-6 text-green-600" />
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Средний чек</p>
            <p class="text-3xl font-bold text-gray-900">
              {{ formatCurrency(analyticsData?.averageBookingValue || 0) }}
            </p>
            <p class="text-sm text-red-600 mt-1">
              -3% от прошлого периода
            </p>
          </div>
          <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
            <TrendingUpIcon class="w-6 h-6 text-yellow-600" />
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Процент отказов</p>
            <p class="text-3xl font-bold text-gray-900">
              {{ (analyticsData?.cancellationRate || 0).toFixed(1) }}%
            </p>
            <p class="text-sm text-green-600 mt-1">
              -2% от прошлого периода
            </p>
          </div>
          <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
            <XCircleIcon class="w-6 h-6 text-red-600" />
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row 1 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Revenue Chart -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Динамика выручки</h3>
        <div v-if="loading" class="flex justify-center py-8">
          <div class="spinner w-8 h-8"></div>
        </div>
        <div v-else class="chart-container">
          <canvas ref="revenueChart"></canvas>
        </div>
      </div>

      <!-- Bookings Chart -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Количество бронирований</h3>
        <div v-if="loading" class="flex justify-center py-8">
          <div class="spinner w-8 h-8"></div>
        </div>
        <div v-else class="chart-container">
          <canvas ref="bookingsChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Category Revenue Chart -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Выручка по категориям</h3>
        <div v-if="loading" class="flex justify-center py-8">
          <div class="spinner w-8 h-8"></div>
        </div>
        <div v-else class="chart-container">
          <canvas ref="categoryChart"></canvas>
        </div>
      </div>

      <!-- Status Distribution Chart -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Распределение по статусам</h3>
        <div v-if="loading" class="flex justify-center py-8">
          <div class="spinner w-8 h-8"></div>
        </div>
        <div v-else class="chart-container">
          <canvas ref="statusChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Popular Services -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-6">Популярные услуги</h3>

      <div v-if="loading" class="flex justify-center py-8">
        <div class="spinner w-8 h-8"></div>
      </div>

      <div v-else-if="!analyticsData?.popularServices?.length" class="text-center py-8 text-gray-500">
        Нет данных о популярных услугах
      </div>

      <div v-else class="space-y-4">
        <div
          v-for="(service, index) in analyticsData.popularServices"
          :key="service.name"
          class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
        >
          <div class="flex items-center space-x-4">
            <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
              <span class="text-white font-medium text-sm">{{ index + 1 }}</span>
            </div>
            <div>
              <h4 class="font-medium text-gray-900">{{ service.name }}</h4>
              <p class="text-sm text-gray-500">{{ service.count }} бронирований</p>
            </div>
          </div>
          <div class="text-right">
            <p class="font-medium text-gray-900">{{ formatCurrency(service.revenue) }}</p>
            <p class="text-sm text-gray-500">выручка</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Staff Performance -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-6">Эффективность персонала</h3>

      <div v-if="loading" class="flex justify-center py-8">
        <div class="spinner w-8 h-8"></div>
      </div>

      <div v-else-if="!analyticsData?.staffPerformance?.length" class="text-center py-8 text-gray-500">
        Нет данных об эффективности персонала
      </div>

      <div v-else class="overflow-x-auto">
        <table class="table">
          <thead class="table-header">
            <tr>
              <th class="table-head">Сотрудник</th>
              <th class="table-head">Бронирования</th>
              <th class="table-head">Выручка</th>
              <th class="table-head">Рейтинг</th>
              <th class="table-head">Эффективность</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="staff in analyticsData.staffPerformance"
              :key="staff.staff_id"
              class="table-row"
            >
              <td class="table-cell">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                    <span class="text-white font-medium text-sm">
                      {{ getInitials(staff.staff_name) }}
                    </span>
                  </div>
                  <span class="font-medium text-gray-900">{{ staff.staff_name }}</span>
                </div>
              </td>
              <td class="table-cell">{{ staff.bookings_count }}</td>
              <td class="table-cell">{{ formatCurrency(staff.revenue) }}</td>
              <td class="table-cell">
                <div class="flex items-center space-x-1">
                  <StarIcon class="w-4 h-4 text-yellow-400" />
                  <span>{{ staff.rating.toFixed(1) }}</span>
                </div>
              </td>
              <td class="table-cell">
                <div class="flex items-center space-x-2">
                  <div class="w-16 bg-gray-200 rounded-full h-2">
                    <div
                      class="bg-green-600 h-2 rounded-full"
                      :style="{ width: `${Math.min((staff.revenue / 50000) * 100, 100)}%` }"
                    ></div>
                  </div>
                  <span class="text-sm text-gray-600">
                    {{ Math.min((staff.revenue / 50000) * 100, 100).toFixed(0) }}%
                  </span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, h } from 'vue'
import { useAnalyticsStore, useNotificationStore } from '@/stores'
import { Chart, registerables } from 'chart.js'

// Register Chart.js components
Chart.register(...registerables)

// Stores
const analyticsStore = useAnalyticsStore()
const notificationStore = useNotificationStore()

// State
const selectedPeriod = ref('month')
const customDateRange = ref({
  start: '',
  end: ''
})

// Chart refs
const revenueChart = ref<HTMLCanvasElement>()
const bookingsChart = ref<HTMLCanvasElement>()
const categoryChart = ref<HTMLCanvasElement>()
const statusChart = ref<HTMLCanvasElement>()

// Chart instances
let revenueChartInstance: Chart | null = null
let bookingsChartInstance: Chart | null = null
let categoryChartInstance: Chart | null = null
let statusChartInstance: Chart | null = null

// Computed
const analyticsData = computed(() => analyticsStore.analyticsData)
const loading = computed(() => analyticsStore.loading)

// Methods
const updateDateRange = () => {
  const today = new Date()
  let start: Date
  let end: Date = today

  switch (selectedPeriod.value) {
    case 'week':
      start = new Date(today)
      start.setDate(today.getDate() - 7)
      break
    case 'month':
      start = new Date(today)
      start.setMonth(today.getMonth() - 1)
      break
    case 'quarter':
      start = new Date(today)
      start.setMonth(today.getMonth() - 3)
      break
    case 'year':
      start = new Date(today)
      start.setFullYear(today.getFullYear() - 1)
      break
    case 'custom':
      if (customDateRange.value.start && customDateRange.value.end) {
        start = new Date(customDateRange.value.start)
        end = new Date(customDateRange.value.end)
      } else {
        return
      }
      break
    default:
      start = new Date(today)
      start.setMonth(today.getMonth() - 1)
  }

  analyticsStore.updateDateRange({
    start: start.toISOString().split('T')[0],
    end: end.toISOString().split('T')[0]
  })

  fetchAnalytics()
}

const fetchAnalytics = async () => {
  try {
    await analyticsStore.fetchAnalytics()
    await nextTick()
    createCharts()
  } catch (error) {
    console.error('Error fetching analytics:', error)
    notificationStore.error('Ошибка', 'Не удалось загрузить данные аналитики')
  }
}

const createCharts = () => {
  if (!analyticsData.value) return

  createRevenueChart()
  createBookingsChart()
  createCategoryChart()
  createStatusChart()
}

const createRevenueChart = () => {
  if (!revenueChart.value || !analyticsData.value) return

  // Destroy existing chart
  if (revenueChartInstance) {
    revenueChartInstance.destroy()
  }

  const ctx = revenueChart.value.getContext('2d')
  if (!ctx) return

  revenueChartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels: analyticsData.value.dailyStats.map(stat => 
        new Date(stat.date).toLocaleDateString('ru-RU', { day: '2-digit', month: '2-digit' })
      ),
      datasets: [{
        label: 'Выручка',
        data: analyticsData.value.dailyStats.map(stat => stat.revenue),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return formatCurrency(value as number)
            }
          }
        }
      }
    }
  })
}

const createBookingsChart = () => {
  if (!bookingsChart.value || !analyticsData.value) return

  // Destroy existing chart
  if (bookingsChartInstance) {
    bookingsChartInstance.destroy()
  }

  const ctx = bookingsChart.value.getContext('2d')
  if (!ctx) return

  bookingsChartInstance = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: analyticsData.value.dailyStats.map(stat => 
        new Date(stat.date).toLocaleDateString('ru-RU', { day: '2-digit', month: '2-digit' })
      ),
      datasets: [{
        label: 'Бронирования',
        data: analyticsData.value.dailyStats.map(stat => stat.bookings),
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
        borderColor: 'rgb(34, 197, 94)',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            stepSize: 1
          }
        }
      }
    }
  })
}

const createCategoryChart = () => {
  if (!categoryChart.value || !analyticsData.value) return

  // Destroy existing chart
  if (categoryChartInstance) {
    categoryChartInstance.destroy()
  }

  const ctx = categoryChart.value.getContext('2d')
  if (!ctx) return

  const colors = [
    'rgba(59, 130, 246, 0.8)',
    'rgba(234, 179, 8, 0.8)',
    'rgba(34, 197, 94, 0.8)',
    'rgba(239, 68, 68, 0.8)',
    'rgba(168, 85, 247, 0.8)'
  ]

  categoryChartInstance = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: analyticsData.value.revenueByCategory.map(cat => cat.category),
      datasets: [{
        data: analyticsData.value.revenueByCategory.map(cat => cat.revenue),
        backgroundColor: colors,
        borderWidth: 0
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom'
        }
      }
    }
  })
}

const createStatusChart = () => {
  if (!statusChart.value || !analyticsData.value) return

  // Destroy existing chart
  if (statusChartInstance) {
    statusChartInstance.destroy()
  }

  const ctx = statusChart.value.getContext('2d')
  if (!ctx) return

  const colors = {
    'confirmed': 'rgba(34, 197, 94, 0.8)',
    'pending': 'rgba(234, 179, 8, 0.8)',
    'cancelled': 'rgba(239, 68, 68, 0.8)'
  }

  statusChartInstance = new Chart(ctx, {
    type: 'pie',
    data: {
      labels: analyticsData.value.bookingsByStatus.map(status => {
        const labels: Record<string, string> = {
          'confirmed': 'Подтверждено',
          'pending': 'Ожидает',
          'cancelled': 'Отменено'
        }
        return labels[status.status] || status.status
      }),
      datasets: [{
        data: analyticsData.value.bookingsByStatus.map(status => status.count),
        backgroundColor: analyticsData.value.bookingsByStatus.map(status => 
          colors[status.status as keyof typeof colors] || 'rgba(107, 114, 128, 0.8)'
        ),
        borderWidth: 0
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom'
        }
      }
    }
  })
}

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('ru-RU', {
    style: 'currency',
    currency: 'RUB',
    minimumFractionDigits: 0
  }).format(amount)
}

const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2)
}

// Icon components
const DocumentIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
  })
])

const CalendarIcon = () => h('svg', {
  class: 'w-6 h-6',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('rect', { x: '3', y: '4', width: '18', height: '18', rx: '2', ry: '2' }),
  h('line', { x1: '16', y1: '2', x2: '16', y2: '6' }),
  h('line', { x1: '8', y1: '2', x2: '8', y2: '6' }),
  h('line', { x1: '3', y1: '10', x2: '21', y2: '10' })
])

const CurrencyIcon = () => h('svg', {
  class: 'w-6 h-6',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1'
  })
])

const TrendingUpIcon = () => h('svg', {
  class: 'w-6 h-6',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6'
  })
])

const XCircleIcon = () => h('svg', {
  class: 'w-6 h-6',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z'
  })
])

const StarIcon = () => h('svg', {
  class: 'w-4 h-4',
  fill: 'currentColor',
  viewBox: '0 0 20 20'
}, [
  h('path', {
    d: 'M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z'
  })
])

// Lifecycle
onMounted(async () => {
  // Initialize date range
  updateDateRange()

  // Fetch initial analytics data
  await fetchAnalytics()
})
</script>

<style scoped>
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}
</style>
