<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="border-b border-gray-200 pb-4">
      <h3 class="text-lg font-medium text-gray-900">Управление несколькими бронированиями</h3>
      <p class="text-sm text-gray-500">
        Выбрано {{ selectedBookings.length }} бронирований для массовых операций
      </p>
    </div>

    <div class="space-y-6">
      <!-- Selected Bookings List -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Выбранные бронирования</h4>
        <div class="space-y-3 max-h-60 overflow-y-auto">
          <div
            v-for="booking in selectedBookings"
            :key="booking.id"
            class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200"
          >
            <div class="flex items-center space-x-3">
              <button
                @click="removeBooking(booking.id)"
                class="text-red-600 hover:text-red-800"
                title="Удалить из списка"
              >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </button>
              <div>
                <div class="font-medium text-gray-900">{{ booking.code }}</div>
                <div class="text-sm text-gray-500">
                  {{ booking.guest_name }} • {{ formatDate(booking.date) }} {{ booking.time }}
                </div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">{{ formatCurrency(booking.total_price) }}</div>
              <div class="text-xs text-gray-500">{{ getStatusLabel(booking.status) }}</div>
            </div>
          </div>
        </div>

        <div v-if="selectedBookings.length === 0" class="text-center py-8 text-gray-500">
          Нет выбранных бронирований
        </div>
      </div>

      <!-- Bulk Operations -->
      <div class="bg-blue-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Массовые операции</h4>
        <div class="space-y-4">
          <!-- Operation Type -->
          <div>
            <label class="label text-gray-700 mb-2">Выберите операцию *</label>
            <select v-model="form.operation" class="select" required>
              <option value="">Выберите операцию</option>
              <option value="confirm">Подтвердить все</option>
              <option value="cancel">Отменить все</option>
              <option value="reschedule">Перенести все</option>
              <option value="assign_staff">Назначить сотрудника</option>
              <option value="update_status">Изменить статус</option>
              <option value="send_notifications">Отправить уведомления</option>
              <option value="export">Экспортировать данные</option>
            </select>
          </div>

          <!-- Operation-specific fields -->

          <!-- Cancel Operation -->
          <div v-if="form.operation === 'cancel'" class="space-y-3">
            <div>
              <label class="label text-gray-700 mb-2">Причина отмены</label>
              <select v-model="form.cancelReason" class="select">
                <option value="">Выберите причину</option>
                <option value="guest_request">Просьба гостя</option>
                <option value="staff_unavailable">Недоступность сотрудника</option>
                <option value="equipment_issue">Проблемы с оборудованием</option>
                <option value="emergency">Чрезвычайная ситуация</option>
                <option value="other">Другая причина</option>
              </select>
            </div>
            <div>
              <label class="label text-gray-700 mb-2">Тип возврата</label>
              <select v-model="form.refundType" class="select">
                <option value="full">Полный возврат</option>
                <option value="partial">Частичный возврат</option>
                <option value="none">Без возврата</option>
              </select>
            </div>
          </div>

          <!-- Reschedule Operation -->
          <div v-if="form.operation === 'reschedule'" class="space-y-3">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="label text-gray-700 mb-2">Новая дата</label>
                <input
                  v-model="form.newDate"
                  type="date"
                  class="input"
                  :min="minDate"
                />
              </div>
              <div>
                <label class="label text-gray-700 mb-2">Новое время</label>
                <select v-model="form.newTime" class="select">
                  <option value="">Выберите время</option>
                  <option
                    v-for="timeSlot in availableTimeSlots"
                    :key="timeSlot"
                    :value="timeSlot"
                  >
                    {{ timeSlot }}
                  </option>
                </select>
              </div>
            </div>
          </div>

          <!-- Assign Staff Operation -->
          <div v-if="form.operation === 'assign_staff'">
            <label class="label text-gray-700 mb-2">Сотрудник</label>
            <select v-model="form.staffId" class="select">
              <option value="">Выберите сотрудника</option>
              <option
                v-for="staff in availableStaff"
                :key="staff.id"
                :value="staff.id"
              >
                {{ staff.name }} - {{ staff.position }}
              </option>
            </select>
          </div>

          <!-- Update Status Operation -->
          <div v-if="form.operation === 'update_status'">
            <label class="label text-gray-700 mb-2">Новый статус</label>
            <select v-model="form.newStatus" class="select">
              <option value="">Выберите статус</option>
              <option value="confirmed">Подтверждено</option>
              <option value="pending">Ожидает подтверждения</option>
              <option value="cancelled">Отменено</option>
            </select>
          </div>

          <!-- Send Notifications Operation -->
          <div v-if="form.operation === 'send_notifications'" class="space-y-3">
            <div>
              <label class="label text-gray-700 mb-2">Тип уведомления</label>
              <select v-model="form.notificationType" class="select">
                <option value="">Выберите тип</option>
                <option value="reminder">Напоминание</option>
                <option value="confirmation">Подтверждение</option>
                <option value="update">Обновление</option>
                <option value="custom">Произвольное сообщение</option>
              </select>
            </div>
            <div v-if="form.notificationType === 'custom'">
              <label class="label text-gray-700 mb-2">Сообщение</label>
              <textarea
                v-model="form.customMessage"
                class="input"
                rows="3"
                placeholder="Введите текст сообщения..."
              ></textarea>
            </div>
          </div>

          <!-- Export Operation -->
          <div v-if="form.operation === 'export'">
            <label class="label text-gray-700 mb-2">Формат экспорта</label>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
              <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input
                  v-model="form.exportFormat"
                  type="radio"
                  value="pdf"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">PDF</span>
              </label>
              <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input
                  v-model="form.exportFormat"
                  type="radio"
                  value="excel"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">Excel</span>
              </label>
              <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input
                  v-model="form.exportFormat"
                  type="radio"
                  value="csv"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">CSV</span>
              </label>
              <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input
                  v-model="form.exportFormat"
                  type="radio"
                  value="json"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">JSON</span>
              </label>
            </div>
          </div>

          <!-- Additional Notes -->
          <div v-if="['cancel', 'reschedule', 'assign_staff'].includes(form.operation)">
            <label class="label text-gray-700 mb-2">Дополнительные заметки</label>
            <textarea
              v-model="form.notes"
              class="input"
              rows="3"
              placeholder="Дополнительная информация (необязательно)..."
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Notification Options -->
      <div v-if="form.operation && form.operation !== 'export'" class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Уведомления</h4>
        <div class="space-y-3">
          <label class="flex items-center">
            <input
              v-model="form.notifyGuests"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">
              Уведомить всех гостей
            </span>
          </label>
          <label class="flex items-center">
            <input
              v-model="form.notifyStaff"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">
              Уведомить назначенных сотрудников
            </span>
          </label>
        </div>
      </div>

      <!-- Summary -->
      <div v-if="form.operation" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 class="text-md font-medium text-yellow-800 mb-2">Сводка операции</h4>
        <div class="text-sm text-yellow-700">
          <p><strong>Операция:</strong> {{ getOperationLabel(form.operation) }}</p>
          <p><strong>Количество бронирований:</strong> {{ selectedBookings.length }}</p>
          <p><strong>Общая стоимость:</strong> {{ formatCurrency(totalAmount) }}</p>
          <p v-if="form.notifyGuests || form.notifyStaff">
            <strong>Уведомления:</strong> 
            {{ form.notifyGuests ? 'Гости' : '' }}
            {{ form.notifyGuests && form.notifyStaff ? ', ' : '' }}
            {{ form.notifyStaff ? 'Сотрудники' : '' }}
          </p>
        </div>
      </div>

      <!-- Warning -->
      <div v-if="form.operation === 'cancel'" class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
          <svg class="w-5 h-5 text-red-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
          </svg>
          <div>
            <h4 class="text-sm font-medium text-red-800">Внимание</h4>
            <p class="text-sm text-red-700 mt-1">
              Массовая отмена бронирований необратима. Убедитесь, что все параметры указаны правильно.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
      <button
        @click="closeModal"
        type="button"
        class="btn btn-secondary"
      >
        Отмена
      </button>
      <button
        @click="handleSubmit"
        :disabled="!isFormValid || processing"
        class="btn btn-primary"
      >
        <span v-if="processing">Обработка...</span>
        <span v-else>Выполнить операцию</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useBookingStore, useStaffStore, useModalStore, useNotificationStore } from '@/stores'
import type { Booking } from '@/types'

// Props
interface Props {
  bookingIds?: number[]
}

const props = defineProps<Props>()

// Stores
const bookingStore = useBookingStore()
const staffStore = useStaffStore()
const modalStore = useModalStore()
const notificationStore = useNotificationStore()

// State
const processing = ref(false)
const selectedBookings = ref<Booking[]>([])

const form = ref({
  operation: '',
  cancelReason: '',
  refundType: 'full',
  newDate: '',
  newTime: '',
  staffId: null as number | null,
  newStatus: '',
  notificationType: '',
  customMessage: '',
  exportFormat: 'pdf',
  notes: '',
  notifyGuests: true,
  notifyStaff: true
})

// Computed
const minDate = computed(() => {
  return new Date().toISOString().split('T')[0]
})

const availableStaff = computed(() => {
  return staffStore.activeStaff
})

const availableTimeSlots = computed(() => {
  const slots = []
  for (let hour = 9; hour <= 21; hour++) {
    slots.push(`${hour.toString().padStart(2, '0')}:00`)
    if (hour < 21) {
      slots.push(`${hour.toString().padStart(2, '0')}:30`)
    }
  }
  return slots
})

const totalAmount = computed(() => {
  return selectedBookings.value.reduce((sum, booking) => sum + booking.total_price, 0)
})

const isFormValid = computed(() => {
  if (!form.value.operation || selectedBookings.value.length === 0) return false

  switch (form.value.operation) {
    case 'reschedule':
      return form.value.newDate !== '' && form.value.newTime !== ''
    case 'assign_staff':
      return form.value.staffId !== null
    case 'update_status':
      return form.value.newStatus !== ''
    case 'send_notifications':
      return form.value.notificationType !== '' && 
             (form.value.notificationType !== 'custom' || form.value.customMessage.trim() !== '')
    case 'export':
      return form.value.exportFormat !== ''
    default:
      return true
  }
})

// Methods
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('ru-RU', {
    style: 'currency',
    currency: 'RUB',
    minimumFractionDigits: 0
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ru-RU', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  })
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    confirmed: 'Подтверждено',
    pending: 'Ожидает',
    cancelled: 'Отменено'
  }
  return labels[status] || status
}

const getOperationLabel = (operation: string) => {
  const labels: Record<string, string> = {
    confirm: 'Подтверждение всех бронирований',
    cancel: 'Отмена всех бронирований',
    reschedule: 'Перенос всех бронирований',
    assign_staff: 'Назначение сотрудника',
    update_status: 'Изменение статуса',
    send_notifications: 'Отправка уведомлений',
    export: 'Экспорт данных'
  }
  return labels[operation] || operation
}

const removeBooking = (bookingId: number) => {
  selectedBookings.value = selectedBookings.value.filter(b => b.id !== bookingId)
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  processing.value = true

  try {
    // In a real app, you'd call a bulk operation method with this data:
    // const operationData = {
    //   operation: form.value.operation,
    //   bookingIds: selectedBookings.value.map(b => b.id),
    //   params: {
    //     cancelReason: form.value.cancelReason || undefined,
    //     refundType: form.value.refundType || undefined,
    //     newDate: form.value.newDate || undefined,
    //     newTime: form.value.newTime || undefined,
    //     staffId: form.value.staffId || undefined,
    //     newStatus: form.value.newStatus || undefined,
    //     notificationType: form.value.notificationType || undefined,
    //     customMessage: form.value.customMessage || undefined,
    //     exportFormat: form.value.exportFormat || undefined,
    //     notes: form.value.notes || undefined
    //   },
    //   notifications: {
    //     notifyGuests: form.value.notifyGuests,
    //     notifyStaff: form.value.notifyStaff
    //   }
    // }
    // const success = await bookingStore.bulkOperation(operationData)

    // For now, we'll simulate success
    const success = true

    if (success) {
      notificationStore.success('Успешно', `Операция "${getOperationLabel(form.value.operation)}" выполнена для ${selectedBookings.value.length} бронирований`)
      closeModal()
    } else {
      notificationStore.error('Ошибка', 'Не удалось выполнить операцию')
    }
  } catch (error) {
    console.error('Error performing bulk operation:', error)
    notificationStore.error('Ошибка', 'Произошла ошибка при выполнении операции')
  } finally {
    processing.value = false
  }
}

const closeModal = () => {
  modalStore.closeModal('multiple-bookings')
}

// Lifecycle
onMounted(async () => {
  if (props.bookingIds && props.bookingIds.length > 0) {
    // Find bookings in store
    selectedBookings.value = bookingStore.bookings.filter(b => 
      props.bookingIds!.includes(b.id)
    )
  }

  // Load staff data
  await staffStore.fetchStaff()
})
</script>
