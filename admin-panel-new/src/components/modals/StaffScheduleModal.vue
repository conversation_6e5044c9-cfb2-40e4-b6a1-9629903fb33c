<template>
  <div class="space-y-6">
    <!-- Staff Info Header -->
    <div class="bg-gray-50 rounded-lg p-4">
      <div class="flex items-center space-x-4">
        <div class="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center">
          <span class="text-white font-medium text-lg">
            {{ getInitials(staffMember?.name || '') }}
          </span>
        </div>
        <div>
          <h3 class="font-semibold text-gray-900">{{ staffMember?.name || 'Загрузка...' }}</h3>
          <p class="text-sm text-gray-600">{{ staffMember?.position || '' }}</p>
          <p class="text-xs text-gray-500">{{ staffMember?.department || '' }}</p>
        </div>
      </div>
    </div>

    <!-- Date Navigation -->
    <div class="flex items-center justify-between">
      <button
        @click="previousDay"
        class="btn btn-secondary btn-sm"
      >
        ← Предыдущий день
      </button>

      <div class="text-center">
        <input
          v-model="selectedDate"
          type="date"
          class="input text-center"
          @change="loadSchedule"
        />
        <p class="text-sm text-gray-600 mt-1">{{ formatDateDisplay(selectedDate) }}</p>
      </div>

      <button
        @click="nextDay"
        class="btn btn-secondary btn-sm"
      >
        Следующий день →
      </button>
    </div>

    <!-- Working Hours Info -->
    <div v-if="staffMember" class="bg-blue-50 rounded-lg p-4">
      <h4 class="font-medium text-blue-900 mb-2">Рабочие часы</h4>
      <p class="text-sm text-blue-800">
        {{ getWorkingHoursForDay(selectedDate) }}
      </p>
    </div>

    <!-- Schedule Grid -->
    <div class="border border-gray-200 rounded-lg overflow-hidden">
      <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <h4 class="font-medium text-gray-900">Расписание на день</h4>
      </div>

      <div v-if="loading" class="p-8 text-center">
        <div class="spinner w-8 h-8 mx-auto mb-4"></div>
        <p class="text-gray-500">Загрузка расписания...</p>
      </div>

      <div v-else class="max-h-96 overflow-y-auto">
        <div
          v-for="timeSlot in timeSlots"
          :key="timeSlot"
          class="flex items-center border-b border-gray-100 hover:bg-gray-50"
        >
          <div class="w-20 px-4 py-3 text-sm font-medium text-gray-600 border-r border-gray-200">
            {{ timeSlot }}
          </div>
          <div class="flex-1 px-4 py-3">
            <div v-if="getBookingForSlot(timeSlot)" class="space-y-2">
              <div
                v-for="booking in getBookingForSlot(timeSlot)"
                :key="booking.id"
                class="p-3 rounded-lg border"
                :class="getBookingStatusClass(booking.status)"
              >
                <div class="flex items-center justify-between">
                  <div>
                    <p class="font-medium text-sm">{{ booking.guest_name }}</p>
                    <p class="text-xs text-gray-600">{{ getBookingServices(booking) }}</p>
                    <p class="text-xs text-gray-500">Номер {{ booking.room_number }}</p>
                  </div>
                  <div class="text-right">
                    <p class="text-sm font-medium">{{ formatCurrency(booking.total_price) }}</p>
                    <span class="text-xs px-2 py-1 rounded-full" :class="getStatusBadgeClass(booking.status)">
                      {{ getStatusText(booking.status) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-sm text-gray-400 italic">
              Свободно
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-3 gap-4">
      <div class="bg-green-50 rounded-lg p-4 text-center">
        <p class="text-2xl font-bold text-green-600">{{ todayBookings.length }}</p>
        <p class="text-sm text-green-800">Бронирований</p>
      </div>
      <div class="bg-blue-50 rounded-lg p-4 text-center">
        <p class="text-2xl font-bold text-blue-600">{{ totalWorkingHours }}</p>
        <p class="text-sm text-blue-800">Рабочих часов</p>
      </div>
      <div class="bg-purple-50 rounded-lg p-4 text-center">
        <p class="text-2xl font-bold text-purple-600">{{ formatCurrency(todayRevenue) }}</p>
        <p class="text-sm text-purple-800">Выручка</p>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
      <button
        @click="closeModal"
        type="button"
        class="btn btn-secondary"
      >
        Закрыть
      </button>
      <button
        @click="editWorkingHours"
        type="button"
        class="btn btn-primary"
      >
        Редактировать рабочие часы
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useStaffStore, useBookingStore, useModalStore, useNotificationStore } from '@/stores'
import type { Staff, Booking } from '@/types'

// Props
interface Props {
  staffId: number
  date: string
}

const props = defineProps<Props>()

// Stores
const staffStore = useStaffStore()
const bookingStore = useBookingStore()
const modalStore = useModalStore()
const notificationStore = useNotificationStore()

// State
const loading = ref(false)
const selectedDate = ref(props.date)
const todayBookings = ref<Booking[]>([])

// Computed
const staffMember = computed(() => {
  return staffStore.staff.find(s => s.id === props.staffId)
})

const timeSlots = computed(() => {
  const slots = []
  for (let hour = 9; hour <= 21; hour++) {
    slots.push(`${hour.toString().padStart(2, '0')}:00`)
    if (hour < 21) {
      slots.push(`${hour.toString().padStart(2, '0')}:30`)
    }
  }
  return slots
})

const totalWorkingHours = computed(() => {
  if (!staffMember.value) return 0

  const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
  const dayOfWeek = dayNames[new Date(selectedDate.value).getDay()] as keyof Staff['working_hours']
  const workingHours = staffMember.value.working_hours[dayOfWeek]

  if (!workingHours?.is_working) return 0

  const start = new Date(`2000-01-01 ${workingHours.start}`)
  const end = new Date(`2000-01-01 ${workingHours.end}`)

  return Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60))
})

const todayRevenue = computed(() => {
  return todayBookings.value.reduce((sum, booking) => sum + booking.total_price, 0)
})

// Methods
const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2)
}

const formatDateDisplay = (date: string) => {
  return new Date(date).toLocaleDateString('ru-RU', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getWorkingHoursForDay = (date: string) => {
  if (!staffMember.value) return 'Не определено'

  const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
  const dayOfWeek = dayNames[new Date(date).getDay()] as keyof Staff['working_hours']
  const workingHours = staffMember.value.working_hours[dayOfWeek]

  if (!workingHours?.is_working) {
    return 'Выходной день'
  }

  return `${workingHours.start} - ${workingHours.end}`
}

const getBookingForSlot = (timeSlot: string) => {
  return todayBookings.value.filter(booking => booking.time === timeSlot)
}

const getBookingServices = (booking: Booking) => {
  return booking.services.map(s => s.name || s.service_name).join(', ')
}

const getBookingStatusClass = (status: string) => {
  switch (status) {
    case 'confirmed':
      return 'bg-green-100 border-green-200'
    case 'pending':
      return 'bg-yellow-100 border-yellow-200'
    case 'cancelled':
      return 'bg-red-100 border-red-200'
    default:
      return 'bg-gray-100 border-gray-200'
  }
}

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'confirmed':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'cancelled':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'confirmed':
      return 'Подтверждено'
    case 'pending':
      return 'Ожидает'
    case 'cancelled':
      return 'Отменено'
    default:
      return 'Неизвестно'
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('ru-RU', {
    style: 'currency',
    currency: 'RUB',
    minimumFractionDigits: 0
  }).format(amount)
}

const previousDay = () => {
  const date = new Date(selectedDate.value)
  date.setDate(date.getDate() - 1)
  selectedDate.value = date.toISOString().split('T')[0]
  loadSchedule()
}

const nextDay = () => {
  const date = new Date(selectedDate.value)
  date.setDate(date.getDate() + 1)
  selectedDate.value = date.toISOString().split('T')[0]
  loadSchedule()
}

const loadSchedule = async () => {
  loading.value = true

  try {
    // Filter bookings for the selected date and staff member
    await bookingStore.fetchBookings({
      start: selectedDate.value,
      end: selectedDate.value
    })

    todayBookings.value = bookingStore.bookings.filter(booking => 
      booking.date === selectedDate.value && 
      booking.staff_id === props.staffId
    )
  } catch (error) {
    console.error('Error loading schedule:', error)
    notificationStore.error('Ошибка', 'Не удалось загрузить расписание')
  } finally {
    loading.value = false
  }
}

const editWorkingHours = () => {
  modalStore.openStaffForm(props.staffId)
}

const closeModal = () => {
  modalStore.closeModal('staff-schedule')
}

// Lifecycle
onMounted(() => {
  loadSchedule()
})

// Watch for date changes
watch(selectedDate, () => {
  loadSchedule()
})

</script>
