<template>
  <form @submit.prevent="handleSubmit" class="space-y-6">
    <!-- Guest Information -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="label text-gray-700 mb-2">Имя гостя *</label>
        <input
          v-model="form.guest_name"
          type="text"
          class="input"
          placeholder="Введите имя гостя"
          required
        />
      </div>
      <div>
        <label class="label text-gray-700 mb-2">Номер комнаты *</label>
        <input
          v-model="form.room_number"
          type="text"
          class="input"
          placeholder="101, 205, и т.д."
          required
        />
      </div>
    </div>

    <!-- Contact Information -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="label text-gray-700 mb-2">Телефон</label>
        <input
          v-model="form.phone"
          type="tel"
          class="input"
          placeholder="+7 (999) 123-45-67"
        />
      </div>
      <div>
        <label class="label text-gray-700 mb-2">Email</label>
        <input
          v-model="form.email"
          type="email"
          class="input"
          placeholder="<EMAIL>"
        />
      </div>
    </div>

    <!-- Date and Time -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="label text-gray-700 mb-2">Дата *</label>
        <input
          v-model="form.date"
          type="date"
          class="input"
          required
        />
      </div>
      <div>
        <label class="label text-gray-700 mb-2">Время *</label>
        <select v-model="form.time" class="select" required>
          <option value="">Выберите время</option>
          <option
            v-for="timeSlot in availableTimeSlots"
            :key="timeSlot"
            :value="timeSlot"
          >
            {{ timeSlot }}
          </option>
        </select>
      </div>
    </div>

    <!-- Services Selection -->
    <div>
      <label class="label text-gray-700 mb-2">Услуги *</label>
      <div class="space-y-3">
        <div
          v-for="service in availableServices"
          :key="service.id"
          class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
        >
          <div class="flex items-center space-x-3">
            <input
              type="checkbox"
              :id="`service-${service.id}`"
              :value="service.id"
              v-model="selectedServices"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label :for="`service-${service.id}`" class="cursor-pointer">
              <div class="font-medium text-gray-900">{{ service.name }}</div>
              <div class="text-sm text-gray-500">
                {{ service.duration }} мин • {{ typeof service.category === 'object' ? service.category?.name : service.category }}
              </div>
            </label>
          </div>
          <div class="text-right">
            <div class="font-medium text-gray-900">{{ formatCurrency(service.price) }}</div>
            <div class="text-sm text-gray-500">до {{ service.max_persons }} чел.</div>
          </div>
        </div>
      </div>

      <div v-if="selectedServices.length === 0" class="text-sm text-red-600 mt-2">
        Выберите хотя бы одну услугу
      </div>
    </div>

    <!-- Staff Assignment -->
    <div>
      <label class="label text-gray-700 mb-2">Сотрудник</label>
      <select v-model="form.staff_id" class="select">
        <option value="">Автоматическое назначение</option>
        <option
          v-for="staff in availableStaff"
          :key="staff.id"
          :value="staff.id"
        >
          {{ staff.name }} - {{ staff.position }}
        </option>
      </select>
    </div>

    <!-- Number of Persons -->
    <div>
      <label class="label text-gray-700 mb-2">Количество человек *</label>
      <input
        v-model.number="form.persons"
        type="number"
        min="1"
        :max="maxPersons"
        class="input"
        required
      />
      <p v-if="maxPersons > 0" class="text-sm text-gray-500 mt-1">
        Максимум {{ maxPersons }} человек для выбранных услуг
      </p>
    </div>

    <!-- Special Requests -->
    <div>
      <label class="label text-gray-700 mb-2">Особые пожелания</label>
      <textarea
        v-model="form.special_requests"
        class="input"
        rows="3"
        placeholder="Дополнительные пожелания или комментарии..."
      ></textarea>
    </div>

    <!-- Total Price -->
    <div class="bg-gray-50 rounded-lg p-4">
      <div class="flex justify-between items-center">
        <span class="text-lg font-medium text-gray-900">Общая стоимость:</span>
        <span class="text-2xl font-bold text-primary-600">{{ formatCurrency(totalPrice) }}</span>
      </div>
      <div v-if="totalDuration > 0" class="text-sm text-gray-600 mt-1">
        Общая продолжительность: {{ totalDuration }} минут
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
      <button
        @click="closeModal"
        type="button"
        class="btn btn-secondary"
      >
        Отмена
      </button>
      <button
        type="submit"
        :disabled="!isFormValid || submitting"
        class="btn btn-primary"
      >
        <span v-if="submitting">Создание...</span>
        <span v-else>Создать бронирование</span>
      </button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useServiceStore, useStaffStore, useBookingStore, useModalStore, useNotificationStore } from '@/stores'

// Props
interface Props {
  date?: string
  time?: string
}

const props = defineProps<Props>()

// Stores
const serviceStore = useServiceStore()
const staffStore = useStaffStore()
const bookingStore = useBookingStore()
const modalStore = useModalStore()
const notificationStore = useNotificationStore()

// State
const submitting = ref(false)
const selectedServices = ref<number[]>([])

const form = ref({
  guest_name: '',
  room_number: '',
  phone: '',
  email: '',
  date: props.date || new Date().toISOString().split('T')[0],
  time: props.time || '',
  staff_id: null as number | null,
  persons: 1,
  special_requests: ''
})

// Computed
const availableServices = computed(() => {
  return serviceStore.services.filter(service => service.is_active)
})

const availableStaff = computed(() => {
  return staffStore.activeStaff
})

const availableTimeSlots = computed(() => {
  const slots = []
  for (let hour = 9; hour <= 21; hour++) {
    slots.push(`${hour.toString().padStart(2, '0')}:00`)
    if (hour < 21) {
      slots.push(`${hour.toString().padStart(2, '0')}:30`)
    }
  }
  return slots
})

const selectedServiceObjects = computed(() => {
  return availableServices.value.filter(service => 
    selectedServices.value.includes(service.id)
  )
})

const totalPrice = computed(() => {
  return selectedServiceObjects.value.reduce((sum, service) => sum + service.price, 0)
})

const totalDuration = computed(() => {
  return selectedServiceObjects.value.reduce((sum, service) => sum + service.duration, 0)
})

const maxPersons = computed(() => {
  if (selectedServiceObjects.value.length === 0) return 0
  return Math.min(...selectedServiceObjects.value.map(service => service.max_persons))
})

const isFormValid = computed(() => {
  return form.value.guest_name.trim() !== '' &&
         form.value.room_number.trim() !== '' &&
         form.value.date !== '' &&
         form.value.time !== '' &&
         selectedServices.value.length > 0 &&
         form.value.persons > 0 &&
         form.value.persons <= maxPersons.value
})

// Methods
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('ru-RU', {
    style: 'currency',
    currency: 'RUB',
    minimumFractionDigits: 0
  }).format(amount)
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  submitting.value = true

  try {
    const bookingData = {
      ...form.value,
      staff_id: form.value.staff_id || undefined,
      services: selectedServiceObjects.value.map(service => ({
        id: service.id,
        name: service.name,
        price: service.price,
        duration: service.duration,
        category: service.category
      })),
      total_price: totalPrice.value,
      total_duration: totalDuration.value,
      status: 'pending',
      notes: form.value.special_requests || ''
    }

    const success = await bookingStore.createBooking(bookingData)

    if (success) {
      notificationStore.success('Успешно', 'Бронирование создано')
      closeModal()
    } else {
      notificationStore.error('Ошибка', 'Не удалось создать бронирование')
    }
  } catch (error) {
    console.error('Error creating booking:', error)
    notificationStore.error('Ошибка', 'Произошла ошибка при создании бронирования')
  } finally {
    submitting.value = false
  }
}

const closeModal = () => {
  modalStore.closeModal('booking-form')
}

// Lifecycle
onMounted(async () => {
  // Load services and staff
  await Promise.all([
    serviceStore.fetchServices(),
    staffStore.fetchStaff()
  ])
})
</script>
