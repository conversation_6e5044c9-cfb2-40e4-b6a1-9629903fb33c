<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="border-b border-gray-200 pb-4">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-medium text-gray-900">Детали бронирования</h3>
          <p class="text-sm text-gray-500">Код: {{ booking?.code || 'N/A' }}</p>
        </div>
        <div class="flex items-center space-x-2">
          <span 
            :class="statusClasses"
            class="px-3 py-1 rounded-full text-xs font-medium"
          >
            {{ statusText }}
          </span>
        </div>
      </div>
    </div>

    <div v-if="booking" class="space-y-6">
      <!-- Guest Information -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Информация о госте</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="text-sm font-medium text-gray-700">Имя гостя</label>
            <p class="text-sm text-gray-900">{{ booking.guest_name }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-700">Номер комнаты</label>
            <p class="text-sm text-gray-900">{{ booking.room_number }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-700">Телефон</label>
            <p class="text-sm text-gray-900">{{ booking.phone || 'Не указан' }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-700">Количество человек</label>
            <p class="text-sm text-gray-900">{{ booking.persons }}</p>
          </div>
        </div>
      </div>

      <!-- Booking Details -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Детали бронирования</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="text-sm font-medium text-gray-700">Дата</label>
            <p class="text-sm text-gray-900">{{ formatDate(booking.date) }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-700">Время</label>
            <p class="text-sm text-gray-900">{{ booking.time }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-700">Сотрудник</label>
            <p class="text-sm text-gray-900">{{ booking.staff_name || 'Не назначен' }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-700">Код подтверждения</label>
            <p class="text-sm text-gray-900 font-mono">{{ booking.confirmation_code }}</p>
          </div>
        </div>
      </div>

      <!-- Services -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Услуги</h4>
        <div class="space-y-3">
          <div
            v-for="service in booking.services"
            :key="service.id"
            class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200"
          >
            <div>
              <div class="font-medium text-gray-900">{{ service.name }}</div>
              <div class="text-sm text-gray-500">
                {{ service.duration }} мин
                <span v-if="service.category"> • {{ service.category }}</span>
              </div>
            </div>
            <div class="text-right">
              <div class="font-medium text-gray-900">{{ formatCurrency(service.price) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Total -->
      <div class="bg-primary-50 rounded-lg p-4">
        <div class="flex justify-between items-center">
          <div>
            <span class="text-lg font-medium text-gray-900">Общая стоимость:</span>
            <div class="text-sm text-gray-600">Продолжительность: {{ booking.total_duration }} минут</div>
          </div>
          <span class="text-2xl font-bold text-primary-600">{{ formatCurrency(booking.total_price) }}</span>
        </div>
      </div>

      <!-- Notes -->
      <div v-if="booking.notes" class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Особые пожелания</h4>
        <p class="text-sm text-gray-700">{{ booking.notes }}</p>
      </div>

      <!-- Timestamps -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Информация о создании</h4>
        <div class="text-sm text-gray-600">
          <p>Создано: {{ formatDateTime(booking.created_at) }}</p>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-8">
      <p class="text-gray-500">Загрузка данных бронирования...</p>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
      <button
        @click="closeModal"
        type="button"
        class="btn btn-secondary"
      >
        Закрыть
      </button>
      <button
        v-if="booking && booking.status !== 'cancelled'"
        @click="openRescheduleModal"
        type="button"
        class="btn btn-primary"
      >
        Перенести
      </button>
      <button
        v-if="booking && booking.status !== 'cancelled'"
        @click="openCancelModal"
        type="button"
        class="btn btn-danger"
      >
        Отменить
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useBookingStore, useModalStore } from '@/stores'
import type { Booking } from '@/types'

// Props
interface Props {
  bookingId?: number
}

const props = defineProps<Props>()

// Stores
const bookingStore = useBookingStore()
const modalStore = useModalStore()

// State
const booking = ref<Booking | null>(null)

// Computed
const statusClasses = computed(() => {
  if (!booking.value) return ''

  switch (booking.value.status) {
    case 'confirmed':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'cancelled':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
})

const statusText = computed(() => {
  if (!booking.value) return ''

  switch (booking.value.status) {
    case 'confirmed':
      return 'Подтверждено'
    case 'pending':
      return 'Ожидает подтверждения'
    case 'cancelled':
      return 'Отменено'
    default:
      return 'Неизвестно'
  }
})

// Methods
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('ru-RU', {
    style: 'currency',
    currency: 'RUB',
    minimumFractionDigits: 0
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ru-RU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('ru-RU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const closeModal = () => {
  modalStore.closeModal('booking-details')
}

const openRescheduleModal = () => {
  if (booking.value) {
    modalStore.openModal('booking-reschedule', 'Перенести бронирование', 'BookingRescheduleModal', { bookingId: booking.value.id })
  }
}

const openCancelModal = () => {
  if (booking.value) {
    modalStore.openModal('booking-cancel', 'Отменить бронирование', 'BookingCancelModal', { bookingId: booking.value.id })
  }
}

// Lifecycle
onMounted(async () => {
  if (props.bookingId) {
    // Find booking in store or fetch it
    const existingBooking = bookingStore.bookings.find(b => b.id === props.bookingId)
    if (existingBooking) {
      booking.value = existingBooking
    } else {
      // If booking not in store, you might need to fetch it
      // For now, we'll just show loading state
      console.warn('Booking not found in store:', props.bookingId)
    }
  }
})
</script>
