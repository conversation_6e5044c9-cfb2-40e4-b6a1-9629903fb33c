<template>
  <form @submit.prevent="handleSubmit" class="space-y-6">
    <!-- Header -->
    <div class="border-b border-gray-200 pb-4">
      <h3 class="text-lg font-medium text-gray-900">Генератор отчетов</h3>
      <p class="text-sm text-gray-500">Создайте отчет с настраиваемыми параметрами</p>
    </div>

    <div class="space-y-6">
      <!-- Report Type -->
      <div>
        <label class="label text-gray-700 mb-2">Тип отчета *</label>
        <select v-model="form.reportType" class="select" required>
          <option value="">Выберите тип отчета</option>
          <option value="bookings">Отчет по бронированиям</option>
          <option value="revenue">Отчет по доходам</option>
          <option value="services">Отчет по услугам</option>
          <option value="staff">Отчет по сотрудникам</option>
          <option value="guests">Отчет по гостям</option>
          <option value="analytics">Аналитический отчет</option>
        </select>
      </div>

      <!-- Date Range -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Период отчета</h4>
        <div class="space-y-3">
          <div class="flex items-center space-x-4">
            <label class="flex items-center">
              <input
                v-model="form.dateRange"
                type="radio"
                value="today"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700">Сегодня</span>
            </label>
            <label class="flex items-center">
              <input
                v-model="form.dateRange"
                type="radio"
                value="week"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700">Эта неделя</span>
            </label>
            <label class="flex items-center">
              <input
                v-model="form.dateRange"
                type="radio"
                value="month"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700">Этот месяц</span>
            </label>
            <label class="flex items-center">
              <input
                v-model="form.dateRange"
                type="radio"
                value="custom"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700">Произвольный</span>
            </label>
          </div>

          <!-- Custom Date Range -->
          <div v-if="form.dateRange === 'custom'" class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
            <div>
              <label class="label text-gray-700 mb-2">Дата начала</label>
              <input
                v-model="form.startDate"
                type="date"
                class="input"
                required
              />
            </div>
            <div>
              <label class="label text-gray-700 mb-2">Дата окончания</label>
              <input
                v-model="form.endDate"
                type="date"
                class="input"
                :min="form.startDate"
                required
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Фильтры</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Status Filter -->
          <div v-if="form.reportType === 'bookings'">
            <label class="label text-gray-700 mb-2">Статус бронирования</label>
            <select v-model="form.status" class="select">
              <option value="">Все статусы</option>
              <option value="confirmed">Подтверждено</option>
              <option value="pending">Ожидает подтверждения</option>
              <option value="cancelled">Отменено</option>
            </select>
          </div>

          <!-- Service Category Filter -->
          <div v-if="['bookings', 'services', 'revenue'].includes(form.reportType)">
            <label class="label text-gray-700 mb-2">Категория услуг</label>
            <select v-model="form.serviceCategory" class="select">
              <option value="">Все категории</option>
              <option value="spa">СПА</option>
              <option value="massage">Массаж</option>
              <option value="beauty">Красота</option>
              <option value="wellness">Велнес</option>
            </select>
          </div>

          <!-- Staff Filter -->
          <div v-if="['bookings', 'staff'].includes(form.reportType)">
            <label class="label text-gray-700 mb-2">Сотрудник</label>
            <select v-model="form.staffId" class="select">
              <option value="">Все сотрудники</option>
              <option
                v-for="staff in availableStaff"
                :key="staff.id"
                :value="staff.id"
              >
                {{ staff.name }} - {{ staff.position }}
              </option>
            </select>
          </div>

          <!-- Room Filter -->
          <div v-if="form.reportType === 'guests'">
            <label class="label text-gray-700 mb-2">Номер комнаты</label>
            <input
              v-model="form.roomNumber"
              type="text"
              class="input"
              placeholder="Например: 101, 205"
            />
          </div>
        </div>
      </div>

      <!-- Report Options -->
      <div class="bg-blue-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Параметры отчета</h4>
        <div class="space-y-3">
          <label class="flex items-center">
            <input
              v-model="form.includeCharts"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">
              Включить графики и диаграммы
            </span>
          </label>
          <label class="flex items-center">
            <input
              v-model="form.includeDetails"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">
              Включить детальную информацию
            </span>
          </label>
          <label class="flex items-center">
            <input
              v-model="form.includeSummary"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">
              Включить сводную информацию
            </span>
          </label>
        </div>
      </div>

      <!-- Export Format -->
      <div>
        <label class="label text-gray-700 mb-2">Формат экспорта *</label>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
          <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
            <input
              v-model="form.exportFormat"
              type="radio"
              value="pdf"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              required
            />
            <span class="ml-2 text-sm text-gray-700">PDF</span>
          </label>
          <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
            <input
              v-model="form.exportFormat"
              type="radio"
              value="excel"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">Excel</span>
          </label>
          <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
            <input
              v-model="form.exportFormat"
              type="radio"
              value="csv"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">CSV</span>
          </label>
          <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
            <input
              v-model="form.exportFormat"
              type="radio"
              value="json"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">JSON</span>
          </label>
        </div>
      </div>

      <!-- Report Preview -->
      <div v-if="form.reportType" class="bg-green-50 border border-green-200 rounded-lg p-4">
        <h4 class="text-md font-medium text-green-800 mb-2">Предварительный просмотр</h4>
        <div class="text-sm text-green-700">
          <p><strong>Тип:</strong> {{ getReportTypeLabel(form.reportType) }}</p>
          <p><strong>Период:</strong> {{ getDateRangeLabel() }}</p>
          <p v-if="form.status"><strong>Статус:</strong> {{ getStatusLabel(form.status) }}</p>
          <p v-if="form.serviceCategory"><strong>Категория:</strong> {{ form.serviceCategory }}</p>
          <p><strong>Формат:</strong> {{ form.exportFormat?.toUpperCase() }}</p>
        </div>
      </div>

      <!-- Email Options -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Отправка по email</h4>
        <div class="space-y-3">
          <label class="flex items-center">
            <input
              v-model="form.sendEmail"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">
              Отправить отчет на email
            </span>
          </label>

          <div v-if="form.sendEmail" class="mt-3">
            <label class="label text-gray-700 mb-2">Email адреса</label>
            <input
              v-model="form.emailAddresses"
              type="text"
              class="input"
              placeholder="<EMAIL>, <EMAIL>"
            />
            <p class="text-xs text-gray-500 mt-1">
              Разделите несколько адресов запятыми
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
      <button
        @click="closeModal"
        type="button"
        class="btn btn-secondary"
      >
        Отмена
      </button>
      <button
        @click="previewReport"
        type="button"
        :disabled="!isFormValid"
        class="btn btn-outline"
      >
        Предварительный просмотр
      </button>
      <button
        type="submit"
        :disabled="!isFormValid || generating"
        class="btn btn-primary"
      >
        <span v-if="generating">Генерация...</span>
        <span v-else>Сгенерировать отчет</span>
      </button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useStaffStore, useModalStore, useNotificationStore } from '@/stores'

// Stores
const staffStore = useStaffStore()
const modalStore = useModalStore()
const notificationStore = useNotificationStore()

// State
const generating = ref(false)

const form = ref({
  reportType: '',
  dateRange: 'month',
  startDate: '',
  endDate: '',
  status: '',
  serviceCategory: '',
  staffId: null as number | null,
  roomNumber: '',
  includeCharts: true,
  includeDetails: true,
  includeSummary: true,
  exportFormat: 'pdf',
  sendEmail: false,
  emailAddresses: ''
})

// Computed
const availableStaff = computed(() => {
  return staffStore.activeStaff
})

const isFormValid = computed(() => {
  const baseValid = form.value.reportType !== '' && form.value.exportFormat !== ''

  if (form.value.dateRange === 'custom') {
    return baseValid && form.value.startDate !== '' && form.value.endDate !== ''
  }

  if (form.value.sendEmail) {
    return baseValid && form.value.emailAddresses.trim() !== ''
  }

  return baseValid
})

// Methods
const getReportTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    bookings: 'Отчет по бронированиям',
    revenue: 'Отчет по доходам',
    services: 'Отчет по услугам',
    staff: 'Отчет по сотрудникам',
    guests: 'Отчет по гостям',
    analytics: 'Аналитический отчет'
  }
  return labels[type] || type
}

const getDateRangeLabel = () => {
  switch (form.value.dateRange) {
    case 'today':
      return 'Сегодня'
    case 'week':
      return 'Эта неделя'
    case 'month':
      return 'Этот месяц'
    case 'custom':
      return `${form.value.startDate} - ${form.value.endDate}`
    default:
      return 'Не указан'
  }
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    confirmed: 'Подтверждено',
    pending: 'Ожидает подтверждения',
    cancelled: 'Отменено'
  }
  return labels[status] || status
}

const previewReport = () => {
  // In a real app, you'd show a preview modal or navigate to a preview page
  notificationStore.info('Информация', 'Функция предварительного просмотра будет доступна в следующей версии')
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  generating.value = true

  try {
    // In a real app, you'd call a report generation method with this data:
    // const reportData = {
    //   type: form.value.reportType,
    //   dateRange: form.value.dateRange,
    //   startDate: form.value.dateRange === 'custom' ? form.value.startDate : undefined,
    //   endDate: form.value.dateRange === 'custom' ? form.value.endDate : undefined,
    //   filters: {
    //     status: form.value.status || undefined,
    //     serviceCategory: form.value.serviceCategory || undefined,
    //     staffId: form.value.staffId || undefined,
    //     roomNumber: form.value.roomNumber || undefined
    //   },
    //   options: {
    //     includeCharts: form.value.includeCharts,
    //     includeDetails: form.value.includeDetails,
    //     includeSummary: form.value.includeSummary
    //   },
    //   exportFormat: form.value.exportFormat,
    //   email: form.value.sendEmail ? {
    //     addresses: form.value.emailAddresses.split(',').map(email => email.trim())
    //   } : undefined
    // }
    // const success = await reportStore.generateReport(reportData)

    // For now, we'll simulate success
    const success = true

    if (success) {
      notificationStore.success('Успешно', 'Отчет сгенерирован и будет доступен для скачивания')
      closeModal()
    } else {
      notificationStore.error('Ошибка', 'Не удалось сгенерировать отчет')
    }
  } catch (error) {
    console.error('Error generating report:', error)
    notificationStore.error('Ошибка', 'Произошла ошибка при генерации отчета')
  } finally {
    generating.value = false
  }
}

const closeModal = () => {
  modalStore.closeModal('report-generator')
}

// Lifecycle
onMounted(async () => {
  // Set default dates
  const today = new Date()
  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)

  form.value.startDate = firstDayOfMonth.toISOString().split('T')[0]
  form.value.endDate = today.toISOString().split('T')[0]

  // Load staff data
  await staffStore.fetchStaff()
})
</script>
