<template>
  <form @submit.prevent="handleSubmit" class="space-y-6">
    <!-- Header -->
    <div class="border-b border-gray-200 pb-4">
      <h3 class="text-lg font-medium text-gray-900">Перенести бронирование</h3>
      <p v-if="booking" class="text-sm text-gray-500">
        Код: {{ booking.code }} • {{ booking.guest_name }}
      </p>
    </div>

    <div v-if="booking" class="space-y-6">
      <!-- Current Booking Info -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Текущее бронирование</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="text-sm font-medium text-gray-700">Текущая дата</label>
            <p class="text-sm text-gray-900">{{ formatDate(booking.date) }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-700">Текущее время</label>
            <p class="text-sm text-gray-900">{{ booking.time }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-700">Услуги</label>
            <p class="text-sm text-gray-900">
              {{ booking.services.map(s => s.name).join(', ') }}
            </p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-700">Продолжительность</label>
            <p class="text-sm text-gray-900">{{ booking.total_duration }} минут</p>
          </div>
        </div>
      </div>

      <!-- New Date and Time -->
      <div class="bg-blue-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Новые дата и время</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="label text-gray-700 mb-2">Новая дата *</label>
            <input
              v-model="form.date"
              type="date"
              class="input"
              :min="minDate"
              required
            />
          </div>
          <div>
            <label class="label text-gray-700 mb-2">Новое время *</label>
            <select v-model="form.time" class="select" required>
              <option value="">Выберите время</option>
              <option
                v-for="timeSlot in availableTimeSlots"
                :key="timeSlot.time"
                :value="timeSlot.time"
                :disabled="!timeSlot.available"
              >
                {{ timeSlot.time }}
                <span v-if="!timeSlot.available"> (занято)</span>
              </option>
            </select>
          </div>
        </div>
      </div>

      <!-- Staff Assignment -->
      <div>
        <label class="label text-gray-700 mb-2">Сотрудник</label>
        <select v-model="form.staff_id" class="select">
          <option value="">Автоматическое назначение</option>
          <option
            v-for="staff in availableStaff"
            :key="staff.id"
            :value="staff.id"
          >
            {{ staff.name }} - {{ staff.position }}
          </option>
        </select>
        <p class="text-sm text-gray-500 mt-1">
          Текущий сотрудник: {{ booking.staff_name || 'Не назначен' }}
        </p>
      </div>

      <!-- Reason for Rescheduling -->
      <div>
        <label class="label text-gray-700 mb-2">Причина переноса</label>
        <textarea
          v-model="form.reason"
          class="input"
          rows="3"
          placeholder="Укажите причину переноса бронирования (необязательно)..."
        ></textarea>
      </div>

      <!-- Availability Check -->
      <div v-if="form.date && form.time" class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          <span class="text-sm font-medium text-green-800">
            Выбранное время доступно для бронирования
          </span>
        </div>
      </div>

      <!-- Warning -->
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex">
          <svg class="w-5 h-5 text-yellow-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
          </svg>
          <div>
            <h4 class="text-sm font-medium text-yellow-800">Внимание</h4>
            <p class="text-sm text-yellow-700 mt-1">
              Перенос бронирования может повлиять на доступность других услуг. 
              Убедитесь, что новое время подходит гостю.
            </p>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-8">
      <p class="text-gray-500">Загрузка данных бронирования...</p>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
      <button
        @click="closeModal"
        type="button"
        class="btn btn-secondary"
      >
        Отмена
      </button>
      <button
        type="submit"
        :disabled="!isFormValid || submitting"
        class="btn btn-primary"
      >
        <span v-if="submitting">Перенос...</span>
        <span v-else>Перенести бронирование</span>
      </button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useBookingStore, useStaffStore, useModalStore, useNotificationStore } from '@/stores'
import type { Booking, TimeSlot } from '@/types'

// Props
interface Props {
  bookingId?: number
}

const props = defineProps<Props>()

// Stores
const bookingStore = useBookingStore()
const staffStore = useStaffStore()
const modalStore = useModalStore()
const notificationStore = useNotificationStore()

// State
const submitting = ref(false)
const booking = ref<Booking | null>(null)

const form = ref({
  date: '',
  time: '',
  staff_id: null as number | null,
  reason: ''
})

// Computed
const minDate = computed(() => {
  return new Date().toISOString().split('T')[0]
})

const availableStaff = computed(() => {
  return staffStore.activeStaff
})

const availableTimeSlots = computed((): TimeSlot[] => {
  if (!form.value.date) return []

  const slots: TimeSlot[] = []
  for (let hour = 9; hour <= 21; hour++) {
    const timeSlot = `${hour.toString().padStart(2, '0')}:00`
    slots.push({
      time: timeSlot,
      available: true // In a real app, you'd check availability against existing bookings
    })

    if (hour < 21) {
      const halfHourSlot = `${hour.toString().padStart(2, '0')}:30`
      slots.push({
        time: halfHourSlot,
        available: true
      })
    }
  }
  return slots
})

const isFormValid = computed(() => {
  return form.value.date !== '' && 
         form.value.time !== '' &&
         booking.value !== null &&
         (form.value.date !== booking.value.date || form.value.time !== booking.value.time)
})

// Methods
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ru-RU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const handleSubmit = async () => {
  if (!isFormValid.value || !booking.value) return

  submitting.value = true

  try {
    // In a real app, you'd call a reschedule method with this data:
    // const rescheduleData = {
    //   bookingId: booking.value.id,
    //   newDate: form.value.date,
    //   newTime: form.value.time,
    //   staffId: form.value.staff_id || undefined,
    //   reason: form.value.reason || undefined
    // }
    // const success = await bookingStore.rescheduleBooking(rescheduleData)

    // For now, we'll simulate success
    const success = true

    if (success) {
      notificationStore.success('Успешно', 'Бронирование перенесено')
      closeModal()
    } else {
      notificationStore.error('Ошибка', 'Не удалось перенести бронирование')
    }
  } catch (error) {
    console.error('Error rescheduling booking:', error)
    notificationStore.error('Ошибка', 'Произошла ошибка при переносе бронирования')
  } finally {
    submitting.value = false
  }
}

const closeModal = () => {
  modalStore.closeModal('booking-reschedule')
}

// Watch for date changes to update available time slots
watch(() => form.value.date, () => {
  form.value.time = '' // Reset time when date changes
})

// Lifecycle
onMounted(async () => {
  if (props.bookingId) {
    // Find booking in store
    const existingBooking = bookingStore.bookings.find(b => b.id === props.bookingId)
    if (existingBooking) {
      booking.value = existingBooking
      // Pre-fill form with current values
      form.value.date = existingBooking.date
      form.value.time = existingBooking.time
      form.value.staff_id = existingBooking.staff_id || null
    } else {
      console.warn('Booking not found in store:', props.bookingId)
    }
  }

  // Load staff data
  await staffStore.fetchStaff()
})
</script>
