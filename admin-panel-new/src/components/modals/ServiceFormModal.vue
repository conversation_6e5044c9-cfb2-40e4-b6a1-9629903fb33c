<template>
  <form @submit.prevent="handleSubmit" class="space-y-6">
    <!-- Service Name -->
    <div>
      <label class="label text-gray-700 mb-2">Название услуги *</label>
      <input
        v-model="form.name"
        type="text"
        class="input"
        placeholder="Введите название услуги"
        required
      />
    </div>

    <!-- Description -->
    <div>
      <label class="label text-gray-700 mb-2">Описание</label>
      <textarea
        v-model="form.description"
        class="input"
        rows="3"
        placeholder="Описание услуги"
      ></textarea>
    </div>

    <!-- Category -->
    <div>
      <label class="label text-gray-700 mb-2">Категория *</label>
      <select v-model="form.category" class="select" required>
        <option value="">Выберите категорию</option>
        <option
          v-for="category in categories"
          :key="category.id"
          :value="category.slug"
        >
          {{ category.name }}
        </option>
      </select>
    </div>

    <!-- Price and Duration -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="label text-gray-700 mb-2">Цена (₽)</label>
        <input
          v-model.number="form.price"
          type="number"
          class="input"
          min="0"
          step="1"
          :disabled="form.is_free"
        />
      </div>
      <div>
        <label class="label text-gray-700 mb-2">Длительность (мин) *</label>
        <input
          v-model.number="form.duration"
          type="number"
          class="input"
          min="1"
          step="1"
          required
        />
      </div>
    </div>

    <!-- Options -->
    <div class="space-y-3">
      <label class="flex items-center space-x-2 cursor-pointer">
        <input
          type="checkbox"
          v-model="form.is_free"
          class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          @change="handleFreeChange"
        />
        <span class="text-sm text-gray-700">Бесплатная услуга</span>
      </label>

      <label class="flex items-center space-x-2 cursor-pointer">
        <input
          type="checkbox"
          v-model="form.requires_booking"
          class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <span class="text-sm text-gray-700">Требует предварительного бронирования</span>
      </label>

      <label class="flex items-center space-x-2 cursor-pointer">
        <input
          type="checkbox"
          v-model="form.is_active"
          class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <span class="text-sm text-gray-700">Активная услуга</span>
      </label>
    </div>

    <!-- Max Persons and Time Slot Step -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="label text-gray-700 mb-2">Максимум человек *</label>
        <input
          v-model.number="form.max_persons"
          type="number"
          class="input"
          min="1"
          step="1"
          required
        />
      </div>
      <div>
        <label class="label text-gray-700 mb-2">Шаг временного слота (мин)</label>
        <select v-model="form.time_slot_step" class="select">
          <option :value="15">15 минут</option>
          <option :value="30">30 минут</option>
          <option :value="60">60 минут</option>
        </select>
      </div>
    </div>

    <!-- Image URL -->
    <div>
      <label class="label text-gray-700 mb-2">URL изображения</label>
      <input
        v-model="form.image"
        type="url"
        class="input"
        placeholder="https://example.com/image.jpg"
      />
    </div>

    <!-- Card Width -->
    <div>
      <label class="label text-gray-700 mb-2">Размер карточки</label>
      <select v-model="form.card_width" class="select">
        <option value="half">Половина</option>
        <option value="full">Полная</option>
        <option value="third">Треть</option>
      </select>
    </div>

    <!-- Form Actions -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
      <button
        type="button"
        @click="$emit('close')"
        class="btn btn-secondary"
        :disabled="loading"
      >
        Отмена
      </button>
      <button
        type="submit"
        class="btn btn-primary"
        :disabled="loading || !isFormValid"
      >
        <span v-if="loading">{{ isEditing ? 'Сохранение...' : 'Создание...' }}</span>
        <span v-else>{{ isEditing ? 'Сохранить' : 'Создать' }}</span>
      </button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useServiceStore, useNotificationStore } from '@/stores'

interface Props {
  serviceId?: number
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

const serviceStore = useServiceStore()
const notificationStore = useNotificationStore()

const loading = ref(false)
const isEditing = computed(() => !!props.serviceId)

const form = ref({
  name: '',
  description: '',
  category: '',
  price: 0,
  duration: 30,
  is_free: false,
  requires_booking: true,
  is_active: true,
  max_persons: 1,
  time_slot_step: 30,
  image: '',
  card_width: 'half' as 'half' | 'full' | 'third'
})

const { categories } = serviceStore

const isFormValid = computed(() => {
  return form.value.name.trim() !== '' && 
         form.value.category !== '' && 
         form.value.duration > 0 && 
         form.value.max_persons > 0
})

const handleFreeChange = () => {
  if (form.value.is_free) {
    form.value.price = 0
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  loading.value = true

  try {
    const serviceData = { ...form.value }

    if (isEditing.value && props.serviceId) {
      const result = await serviceStore.updateService(props.serviceId, serviceData)
      if (result) {
        notificationStore.success('Успешно', 'Услуга обновлена')
        emit('close')
      } else {
        notificationStore.error('Ошибка', 'Не удалось обновить услугу')
      }
    } else {
      const result = await serviceStore.createService(serviceData)
      if (result) {
        notificationStore.success('Успешно', 'Услуга создана')
        emit('close')
      } else {
        notificationStore.error('Ошибка', 'Не удалось создать услугу')
      }
    }
  } catch (error) {
    console.error('Error saving service:', error)
    notificationStore.error('Ошибка', 'Произошла ошибка при сохранении услуги')
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  // Load categories if not already loaded
  if (categories.length === 0) {
    await serviceStore.fetchCategories()
  }

  // Load service data if editing
  if (isEditing.value && props.serviceId) {
    const service = serviceStore.getServiceById(props.serviceId)
    if (service) {
      form.value = {
        name: service.name,
        description: service.description || '',
        category: service.category || '',
        price: service.price,
        duration: service.duration,
        is_free: service.is_free || false,
        requires_booking: service.requires_booking !== false,
        is_active: service.is_active !== false,
        max_persons: service.max_persons || 1,
        time_slot_step: service.time_slot_step || 30,
        image: service.image || '',
        card_width: service.card_width || 'half'
      }
    }
  }
})
</script>
