<template>
  <div class="text-center">
    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
      <ExclamationTriangleIcon class="h-6 w-6 text-red-600" />
    </div>
    
    <h3 class="text-lg font-medium text-gray-900 mb-2">
      {{ title }}
    </h3>
    
    <p class="text-sm text-gray-500 mb-6">
      {{ message }}
    </p>
    
    <div class="flex space-x-3 justify-center">
      <button
        @click="$emit('close')"
        type="button"
        class="btn btn-secondary"
      >
        Отмена
      </button>
      <button
        @click="handleConfirm"
        type="button"
        class="btn btn-danger"
        :disabled="loading"
      >
        <span v-if="loading">Выполняется...</span>
        <span v-else>Подтвердить</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, h } from 'vue'

interface Props {
  title?: string
  message?: string
  onConfirm?: () => Promise<void> | void
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Подтверждение действия',
  message: 'Вы уверены, что хотите выполнить это действие?'
})

const emit = defineEmits<{
  close: []
}>()

const loading = ref(false)

const handleConfirm = async () => {
  if (props.onConfirm) {
    loading.value = true
    try {
      await props.onConfirm()
      emit('close')
    } catch (error) {
      console.error('Error in confirm action:', error)
    } finally {
      loading.value = false
    }
  } else {
    emit('close')
  }
}

const ExclamationTriangleIcon = () => h('svg', {
  class: 'h-6 w-6',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'
  })
])
</script>