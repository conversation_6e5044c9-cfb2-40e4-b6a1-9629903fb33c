<template>
  <form @submit.prevent="handleSubmit" class="space-y-6">
    <!-- Category Name -->
    <div>
      <label class="label text-gray-700 mb-2">Название категории *</label>
      <input
        v-model="form.name"
        type="text"
        class="input"
        placeholder="Введите название категории"
        required
        @input="updateSlug"
      />
    </div>

    <!-- Slug -->
    <div>
      <label class="label text-gray-700 mb-2">URL-адрес (slug) *</label>
      <input
        v-model="form.slug"
        type="text"
        class="input"
        placeholder="kategoriya-slag"
        required
        pattern="[a-z0-9-]+"
        title="Только строчные буквы, цифры и дефисы"
      />
      <p class="text-xs text-gray-500 mt-1">
        Используется в URL. Только строчные буквы, цифры и дефисы.
      </p>
    </div>

    <!-- Description -->
    <div>
      <label class="label text-gray-700 mb-2">Описание</label>
      <textarea
        v-model="form.description"
        class="input"
        rows="3"
        placeholder="Описание категории"
      ></textarea>
    </div>

    <!-- Icon -->
    <div>
      <label class="label text-gray-700 mb-2">Иконка</label>
      <select v-model="form.icon" class="select">
        <option value="bed">Кровать (bed)</option>
        <option value="waves">Волны (waves)</option>
        <option value="utensils">Столовые приборы (utensils)</option>
        <option value="plus">Плюс (plus)</option>
        <option value="star">Звезда (star)</option>
        <option value="heart">Сердце (heart)</option>
        <option value="home">Дом (home)</option>
        <option value="car">Машина (car)</option>
        <option value="gift">Подарок (gift)</option>
      </select>
    </div>

    <!-- Image URL -->
    <div>
      <label class="label text-gray-700 mb-2">URL изображения</label>
      <input
        v-model="form.image"
        type="url"
        class="input"
        placeholder="https://primer.com/izobrazhenie.jpg"
      />
    </div>

    <!-- Card Width -->
    <div>
      <label class="label text-gray-700 mb-2">Размер карточки</label>
      <select v-model="form.card_width" class="select">
        <option value="third">Треть (1/3)</option>
        <option value="half">Половина (1/2)</option>
        <option value="full">Полная ширина</option>
      </select>
    </div>

    <!-- Working Hours -->
    <div>
      <label class="label text-gray-700 mb-4">Рабочие часы</label>
      <div class="space-y-3">
        <div
          v-for="(day, dayKey) in daysOfWeek"
          :key="dayKey"
          class="flex items-center space-x-4"
        >
          <div class="w-20 text-sm text-gray-600">{{ day }}</div>
          <div class="flex items-center space-x-2">
            <input
              v-model="form.working_hours[dayKey].start"
              type="time"
              class="input text-sm"
              style="width: 120px;"
            />
            <span class="text-gray-500">—</span>
            <input
              v-model="form.working_hours[dayKey].end"
              type="time"
              class="input text-sm"
              style="width: 120px;"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Status -->
    <div>
      <label class="flex items-center space-x-2 cursor-pointer">
        <input
          type="checkbox"
          v-model="form.is_active"
          class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <span class="text-sm text-gray-700">Активная категория</span>
      </label>
    </div>

    <!-- Form Actions -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
      <button
        type="button"
        @click="$emit('close')"
        class="btn btn-secondary"
        :disabled="loading"
      >
        Отмена
      </button>
      <button
        type="submit"
        class="btn btn-primary"
        :disabled="loading || !isFormValid"
      >
        <span v-if="loading">{{ isEditing ? 'Сохранение...' : 'Создание...' }}</span>
        <span v-else>{{ isEditing ? 'Сохранить' : 'Создать' }}</span>
      </button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useServiceStore, useNotificationStore } from '@/stores'

interface Props {
  categoryId?: number
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

const serviceStore = useServiceStore()
const notificationStore = useNotificationStore()

const loading = ref(false)
const isEditing = computed(() => !!props.categoryId)

const daysOfWeek = {
  monday: 'Пн',
  tuesday: 'Вт',
  wednesday: 'Ср',
  thursday: 'Чт',
  friday: 'Пт',
  saturday: 'Сб',
  sunday: 'Вс'
}

const form = ref({
  name: '',
  slug: '',
  description: '',
  icon: 'plus',
  image: '',
  card_width: 'third' as 'third' | 'half' | 'full',
  is_active: true,
  working_hours: {
    monday: { start: '09:00', end: '18:00' },
    tuesday: { start: '09:00', end: '18:00' },
    wednesday: { start: '09:00', end: '18:00' },
    thursday: { start: '09:00', end: '18:00' },
    friday: { start: '09:00', end: '18:00' },
    saturday: { start: '10:00', end: '17:00' },
    sunday: { start: '10:00', end: '17:00' }
  }
})

const isFormValid = computed(() => {
  return form.value.name.trim() !== '' && 
         form.value.slug.trim() !== '' &&
         /^[a-z0-9-]+$/.test(form.value.slug)
})

const updateSlug = () => {
  if (!isEditing.value) {
    form.value.slug = form.value.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  loading.value = true

  try {
    const categoryData = { ...form.value }

    if (isEditing.value && props.categoryId) {
      const result = await serviceStore.updateCategory(props.categoryId, categoryData)
      if (result) {
        notificationStore.success('Успешно', 'Категория обновлена')
        emit('close')
      } else {
        notificationStore.error('Ошибка', 'Не удалось обновить категорию')
      }
    } else {
      const result = await serviceStore.createCategory(categoryData)
      if (result) {
        notificationStore.success('Успешно', 'Категория создана')
        emit('close')
      } else {
        notificationStore.error('Ошибка', 'Не удалось создать категорию')
      }
    }
  } catch (error) {
    console.error('Ошибка сохранения категории:', error)
    notificationStore.error('Ошибка', 'Произошла ошибка при сохранении категории')
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  // Загружаем данные категории при редактировании
  if (isEditing.value && props.categoryId) {
    const category = serviceStore.getCategoryById(props.categoryId)
    if (category) {
      form.value = {
        name: category.name,
        slug: category.slug,
        description: category.description || '',
        icon: category.icon || 'plus',
        image: category.image || '',
        card_width: category.card_width || 'third',
        is_active: category.is_active !== false,
        working_hours: category.working_hours || form.value.working_hours
      }
    }
  }
})
</script>
