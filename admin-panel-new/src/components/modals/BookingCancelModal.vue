<template>
  <form @submit.prevent="handleSubmit" class="space-y-6">
    <!-- Header -->
    <div class="border-b border-gray-200 pb-4">
      <h3 class="text-lg font-medium text-gray-900">Отменить бронирование</h3>
      <p v-if="booking" class="text-sm text-gray-500">
        Код: {{ booking.code }} • {{ booking.guest_name }}
      </p>
    </div>

    <div v-if="booking" class="space-y-6">
      <!-- Booking Info -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Информация о бронировании</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="text-sm font-medium text-gray-700">Дата и время</label>
            <p class="text-sm text-gray-900">{{ formatDate(booking.date) }} в {{ booking.time }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-700">Гость</label>
            <p class="text-sm text-gray-900">{{ booking.guest_name }} ({{ booking.room_number }})</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-700">Услуги</label>
            <p class="text-sm text-gray-900">
              {{ booking.services.map(s => s.name).join(', ') }}
            </p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-700">Стоимость</label>
            <p class="text-sm text-gray-900">{{ formatCurrency(booking.total_price) }}</p>
          </div>
        </div>
      </div>

      <!-- Cancellation Reason -->
      <div>
        <label class="label text-gray-700 mb-2">Причина отмены *</label>
        <select v-model="form.reason" class="select" required>
          <option value="">Выберите причину</option>
          <option value="guest_request">Просьба гостя</option>
          <option value="staff_unavailable">Недоступность сотрудника</option>
          <option value="equipment_issue">Проблемы с оборудованием</option>
          <option value="emergency">Чрезвычайная ситуация</option>
          <option value="overbooking">Перебронирование</option>
          <option value="other">Другая причина</option>
        </select>
      </div>

      <!-- Additional Details -->
      <div>
        <label class="label text-gray-700 mb-2">Дополнительные детали</label>
        <textarea
          v-model="form.details"
          class="input"
          rows="3"
          placeholder="Укажите дополнительные детали отмены (необязательно)..."
        ></textarea>
      </div>

      <!-- Refund Options -->
      <div class="bg-blue-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Возврат средств</h4>
        <div class="space-y-3">
          <label class="flex items-center">
            <input
              v-model="form.refund_type"
              type="radio"
              value="full"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">
              Полный возврат ({{ formatCurrency(booking.total_price) }})
            </span>
          </label>
          <label class="flex items-center">
            <input
              v-model="form.refund_type"
              type="radio"
              value="partial"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">
              Частичный возврат
            </span>
          </label>
          <label class="flex items-center">
            <input
              v-model="form.refund_type"
              type="radio"
              value="none"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">
              Без возврата
            </span>
          </label>
        </div>

        <!-- Partial Refund Amount -->
        <div v-if="form.refund_type === 'partial'" class="mt-3">
          <label class="label text-gray-700 mb-2">Сумма возврата</label>
          <input
            v-model.number="form.refund_amount"
            type="number"
            min="0"
            :max="booking.total_price"
            class="input"
            placeholder="Введите сумму возврата"
          />
        </div>
      </div>

      <!-- Notification Options -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Уведомления</h4>
        <div class="space-y-3">
          <label class="flex items-center">
            <input
              v-model="form.notify_guest"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">
              Уведомить гостя об отмене
            </span>
          </label>
          <label class="flex items-center">
            <input
              v-model="form.notify_staff"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">
              Уведомить назначенного сотрудника
            </span>
          </label>
        </div>
      </div>

      <!-- Warning -->
      <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
          <svg class="w-5 h-5 text-red-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
          </svg>
          <div>
            <h4 class="text-sm font-medium text-red-800">Внимание</h4>
            <p class="text-sm text-red-700 mt-1">
              Отмена бронирования необратима. Убедитесь, что все детали указаны правильно.
              {{ form.notify_guest ? 'Гость получит уведомление об отмене.' : '' }}
            </p>
          </div>
        </div>
      </div>

      <!-- Confirmation -->
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <label class="flex items-start">
          <input
            v-model="form.confirmed"
            type="checkbox"
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mt-1"
            required
          />
          <span class="ml-2 text-sm text-gray-700">
            Я подтверждаю, что хочу отменить это бронирование и понимаю, 
            что это действие нельзя отменить.
          </span>
        </label>
      </div>
    </div>

    <div v-else class="text-center py-8">
      <p class="text-gray-500">Загрузка данных бронирования...</p>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
      <button
        @click="closeModal"
        type="button"
        class="btn btn-secondary"
      >
        Отмена
      </button>
      <button
        type="submit"
        :disabled="!isFormValid || submitting"
        class="btn btn-danger"
      >
        <span v-if="submitting">Отмена бронирования...</span>
        <span v-else>Отменить бронирование</span>
      </button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useBookingStore, useModalStore, useNotificationStore } from '@/stores'
import type { Booking } from '@/types'

// Props
interface Props {
  bookingId?: number
}

const props = defineProps<Props>()

// Stores
const bookingStore = useBookingStore()
const modalStore = useModalStore()
const notificationStore = useNotificationStore()

// State
const submitting = ref(false)
const booking = ref<Booking | null>(null)

const form = ref({
  reason: '',
  details: '',
  refund_type: 'full',
  refund_amount: 0,
  notify_guest: true,
  notify_staff: true,
  confirmed: false
})

// Computed
const isFormValid = computed(() => {
  const baseValid = form.value.reason !== '' && 
                   form.value.confirmed &&
                   booking.value !== null

  if (form.value.refund_type === 'partial') {
    return baseValid && 
           form.value.refund_amount > 0 && 
           form.value.refund_amount <= (booking.value?.total_price || 0)
  }

  return baseValid
})

// Methods
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('ru-RU', {
    style: 'currency',
    currency: 'RUB',
    minimumFractionDigits: 0
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ru-RU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const handleSubmit = async () => {
  if (!isFormValid.value || !booking.value) return

  submitting.value = true

  try {
    // In a real app, you'd call a cancel method with this data:
    // const cancelData = {
    //   bookingId: booking.value.id,
    //   reason: form.value.reason,
    //   details: form.value.details || undefined,
    //   refundType: form.value.refund_type,
    //   refundAmount: form.value.refund_type === 'partial' ? form.value.refund_amount : undefined,
    //   notifyGuest: form.value.notify_guest,
    //   notifyStaff: form.value.notify_staff
    // }
    // const success = await bookingStore.cancelBooking(cancelData)

    // For now, we'll simulate success
    const success = true

    if (success) {
      notificationStore.success('Успешно', 'Бронирование отменено')
      closeModal()
    } else {
      notificationStore.error('Ошибка', 'Не удалось отменить бронирование')
    }
  } catch (error) {
    console.error('Error cancelling booking:', error)
    notificationStore.error('Ошибка', 'Произошла ошибка при отмене бронирования')
  } finally {
    submitting.value = false
  }
}

const closeModal = () => {
  modalStore.closeModal('booking-cancel')
}

// Lifecycle
onMounted(async () => {
  if (props.bookingId) {
    // Find booking in store
    const existingBooking = bookingStore.bookings.find(b => b.id === props.bookingId)
    if (existingBooking) {
      booking.value = existingBooking
      // Set default refund amount to full price
      form.value.refund_amount = existingBooking.total_price
    } else {
      console.warn('Booking not found in store:', props.bookingId)
    }
  }
})
</script>
