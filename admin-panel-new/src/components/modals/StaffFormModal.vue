<template>
  <form @submit.prevent="handleSubmit" class="space-y-6">
    <!-- Staff Name -->
    <div>
      <label class="label text-gray-700 mb-2">Имя сотрудника *</label>
      <input
        v-model="form.name"
        type="text"
        class="input"
        placeholder="Введите имя сотрудника"
        required
      />
    </div>

    <!-- Position and Department -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="label text-gray-700 mb-2">Должность *</label>
        <input
          v-model="form.position"
          type="text"
          class="input"
          placeholder="Массажист, Администратор и т.д."
          required
        />
      </div>
      <div>
        <label class="label text-gray-700 mb-2">Отдел *</label>
        <select v-model="form.department" class="select" required>
          <option value="">Выберите отдел</option>
          <option value="SPA">SPA</option>
          <option value="Housekeeping">Housekeeping</option>
          <option value="Restaurant">Ресторан</option>
          <option value="Reception">Ресепшн</option>
          <option value="Security">Безопасность</option>
          <option value="Maintenance">Техническая служба</option>
          <option value="Management">Управление</option>
        </select>
      </div>
    </div>

    <!-- Contact Information -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="label text-gray-700 mb-2">Телефон *</label>
        <input
          v-model="form.phone"
          type="tel"
          class="input"
          placeholder="+7 (999) 123-45-67"
          required
        />
      </div>
      <div>
        <label class="label text-gray-700 mb-2">Email *</label>
        <input
          v-model="form.email"
          type="email"
          class="input"
          placeholder="<EMAIL>"
          required
        />
      </div>
    </div>

    <!-- Specializations -->
    <div>
      <label class="label text-gray-700 mb-2">Специализации</label>
      <div class="space-y-2">
        <div class="flex items-center space-x-2">
          <input
            v-model="newSpecialization"
            type="text"
            class="input flex-1"
            placeholder="Добавить специализацию"
            @keyup.enter="addSpecialization"
          />
          <button
            type="button"
            @click="addSpecialization"
            class="btn btn-secondary btn-sm"
          >
            Добавить
          </button>
        </div>
        <div v-if="form.specializations.length > 0" class="flex flex-wrap gap-2">
          <span
            v-for="(spec, index) in form.specializations"
            :key="index"
            class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
          >
            {{ spec }}
            <button
              type="button"
              @click="removeSpecialization(index)"
              class="ml-2 text-blue-600 hover:text-blue-800"
            >
              ×
            </button>
          </span>
        </div>
      </div>
    </div>

    <!-- Working Hours -->
    <div>
      <label class="label text-gray-700 mb-4">Рабочие часы</label>
      <div class="space-y-3">
        <div
          v-for="(day, dayKey) in daysOfWeek"
          :key="dayKey"
          class="flex items-center space-x-4"
        >
          <div class="w-20 text-sm text-gray-600">{{ day }}</div>
          <label class="flex items-center space-x-2">
            <input
              type="checkbox"
              v-model="form.working_hours[dayKey].is_working"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm text-gray-600">Рабочий день</span>
          </label>
          <div
            v-if="form.working_hours[dayKey].is_working"
            class="flex items-center space-x-2"
          >
            <input
              v-model="form.working_hours[dayKey].start"
              type="time"
              class="input text-sm"
              style="width: 120px;"
            />
            <span class="text-gray-500">—</span>
            <input
              v-model="form.working_hours[dayKey].end"
              type="time"
              class="input text-sm"
              style="width: 120px;"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Status -->
    <div>
      <label class="flex items-center space-x-2 cursor-pointer">
        <input
          type="checkbox"
          v-model="form.is_active"
          class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <span class="text-sm text-gray-700">Активный сотрудник</span>
      </label>
    </div>

    <!-- Form Actions -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
      <button
        type="button"
        @click="$emit('close')"
        class="btn btn-secondary"
        :disabled="loading"
      >
        Отмена
      </button>
      <button
        type="submit"
        class="btn btn-primary"
        :disabled="loading || !isFormValid"
      >
        <span v-if="loading">{{ isEditing ? 'Сохранение...' : 'Создание...' }}</span>
        <span v-else>{{ isEditing ? 'Сохранить' : 'Создать' }}</span>
      </button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useStaffStore, useNotificationStore } from '@/stores'

interface Props {
  staffId?: number
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

const staffStore = useStaffStore()
const notificationStore = useNotificationStore()

const loading = ref(false)
const newSpecialization = ref('')
const isEditing = computed(() => !!props.staffId)

const daysOfWeek = {
  monday: 'Пн',
  tuesday: 'Вт',
  wednesday: 'Ср',
  thursday: 'Чт',
  friday: 'Пт',
  saturday: 'Сб',
  sunday: 'Вс'
}

const form = ref({
  name: '',
  position: '',
  department: '',
  phone: '',
  email: '',
  specializations: [] as string[],
  is_active: true,
  working_hours: {
    monday: { is_working: true, start: '09:00', end: '18:00' },
    tuesday: { is_working: true, start: '09:00', end: '18:00' },
    wednesday: { is_working: true, start: '09:00', end: '18:00' },
    thursday: { is_working: true, start: '09:00', end: '18:00' },
    friday: { is_working: true, start: '09:00', end: '18:00' },
    saturday: { is_working: false, start: '10:00', end: '17:00' },
    sunday: { is_working: false, start: '10:00', end: '17:00' }
  }
})

const isFormValid = computed(() => {
  return form.value.name.trim() !== '' && 
         form.value.position.trim() !== '' && 
         form.value.department !== '' &&
         form.value.phone.trim() !== '' &&
         form.value.email.trim() !== '' &&
         /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email)
})

const addSpecialization = () => {
  const spec = newSpecialization.value.trim()
  if (spec && !form.value.specializations.includes(spec)) {
    form.value.specializations.push(spec)
    newSpecialization.value = ''
  }
}

const removeSpecialization = (index: number) => {
  form.value.specializations.splice(index, 1)
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  loading.value = true

  try {
    const staffData = { ...form.value }

    if (isEditing.value && props.staffId) {
      const result = await staffStore.updateStaff(props.staffId, staffData)
      if (result) {
        notificationStore.success('Успешно', 'Сотрудник обновлен')
        emit('close')
      } else {
        notificationStore.error('Ошибка', 'Не удалось обновить сотрудника')
      }
    } else {
      const result = await staffStore.createStaff(staffData)
      if (result) {
        notificationStore.success('Успешно', 'Сотрудник создан')
        emit('close')
      } else {
        notificationStore.error('Ошибка', 'Не удалось создать сотрудника')
      }
    }
  } catch (error) {
    console.error('Error saving staff:', error)
    notificationStore.error('Ошибка', 'Произошла ошибка при сохранении сотрудника')
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  // Load staff data if editing
  if (isEditing.value && props.staffId) {
    const staff = staffStore.getStaffById(props.staffId)
    if (staff) {
      form.value = {
        name: staff.name,
        position: staff.position,
        department: staff.department,
        phone: staff.phone,
        email: staff.email,
        specializations: [...staff.specializations],
        is_active: staff.is_active,
        working_hours: staff.working_hours || form.value.working_hours
      }
    }
  }
})
</script>
