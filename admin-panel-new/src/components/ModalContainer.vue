<template>
  <div>
    <!-- Render all active modals -->
    <div
      v-for="(modal, id) in activeModals"
      :key="id"
      class="modal-wrapper"
    >
      <Teleport to="body">
        <Transition
          name="modal"
          appear
        >
          <div
            v-if="modal.isOpen"
            class="modal-overlay"
            @click="handleOverlayClick(id)"
          >
            <div
              class="modal-content"
              @click.stop
              :class="getModalSizeClass(id)"
            >
              <!-- Modal Header -->
              <div class="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                  {{ modal.title }}
                </h3>
                <button
                  @click="closeModal(id)"
                  class="text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-md p-1"
                >
                  <span class="sr-only">Закрыть</span>
                  <XIcon class="w-6 h-6" />
                </button>
              </div>

              <!-- Modal Body -->
              <div class="p-6">
                <!-- Dynamic component rendering -->
                <component
                  v-if="modal.component"
                  :is="getModalComponent(modal.component)"
                  v-bind="modal.props || {}"
                  @close="closeModal(id)"
                />

                <!-- Fallback content -->
                <div v-else class="text-gray-500">
                  Содержимое модального окна
                </div>
              </div>
            </div>
          </div>
        </Transition>
      </Teleport>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, h } from 'vue'
import { useModalStore } from '@/stores'

// Import modal components
import ConfirmDialog from '@/components/modals/ConfirmDialog.vue'
import ServiceFormModal from '@/components/modals/ServiceFormModal.vue'
import CategoryFormModal from '@/components/modals/CategoryFormModal.vue'
import StaffFormModal from '@/components/modals/StaffFormModal.vue'
import StaffScheduleModal from '@/components/modals/StaffScheduleModal.vue'
import BookingFormModal from '@/components/modals/BookingFormModal.vue'
import BookingDetailsModal from '@/components/modals/BookingDetailsModal.vue'
import BookingRescheduleModal from '@/components/modals/BookingRescheduleModal.vue'
import BookingCancelModal from '@/components/modals/BookingCancelModal.vue'
import ReportGeneratorModal from '@/components/modals/ReportGeneratorModal.vue'
import MultipleBookingsModal from '@/components/modals/MultipleBookingsModal.vue'

const modalStore = useModalStore()
const { modals, closeModal } = modalStore

// Get only open modals
const activeModals = computed(() => {
  const active: Record<string, any> = {}
  Object.entries(modals).forEach(([id, modal]) => {
    if (modal.isOpen) {
      active[id] = modal
    }
  })
  return active
})

// Close modal when clicking overlay (can be disabled per modal)
const handleOverlayClick = (id: string) => {
  // For now, always allow closing on overlay click
  // Later we can add a prop to disable this per modal
  closeModal(id)
}

// Get modal size class based on modal type
const getModalSizeClass = (id: string) => {
  const modal = modals[id]
  if (!modal?.component) return 'max-w-lg'

  switch (modal.component) {
    case 'BookingDetailsModal':
    case 'ServiceFormModal':
    case 'CategoryFormModal':
    case 'StaffFormModal':
    case 'BookingFormModal':
      return 'max-w-2xl'
    case 'StaffScheduleModal':
    case 'MultipleBookingsModal':
    case 'ReportGeneratorModal':
      return 'max-w-4xl'
    case 'ConfirmDialog':
      return 'max-w-md'
    default:
      return 'max-w-lg'
  }
}

// Get modal component
const getModalComponent = (componentName: string) => {
  const components: Record<string, any> = {
    ConfirmDialog,
    ServiceFormModal,
    CategoryFormModal,
    StaffFormModal,
    StaffScheduleModal,
    BookingFormModal,
    BookingDetailsModal,
    BookingRescheduleModal,
    BookingCancelModal,
    ReportGeneratorModal,
    MultipleBookingsModal
  }

  return components[componentName] || (() => h('div', 'Неизвестный компонент'))
}

// Icon component
const XIcon = () => h('svg', {
  class: 'w-6 h-6',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M6 18L18 6M6 6l12 12'
  })
])
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 z-50 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4;
}

.modal-content {
  @apply bg-white rounded-lg shadow-xl w-full max-h-[90vh] overflow-y-auto;
}

/* Modal transitions */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

.modal-enter-active .modal-content,
.modal-leave-active .modal-content {
  transition: all 0.3s ease;
}

.modal-enter-from .modal-content,
.modal-leave-to .modal-content {
  transform: scale(0.9);
}
</style>
