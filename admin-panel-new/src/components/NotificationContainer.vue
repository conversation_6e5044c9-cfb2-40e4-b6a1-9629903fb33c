<template>
  <div class="fixed top-4 right-4 z-50 space-y-4 max-w-sm w-full">
    <TransitionGroup
      name="notification"
      tag="div"
      class="space-y-4"
    >
      <div
        v-for="notification in notifications"
        :key="notification.id"
        class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm w-full"
        :class="getNotificationClasses(notification.type)"
      >
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <component :is="getIcon(notification.type)" class="w-6 h-6" />
          </div>
          <div class="ml-3 w-0 flex-1">
            <p class="text-sm font-medium" :class="getTitleClasses(notification.type)">
              {{ notification.title }}
            </p>
            <p class="mt-1 text-sm" :class="getMessageClasses(notification.type)">
              {{ notification.message }}
            </p>
            <div v-if="notification.actions && notification.actions.length > 0" class="mt-3 flex space-x-2">
              <button
                v-for="action in notification.actions"
                :key="action.label"
                @click="action.action"
                class="text-sm font-medium underline hover:no-underline"
                :class="getActionClasses(notification.type)"
              >
                {{ action.label }}
              </button>
            </div>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button
              @click="removeNotification(notification.id)"
              class="rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <span class="sr-only">Закрыть</span>
              <XIcon class="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { h } from 'vue'
import { useNotificationStore } from '@/stores'

const notificationStore = useNotificationStore()
const { notifications, removeNotification } = notificationStore

// Icon components
const CheckCircleIcon = () => h('svg', {
  class: 'w-6 h-6 text-green-400',
  fill: 'currentColor',
  viewBox: '0 0 20 20'
}, [
  h('path', {
    'fill-rule': 'evenodd',
    d: 'M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z',
    'clip-rule': 'evenodd'
  })
])

const ExclamationCircleIcon = () => h('svg', {
  class: 'w-6 h-6 text-red-400',
  fill: 'currentColor',
  viewBox: '0 0 20 20'
}, [
  h('path', {
    'fill-rule': 'evenodd',
    d: 'M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z',
    'clip-rule': 'evenodd'
  })
])

const ExclamationTriangleIcon = () => h('svg', {
  class: 'w-6 h-6 text-yellow-400',
  fill: 'currentColor',
  viewBox: '0 0 20 20'
}, [
  h('path', {
    'fill-rule': 'evenodd',
    d: 'M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z',
    'clip-rule': 'evenodd'
  })
])

const InformationCircleIcon = () => h('svg', {
  class: 'w-6 h-6 text-blue-400',
  fill: 'currentColor',
  viewBox: '0 0 20 20'
}, [
  h('path', {
    'fill-rule': 'evenodd',
    d: 'M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z',
    'clip-rule': 'evenodd'
  })
])

const XIcon = () => h('svg', {
  class: 'w-5 h-5',
  fill: 'none',
  stroke: 'currentColor',
  viewBox: '0 0 24 24'
}, [
  h('path', {
    'stroke-linecap': 'round',
    'stroke-linejoin': 'round',
    'stroke-width': '2',
    d: 'M6 18L18 6M6 6l12 12'
  })
])

const getIcon = (type: string) => {
  switch (type) {
    case 'success':
      return CheckCircleIcon
    case 'error':
      return ExclamationCircleIcon
    case 'warning':
      return ExclamationTriangleIcon
    case 'info':
      return InformationCircleIcon
    default:
      return InformationCircleIcon
  }
}

const getNotificationClasses = (type: string) => {
  switch (type) {
    case 'success':
      return 'border-l-4 border-green-400'
    case 'error':
      return 'border-l-4 border-red-400'
    case 'warning':
      return 'border-l-4 border-yellow-400'
    case 'info':
      return 'border-l-4 border-blue-400'
    default:
      return 'border-l-4 border-gray-400'
  }
}

const getTitleClasses = (type: string) => {
  switch (type) {
    case 'success':
      return 'text-green-800'
    case 'error':
      return 'text-red-800'
    case 'warning':
      return 'text-yellow-800'
    case 'info':
      return 'text-blue-800'
    default:
      return 'text-gray-800'
  }
}

const getMessageClasses = (type: string) => {
  switch (type) {
    case 'success':
      return 'text-green-700'
    case 'error':
      return 'text-red-700'
    case 'warning':
      return 'text-yellow-700'
    case 'info':
      return 'text-blue-700'
    default:
      return 'text-gray-700'
  }
}

const getActionClasses = (type: string) => {
  switch (type) {
    case 'success':
      return 'text-green-600 hover:text-green-500'
    case 'error':
      return 'text-red-600 hover:text-red-500'
    case 'warning':
      return 'text-yellow-600 hover:text-yellow-500'
    case 'info':
      return 'text-blue-600 hover:text-blue-500'
    default:
      return 'text-gray-600 hover:text-gray-500'
  }
}
</script>

<style scoped>
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>