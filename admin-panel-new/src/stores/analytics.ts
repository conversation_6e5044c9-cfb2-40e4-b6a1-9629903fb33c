import {defineStore} from 'pinia'
import {computed, ref} from 'vue'
import type {AnalyticsData, Booking, ChartData, ReportConfig} from '@/types'
import { ApiClient } from '@/utils/api'

export const useAnalyticsStore = defineStore('analytics', () => {
  // State
  const analyticsData = ref<AnalyticsData | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const dateRange = ref({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  })

  // Computed
  const revenueChartData = computed((): ChartData | null => {
    if (!analyticsData.value) return null

    return {
      labels: analyticsData.value.dailyStats.map(stat => stat.date),
      datasets: [{
        label: 'Выручка',
        data: analyticsData.value.dailyStats.map(stat => stat.revenue),
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 2
      }]
    }
  })

  const bookingsChartData = computed((): ChartData | null => {
    if (!analyticsData.value) return null

    return {
      labels: analyticsData.value.dailyStats.map(stat => stat.date),
      datasets: [{
        label: 'Бронирования',
        data: analyticsData.value.dailyStats.map(stat => stat.bookings),
        backgroundColor: 'rgba(34, 197, 94, 0.5)',
        borderColor: 'rgb(34, 197, 94)',
        borderWidth: 2
      }]
    }
  })

  const categoryRevenueChartData = computed((): ChartData | null => {
    if (!analyticsData.value) return null

    const colors = [
      'rgba(59, 130, 246, 0.8)',
      'rgba(234, 179, 8, 0.8)',
      'rgba(34, 197, 94, 0.8)',
      'rgba(239, 68, 68, 0.8)',
      'rgba(168, 85, 247, 0.8)'
    ]

    return {
      labels: analyticsData.value.revenueByCategory.map(cat => cat.category),
      datasets: [{
        label: 'Выручка по категориям',
        data: analyticsData.value.revenueByCategory.map(cat => cat.revenue),
        backgroundColor: colors,
        borderWidth: 0
      }]
    }
  })

  const statusDistributionChartData = computed((): ChartData | null => {
    if (!analyticsData.value) return null

    const colors = {
      'confirmed': 'rgba(34, 197, 94, 0.8)',
      'pending': 'rgba(234, 179, 8, 0.8)',
      'cancelled': 'rgba(239, 68, 68, 0.8)'
    }

    return {
      labels: analyticsData.value.bookingsByStatus.map(status => status.status),
      datasets: [{
        label: 'Распределение по статусам',
        data: analyticsData.value.bookingsByStatus.map(status => status.count),
        backgroundColor: analyticsData.value.bookingsByStatus.map(status => 
          colors[status.status as keyof typeof colors] || 'rgba(107, 114, 128, 0.8)'
        ),
        borderWidth: 0
      }]
    }
  })

  // Actions
  const fetchAnalytics = async (customDateRange?: { start: string; end: string }) => {
    loading.value = true
    error.value = null

    try {
      const range = customDateRange || dateRange.value
      const params = new URLSearchParams()
      params.append('start_date', range.start)
      params.append('end_date', range.end)

      const data = await ApiClient.get<AnalyticsData>(`/api/admin/analytics?${params}`)

      if (data.success && data.data) {
        analyticsData.value = data.data
      } else {
        error.value = data.error || 'Ошибка загрузки аналитики'
        // If API fails, generate mock analytics data
        generateMockAnalytics()
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка загрузки аналитики'
      console.error('Error fetching analytics:', err)
      // If API fails, generate mock analytics data
      generateMockAnalytics()
    } finally {
      loading.value = false
    }
  }

  const generateReport = async (config: ReportConfig): Promise<boolean> => {
    loading.value = true
    error.value = null

    try {
      // Fetch data for the report
      const params = new URLSearchParams()
      params.append('start_date', config.dateRange.start)
      params.append('end_date', config.dateRange.end)
      params.append('format', config.format)
      params.append('include_analytics', config.includeAnalytics.toString())
      params.append('include_cancelled', config.includeCancelled.toString())

      if (config.categories && config.categories.length > 0) {
        config.categories.forEach(cat => params.append('categories[]', cat.toString()))
      }

      if (config.staff && config.staff.length > 0) {
        config.staff.forEach(staff => params.append('staff[]', staff.toString()))
      }

      // Add report type to params
      params.append('type', config.type)

      // Используем нативный fetch для загрузки файлов (blob)
      const response = await fetch(`/api/admin/reports/generate?${params}`)

      if (response.ok) {
        // Get the file as blob
        const blob = await response.blob()

        // Create download link
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // Set filename based on type
        const extension = config.type === 'excel' ? 'xlsx' : 'pdf'
        link.download = `hotel-report-${config.dateRange.start}-${config.dateRange.end}.${extension}`

        // Trigger download
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // Clean up
        window.URL.revokeObjectURL(url)

        return true
      } else {
        const errorData = await response.json()
        error.value = errorData.error || 'Ошибка генерации отчета'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка генерации отчета'
      console.error('Error generating report:', err)
      return false
    } finally {
      loading.value = false
    }
  }


  const calculateMockAnalytics = (bookings: Booking[]): AnalyticsData => {
    const totalBookings = bookings.length
    const totalRevenue = bookings.reduce((sum, booking) => sum + booking.total_price, 0)
    const averageBookingValue = totalBookings > 0 ? totalRevenue / totalBookings : 0
    const cancelledBookings = bookings.filter(b => b.status === 'cancelled').length
    const cancellationRate = totalBookings > 0 ? (cancelledBookings / totalBookings) * 100 : 0

    // Popular services
    const serviceStats: Record<string, { count: number; revenue: number }> = {}
    bookings.forEach(booking => {
      booking.services.forEach(service => {
        if (!serviceStats[service.name]) {
          serviceStats[service.name] = { count: 0, revenue: 0 }
        }
        serviceStats[service.name].count++
        serviceStats[service.name].revenue += service.price
      })
    })

    const popularServices = Object.entries(serviceStats)
      .map(([name, stats]) => ({ name, ...stats }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)

    // Revenue by category
    const categoryStats: Record<string, { revenue: number; count: number }> = {}
    bookings.forEach(booking => {
      booking.services.forEach(service => {
        const category = service.category || 'Прочее'
        if (!categoryStats[category]) {
          categoryStats[category] = { revenue: 0, count: 0 }
        }
        categoryStats[category].revenue += service.price
        categoryStats[category].count++
      })
    })

    const revenueByCategory = Object.entries(categoryStats)
      .map(([category, stats]) => ({ category, ...stats }))

    // Bookings by status
    const statusStats: Record<string, number> = {}
    bookings.forEach(booking => {
      statusStats[booking.status] = (statusStats[booking.status] || 0) + 1
    })

    const bookingsByStatus = Object.entries(statusStats)
      .map(([status, count]) => ({
        status,
        count,
        percentage: totalBookings > 0 ? (count / totalBookings) * 100 : 0
      }))

    // Daily stats
    const dailyStats: Record<string, { bookings: number; revenue: number; cancellations: number }> = {}
    bookings.forEach(booking => {
      if (!dailyStats[booking.date]) {
        dailyStats[booking.date] = { bookings: 0, revenue: 0, cancellations: 0 }
      }
      dailyStats[booking.date].bookings++
      dailyStats[booking.date].revenue += booking.total_price
      if (booking.status === 'cancelled') {
        dailyStats[booking.date].cancellations++
      }
    })

    const dailyStatsArray = Object.entries(dailyStats)
      .map(([date, stats]) => ({ date, ...stats }))
      .sort((a, b) => a.date.localeCompare(b.date))

    // Staff performance (mock data)
    const staffPerformance = [
      { staff_id: 1, staff_name: 'Анна Петрова', bookings_count: 15, revenue: 34500, rating: 4.8 },
      { staff_id: 2, staff_name: 'Михаил Сидоров', bookings_count: 12, revenue: 18000, rating: 4.6 },
      { staff_id: 3, staff_name: 'Елена Козлова', bookings_count: 8, revenue: 14400, rating: 4.9 }
    ]

    return {
      totalBookings,
      totalRevenue,
      averageBookingValue,
      cancellationRate,
      popularServices,
      revenueByCategory,
      bookingsByStatus,
      staffPerformance,
      dailyStats: dailyStatsArray
    }
  }

  const generateMockAnalytics = () => {
    // Generate mock analytics data when API fails
    analyticsData.value = {
      totalBookings: 156,
      totalRevenue: 234500,
      averageBookingValue: 1503,
      cancellationRate: 8.5,
      popularServices: [
        { name: 'Классический массаж', count: 45, revenue: 67500 },
        { name: 'Завтрак в номер', count: 38, revenue: 19000 },
        { name: 'Персональная тренировка', count: 32, revenue: 48000 },
        { name: 'Косметические процедуры', count: 28, revenue: 42000 },
        { name: 'Сауна', count: 25, revenue: 37500 }
      ],
      revenueByCategory: [
        { category: 'SPA услуги', revenue: 125000, count: 85 },
        { category: 'Фитнес', revenue: 58000, count: 42 },
        { category: 'Питание', revenue: 35000, count: 65 },
        { category: 'Прочие услуги', revenue: 16500, count: 18 }
      ],
      bookingsByStatus: [
        { status: 'confirmed', count: 128, percentage: 82.1 },
        { status: 'pending', count: 15, percentage: 9.6 },
        { status: 'cancelled', count: 13, percentage: 8.3 }
      ],
      staffPerformance: [
        { staff_id: 1, staff_name: 'Анна Петрова', bookings_count: 45, revenue: 67500, rating: 4.8 },
        { staff_id: 2, staff_name: 'Михаил Сидоров', bookings_count: 38, revenue: 48000, rating: 4.6 },
        { staff_id: 3, staff_name: 'Елена Козлова', bookings_count: 32, revenue: 42000, rating: 4.9 },
        { staff_id: 4, staff_name: 'Дмитрий Волков', bookings_count: 28, revenue: 35000, rating: 4.7 },
        { staff_id: 5, staff_name: 'Ольга Морозова', bookings_count: 13, revenue: 16500, rating: 4.5 }
      ],
      dailyStats: generateMockDailyStats()
    }
  }

  const generateMockDailyStats = () => {
    const stats = []
    new Date();
    const range = dateRange.value
    const startDate = new Date(range.start)
    const endDate = new Date(range.end)

    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0]
      stats.push({
        date: dateStr,
        bookings: Math.floor(Math.random() * 15) + 5,
        revenue: Math.floor(Math.random() * 15000) + 5000,
        cancellations: Math.floor(Math.random() * 3)
      })
    }

    return stats
  }

  const updateDateRange = (newRange: { start: string; end: string }) => {
    dateRange.value = newRange
  }

  return {
    // State
    analyticsData,
    loading,
    error,
    dateRange,

    // Computed
    revenueChartData,
    bookingsChartData,
    categoryRevenueChartData,
    statusDistributionChartData,

    // Actions
    fetchAnalytics,
    generateReport,
    calculateMockAnalytics,
    generateMockAnalytics,
    updateDateRange
  }
})
