import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Staff, Booking } from '@/types'
import { ApiClient } from '@/utils/api'

export const useStaffStore = defineStore('staff', () => {
  // State
  const staff = ref<Staff[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const activeStaff = computed(() => {
    return staff.value.filter(member => member.is_active)
  })

  const staffByDepartment = computed(() => {
    const grouped: Record<string, Staff[]> = {}
    staff.value.forEach(member => {
      if (!grouped[member.department]) {
        grouped[member.department] = []
      }
      grouped[member.department].push(member)
    })
    return grouped
  })

  const staffBySpecialization = computed(() => {
    const grouped: Record<string, Staff[]> = {}
    staff.value.forEach(member => {
      member.specializations.forEach(spec => {
        if (!grouped[spec]) {
          grouped[spec] = []
        }
        grouped[spec].push(member)
      })
    })
    return grouped
  })

  // Actions
  const fetchStaff = async () => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.get<Staff[]>('/api/admin/staff')

      if (data.success && data.data) {
        staff.value = data.data
      } else {
        error.value = data.error || 'Ошибка загрузки персонала'
        // If API fails, use mock data as fallback
        if (staff.value.length === 0) {
          initializeMockStaff()
        }
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка загрузки персонала'
      console.error('Error fetching staff:', err)
      // If API fails, use mock data as fallback
      if (staff.value.length === 0) {
        initializeMockStaff()
      }
    } finally {
      loading.value = false
    }
  }

  const createStaff = async (staffData: Omit<Staff, 'id' | 'created_at' | 'updated_at'>) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.post<Staff>('/api/admin/staff', staffData)

      if (data.success && data.data) {
        staff.value.push(data.data)
        return data.data
      } else {
        error.value = data.error || 'Ошибка создания сотрудника'
        return null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка создания сотрудника'
      console.error('Error creating staff:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  const updateStaff = async (staffId: number, staffData: Partial<Staff>) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.put<Staff>(`/api/admin/staff/${staffId}`, staffData)

      if (data.success && data.data) {
        const index = staff.value.findIndex(s => s.id === staffId)
        if (index !== -1) {
          staff.value[index] = data.data
        }
        return data.data
      } else {
        error.value = data.error || 'Ошибка обновления сотрудника'
        return null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка обновления сотрудника'
      console.error('Error updating staff:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  const deleteStaff = async (staffId: number) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.delete<any>(`/api/admin/staff/${staffId}`)

      if (data.success) {
        staff.value = staff.value.filter(s => s.id !== staffId)
        return true
      } else {
        error.value = data.error || 'Ошибка удаления сотрудника'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка удаления сотрудника'
      console.error('Error deleting staff:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  const assignStaffToBooking = async (bookingId: number, staffId: number) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.put<any>(`/api/admin/bookings/${bookingId}/assign`, { staff_id: staffId })

      if (data.success) {
        return true
      } else {
        error.value = data.error || 'Ошибка назначения сотрудника'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка назначения сотрудника'
      console.error('Error assigning staff:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  const getStaffSchedule = async (staffId: number, date: string) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.get<Booking[]>(`/api/admin/staff/${staffId}/schedule?date=${date}`)

      if (data.success && data.data) {
        return data.data
      } else {
        error.value = data.error || 'Ошибка загрузки расписания'
        return []
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка загрузки расписания'
      console.error('Error fetching staff schedule:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  const getAvailableStaff = async (date: string, time: string, specialization?: string) => {
    loading.value = true
    error.value = null

    try {
      const params = new URLSearchParams()
      params.append('date', date)
      params.append('time', time)
      if (specialization) {
        params.append('specialization', specialization)
      }

      const data = await ApiClient.get<Staff[]>(`/api/admin/staff/available?${params}`)

      if (data.success && data.data) {
        return data.data
      } else {
        error.value = data.error || 'Ошибка загрузки доступного персонала'
        return []
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка загрузки доступного персонала'
      console.error('Error fetching available staff:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  const getStaffWorkload = async (staffId: number, dateRange: { start: string; end: string }) => {
    loading.value = true
    error.value = null

    try {
      const params = new URLSearchParams()
      params.append('start_date', dateRange.start)
      params.append('end_date', dateRange.end)

      const data = await ApiClient.get<{
        total_bookings: number
        total_hours: number
        total_revenue: number
        bookings_by_day: Array<{
          date: string
          bookings: number
          hours: number
          revenue: number
        }>
      }>(`/api/admin/staff/${staffId}/workload?${params}`)

      if (data.success && data.data) {
        return data.data
      } else {
        error.value = data.error || 'Ошибка загрузки загруженности'
        return null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка загрузки загруженности'
      console.error('Error fetching staff workload:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  const getStaffById = (staffId: number): Staff | undefined => {
    return staff.value.find(member => member.id === staffId)
  }

  const getStaffByDepartment = (department: string): Staff[] => {
    return staff.value.filter(member => member.department === department)
  }

  const getStaffBySpecialization = (specialization: string): Staff[] => {
    return staff.value.filter(member => 
      member.specializations.includes(specialization)
    )
  }

  const isStaffAvailable = (staffMember: Staff, date: string, time: string): boolean => {
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
    const dayOfWeek = dayNames[new Date(date).getDay()] as keyof Staff['working_hours']
    const workingHours = staffMember.working_hours[dayOfWeek]

    if (!workingHours.is_working) {
      return false
    }

    const timeMinutes = timeToMinutes(time)
    const startMinutes = timeToMinutes(workingHours.start)
    const endMinutes = timeToMinutes(workingHours.end)

    return timeMinutes >= startMinutes && timeMinutes <= endMinutes
  }

  const getStaffUtilization = (staffMember: Staff, bookings: Booking[]): number => {
    // Calculate utilization based on bookings assigned to this staff member
    const staffBookings = bookings.filter(booking => booking.staff_id === staffMember.id)
    const totalMinutes = staffBookings.reduce((sum, booking) => sum + booking.total_duration, 0)

    // Assuming 8-hour work day = 480 minutes
    const workDayMinutes = 480
    return Math.min((totalMinutes / workDayMinutes) * 100, 100)
  }

  // Helper functions
  const timeToMinutes = (time: string): number => {
    const [hours, minutes] = time.split(':').map(Number)
    return hours * 60 + minutes
  }

  const initializeMockStaff = () => {
    if (staff.value.length === 0) {
      staff.value = [
        {
          id: 1,
          name: 'Анна Петрова',
          email: '<EMAIL>',
          phone: '+7 (999) 123-45-67',
          position: 'Массажист',
          department: 'SPA',
          specializations: ['Классический массаж', 'Расслабляющий массаж', 'Спортивный массаж'],
          is_active: true,
          working_hours: {
            monday: { start: '09:00', end: '18:00', is_working: true },
            tuesday: { start: '09:00', end: '18:00', is_working: true },
            wednesday: { start: '09:00', end: '18:00', is_working: true },
            thursday: { start: '09:00', end: '18:00', is_working: true },
            friday: { start: '09:00', end: '18:00', is_working: true },
            saturday: { start: '10:00', end: '16:00', is_working: true },
            sunday: { start: '00:00', end: '00:00', is_working: false }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 2,
          name: 'Михаил Сидоров',
          email: '<EMAIL>',
          phone: '+7 (999) 234-56-78',
          position: 'Инструктор фитнеса',
          department: 'Фитнес',
          specializations: ['Персональные тренировки', 'Групповые занятия', 'Йога'],
          is_active: true,
          working_hours: {
            monday: { start: '08:00', end: '20:00', is_working: true },
            tuesday: { start: '08:00', end: '20:00', is_working: true },
            wednesday: { start: '08:00', end: '20:00', is_working: true },
            thursday: { start: '08:00', end: '20:00', is_working: true },
            friday: { start: '08:00', end: '20:00', is_working: true },
            saturday: { start: '09:00', end: '18:00', is_working: true },
            sunday: { start: '10:00', end: '16:00', is_working: true }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 3,
          name: 'Елена Козлова',
          email: '<EMAIL>',
          phone: '+7 (999) 345-67-89',
          position: 'Косметолог',
          department: 'SPA',
          specializations: ['Косметические процедуры', 'Уход за лицом', 'Пилинги'],
          is_active: true,
          working_hours: {
            monday: { start: '10:00', end: '19:00', is_working: true },
            tuesday: { start: '10:00', end: '19:00', is_working: true },
            wednesday: { start: '10:00', end: '19:00', is_working: true },
            thursday: { start: '10:00', end: '19:00', is_working: true },
            friday: { start: '10:00', end: '19:00', is_working: true },
            saturday: { start: '11:00', end: '17:00', is_working: true },
            sunday: { start: '00:00', end: '00:00', is_working: false }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ]
    }
  }

  return {
    // State
    staff,
    loading,
    error,

    // Computed
    activeStaff,
    staffByDepartment,
    staffBySpecialization,

    // Actions
    fetchStaff,
    createStaff,
    updateStaff,
    deleteStaff,
    assignStaffToBooking,
    getStaffSchedule,
    getAvailableStaff,
    getStaffWorkload,
    getStaffById,
    getStaffByDepartment,
    getStaffBySpecialization,
    isStaffAvailable,
    getStaffUtilization,
    initializeMockStaff
  }
})
