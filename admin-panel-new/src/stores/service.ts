import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ApiClient } from '@/utils/api'
import type { Service, Category } from '@/types'

export const useServiceStore = defineStore('service', () => {
  // State
  const services = ref<Service[]>([])
  const categories = ref<Category[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const groupedByCategory = computed(() => {
    const grouped: Record<string, Service[]> = {}
    services.value.forEach(service => {
      const category = typeof service.category === 'string' ? service.category : (service.category?.slug || 'other')
      if (!grouped[category]) {
        grouped[category] = []
      }
      grouped[category].push(service)
    })
    return grouped
  })

  const activeServices = computed(() => {
    return services.value.filter(service => service.is_active !== false)
  })

  const activeCategories = computed(() => {
    return categories.value.filter(category => category.is_active)
  })

  // Actions
  const fetchCategories = async () => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.get<Category[]>('/api/admin/categories')
      
      if (data.success && data.data) {
        categories.value = data.data
      } else {
        error.value = data.error || 'Ошибка загрузки категорий'
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка загрузки категорий'
      console.error('Error fetching categories:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchServices = async () => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.get<Service[]>('/api/services')
      
      if (data.success && data.data) {
        services.value = data.data
      } else {
        error.value = data.error || 'Ошибка загрузки услуг'
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка загрузки услуг'
      console.error('Error fetching services:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchServicesByCategory = async (categorySlug: string) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.get<Service[]>(`/api/categories/${categorySlug}/services`)
      
      if (data.success && data.data) {
        // Update services for this category
        const categoryServices = data.data.map(service => ({
          ...service,
          category: categorySlug
        }))
        
        // Remove old services for this category and add new ones
        services.value = services.value.filter(s => s.category !== categorySlug)
        services.value.push(...categoryServices)
      } else {
        error.value = data.error || 'Ошибка загрузки услуг категории'
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка загрузки услуг категории'
      console.error('Error fetching category services:', err)
    } finally {
      loading.value = false
    }
  }

  const createService = async (serviceData: Omit<Service, 'id'>) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.post<Service>('/api/admin/services', serviceData)
      
      if (data.success && data.data) {
        services.value.push(data.data)
        return data.data
      } else {
        error.value = data.error || 'Ошибка создания услуги'
        return null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка создания услуги'
      console.error('Error creating service:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  const updateService = async (serviceId: number, serviceData: Partial<Service>) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.put<Service>(`/api/admin/services/${serviceId}`, serviceData)
      
      if (data.success && data.data) {
        const index = services.value.findIndex(s => s.id === serviceId)
        if (index !== -1) {
          services.value[index] = data.data
        }
        return data.data
      } else {
        error.value = data.error || 'Ошибка обновления услуги'
        return null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка обновления услуги'
      console.error('Error updating service:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  const deleteService = async (serviceId: number) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.delete<any>(`/api/admin/services/${serviceId}`)
      
      if (data.success) {
        services.value = services.value.filter(s => s.id !== serviceId)
        return true
      } else {
        error.value = data.error || 'Ошибка удаления услуги'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка удаления услуги'
      console.error('Error deleting service:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  const createCategory = async (categoryData: Omit<Category, 'id' | 'services_count'>) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.post<Category>('/api/admin/categories', categoryData)
      
      if (data.success && data.data) {
        categories.value.push(data.data)
        return data.data
      } else {
        error.value = data.error || 'Ошибка создания категории'
        return null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка создания категории'
      console.error('Error creating category:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  const updateCategory = async (categoryId: number, categoryData: Partial<Category>) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.put<Category>(`/api/admin/categories/${categoryId}`, categoryData)
      
      if (data.success && data.data) {
        const index = categories.value.findIndex(c => c.id === categoryId)
        if (index !== -1) {
          categories.value[index] = data.data
        }
        return data.data
      } else {
        error.value = data.error || 'Ошибка обновления категории'
        return null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка обновления категории'
      console.error('Error updating category:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  const deleteCategory = async (categoryId: number) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.delete<any>(`/api/admin/categories/${categoryId}`)
      
      if (data.success) {
        categories.value = categories.value.filter(c => c.id !== categoryId)
        // Also remove services from this category
        services.value = services.value.filter(s => s.category !== getCategorySlug(categoryId))
        return true
      } else {
        error.value = data.error || 'Ошибка удаления категории'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка удаления категории'
      console.error('Error deleting category:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  const getServiceById = (serviceId: number): Service | undefined => {
    return services.value.find(service => service.id === serviceId)
  }

  const getCategoryById = (categoryId: number): Category | undefined => {
    return categories.value.find(category => category.id === categoryId)
  }

  const getCategoryBySlug = (slug: string): Category | undefined => {
    return categories.value.find(category => category.slug === slug)
  }

  const getServicesByCategory = (categorySlug: string): Service[] => {
    return services.value.filter(service => service.category === categorySlug)
  }

  // Helper functions
  const getCategorySlug = (categoryId: number): string => {
    const category = getCategoryById(categoryId)
    return category?.slug || ''
  }

  const initializeDefaultCategories = () => {
    if (categories.value.length === 0) {
      categories.value = [
        {
          id: 0,
          name: 'Все категории',
          slug: 'all',
          description: 'Все категории услуг',
          working_hours: {
            monday: { start: '09:00', end: '21:00' },
            tuesday: { start: '09:00', end: '21:00' },
            wednesday: { start: '09:00', end: '21:00' },
            thursday: { start: '09:00', end: '21:00' },
            friday: { start: '09:00', end: '21:00' },
            saturday: { start: '10:00', end: '20:00' },
            sunday: { start: '10:00', end: '20:00' }
          },
          is_active: true,
          services_count: 0
        },
        {
          id: 1,
          name: 'SPA услуги',
          slug: 'spa',
          description: 'SPA и релаксация',
          working_hours: {
            monday: { start: '09:00', end: '21:00' },
            tuesday: { start: '09:00', end: '21:00' },
            wednesday: { start: '09:00', end: '21:00' },
            thursday: { start: '09:00', end: '21:00' },
            friday: { start: '09:00', end: '21:00' },
            saturday: { start: '10:00', end: '20:00' },
            sunday: { start: '10:00', end: '20:00' }
          },
          is_active: true,
          services_count: 5
        },
        {
          id: 2,
          name: 'Питание',
          slug: 'food',
          description: 'Ресторан и кафе',
          working_hours: {
            monday: { start: '09:00', end: '21:00' },
            tuesday: { start: '09:00', end: '21:00' },
            wednesday: { start: '09:00', end: '21:00' },
            thursday: { start: '09:00', end: '21:00' },
            friday: { start: '09:00', end: '21:00' },
            saturday: { start: '10:00', end: '20:00' },
            sunday: { start: '10:00', end: '20:00' }
          },
          is_active: true,
          services_count: 8
        },
        {
          id: 3,
          name: 'Фитнес',
          slug: 'fitness',
          description: 'Спорт и фитнес',
          working_hours: {
            monday: { start: '09:00', end: '21:00' },
            tuesday: { start: '09:00', end: '21:00' },
            wednesday: { start: '09:00', end: '21:00' },
            thursday: { start: '09:00', end: '21:00' },
            friday: { start: '09:00', end: '21:00' },
            saturday: { start: '10:00', end: '20:00' },
            sunday: { start: '10:00', end: '20:00' }
          },
          is_active: true,
          services_count: 3
        }
      ]
    }
  }

  return {
    // State
    services,
    categories,
    loading,
    error,
    
    // Computed
    groupedByCategory,
    activeServices,
    activeCategories,
    
    // Actions
    fetchCategories,
    fetchServices,
    fetchServicesByCategory,
    createService,
    updateService,
    deleteService,
    createCategory,
    updateCategory,
    deleteCategory,
    getServiceById,
    getCategoryById,
    getCategoryBySlug,
    getServicesByCategory,
    initializeDefaultCategories
  }
})