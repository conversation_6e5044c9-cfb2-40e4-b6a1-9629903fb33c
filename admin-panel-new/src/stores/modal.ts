import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { ModalState } from '@/types'

export const useModalStore = defineStore('modal', () => {
  // State
  const modals = ref<Record<string, ModalState>>({})

  // Actions
  const openModal = (id: string, title: string, component?: string, props?: Record<string, any>) => {
    modals.value[id] = {
      isOpen: true,
      title,
      component,
      props
    }
  }

  const closeModal = (id: string) => {
    if (modals.value[id]) {
      modals.value[id].isOpen = false
    }
  }

  const closeAllModals = () => {
    Object.keys(modals.value).forEach(id => {
      modals.value[id].isOpen = false
    })
  }

  const isModalOpen = (id: string): boolean => {
    return modals.value[id]?.isOpen || false
  }

  const getModal = (id: string): ModalState | undefined => {
    return modals.value[id]
  }

  // Convenience methods for common modals
  const openBookingDetails = (bookingId: number) => {
    openModal('booking-details', 'Детали бронирования', 'BookingDetailsModal', { bookingId })
  }

  const openBookingReschedule = (bookingId: number) => {
    openModal('booking-reschedule', 'Перенести бронирование', 'BookingRescheduleModal', { bookingId })
  }

  const openBookingCancel = (bookingId: number) => {
    openModal('booking-cancel', 'Отменить бронирование', 'BookingCancelModal', { bookingId })
  }

  const openServiceForm = (serviceId?: number) => {
    openModal('service-form', serviceId ? 'Редактировать услугу' : 'Создать услугу', 'ServiceFormModal', { serviceId })
  }

  const openCategoryForm = (categoryId?: number) => {
    openModal('category-form', categoryId ? 'Редактировать категорию' : 'Создать категорию', 'CategoryFormModal', { categoryId })
  }

  const openStaffForm = (staffId?: number) => {
    openModal('staff-form', staffId ? 'Редактировать сотрудника' : 'Добавить сотрудника', 'StaffFormModal', { staffId })
  }

  const openStaffSchedule = (staffId: number, date: string) => {
    openModal('staff-schedule', 'Расписание сотрудника', 'StaffScheduleModal', { staffId, date })
  }

  const openReportGenerator = () => {
    openModal('report-generator', 'Генерация отчета', 'ReportGeneratorModal')
  }

  const openMultipleBookings = (date: string, time: string, bookings: any[]) => {
    openModal('multiple-bookings', 'Несколько бронирований', 'MultipleBookingsModal', { date, time, bookings })
  }

  const openBookingForm = (date?: string, time?: string) => {
    openModal('booking-form', 'Создать бронирование', 'BookingFormModal', { date, time })
  }

  const openConfirmDialog = (title: string, message: string, onConfirm: () => void, onCancel?: () => void) => {
    openModal('confirm-dialog', title, 'ConfirmDialog', { message, onConfirm, onCancel })
  }

  return {
    // State
    modals,

    // Actions
    openModal,
    closeModal,
    closeAllModals,
    isModalOpen,
    getModal,

    // Convenience methods
    openBookingDetails,
    openBookingReschedule,
    openBookingCancel,
    openServiceForm,
    openCategoryForm,
    openStaffForm,
    openStaffSchedule,
    openReportGenerator,
    openMultipleBookings,
    openBookingForm,
    openConfirmDialog
  }
})
