import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  Booking, 
  FilterOptions, 
  DragDropEvent,
  CalendarEvent 
} from '@/types'
import { ApiClient } from '@/utils/api'

export const useBookingStore = defineStore('booking', () => {
  // State
  const bookings = ref<Booking[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const filters = ref<FilterOptions>({
    category: undefined,
    date: '',
    status: undefined,
    staff: undefined,
    search: ''
  })

  // Computed
  const filteredBookings = computed(() => {
    let result = [...bookings.value]

    // Filter by category
    if (filters.value.category && filters.value.category !== 0) {
      result = result.filter(booking => 
        booking.services.some(service => 
          service.category === filters.value.category ||
          service.category === getCategorySlug(filters.value.category as number)
        )
      )
    }

    // Filter by date
    if (filters.value.date) {
      result = result.filter(booking => booking.date === filters.value.date)
    }

    // Filter by date range
    if (filters.value.dateRange) {
      result = result.filter(booking => 
        booking.date >= filters.value.dateRange!.start &&
        booking.date <= filters.value.dateRange!.end
      )
    }

    // Filter by status
    if (filters.value.status) {
      result = result.filter(booking => booking.status === filters.value.status)
    }

    // Filter by staff
    if (filters.value.staff) {
      result = result.filter(booking => booking.staff_id === filters.value.staff)
    }

    // Filter by search
    if (filters.value.search) {
      const searchTerm = filters.value.search.toLowerCase()
      result = result.filter(booking =>
        booking.guest_name.toLowerCase().includes(searchTerm) ||
        booking.room_number.toLowerCase().includes(searchTerm) ||
        booking.phone.includes(searchTerm) ||
        booking.code.toLowerCase().includes(searchTerm) ||
        booking.services.some(service => 
          service.name.toLowerCase().includes(searchTerm)
        )
      )
    }

    return result
  })

  const bookingsByDate = computed(() => {
    const grouped: Record<string, Booking[]> = {}
    filteredBookings.value.forEach(booking => {
      if (!grouped[booking.date]) {
        grouped[booking.date] = []
      }
      grouped[booking.date].push(booking)
    })
    return grouped
  })

  const bookingsByTimeSlot = computed(() => {
    const grouped: Record<string, Booking[]> = {}
    filteredBookings.value.forEach(booking => {
      const key = `${booking.date}-${booking.time}`
      if (!grouped[key]) {
        grouped[key] = []
      }
      grouped[key].push(booking)
    })
    return grouped
  })

  const calendarEvents = computed((): CalendarEvent[] => {
    return filteredBookings.value.map(booking => ({
      id: booking.id.toString(),
      title: `${booking.guest_name} - ${booking.services[0]?.name || 'Услуга'}`,
      start: `${booking.date}T${booking.time}`,
      end: calculateEndTime(booking.date, booking.time, booking.total_duration),
      backgroundColor: getServiceCategoryColor(booking.services[0]?.category || ''),
      borderColor: getServiceCategoryColor(booking.services[0]?.category || ''),
      textColor: '#ffffff',
      extendedProps: {
        booking,
        category: booking.services[0]?.category || '',
        status: booking.status
      }
    }))
  })

  // Actions
  const fetchBookings = async (dateRange?: { start: string; end: string }) => {
    loading.value = true
    error.value = null
    
    try {
      console.log('Fetching bookings for date range:', dateRange)
      
      let params: any = {}
      
      if (dateRange) {
        params.start_date = dateRange.start
        params.end_date = dateRange.end
      } else if (filters.value.date) {
        params.date = filters.value.date
      }
      
      if (filters.value.category) {
        params.category = filters.value.category
      }
      
      console.log('API request params:', params)
      
      // Формируем URL с параметрами
      const queryParams = new URLSearchParams()
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
          queryParams.append(key, params[key].toString())
        }
      })
      
      const url = `/api/admin/bookings${queryParams.toString() ? '?' + queryParams.toString() : ''}`
      console.log('API request URL:', url)
      
      const data = await ApiClient.get<any>(url)
      console.log('API response:', data)

      if (data.success && data.data) {
        // Преобразуем данные с сервера в формат Booking
        let rawBookings = data.data
        
        // Обработка пагинированного ответа
        if (data.data.data && Array.isArray(data.data.data)) {
          rawBookings = data.data.data
        } else if (Array.isArray(data.data)) {
          rawBookings = data.data
        } else {
          rawBookings = []
        }
        
        console.log('Raw bookings data:', rawBookings)
        
        const transformedBookings = Array.isArray(rawBookings) ? rawBookings.map((booking: any) => {
          console.log('Processing booking:', booking)
          
          // Обработка даты
          let bookingDate = ''
          if (booking.booking_date) {
            bookingDate = booking.booking_date.includes('T') 
              ? booking.booking_date.split('T')[0] 
              : booking.booking_date
          } else if (booking.date) {
            bookingDate = booking.date.includes('T') 
              ? booking.date.split('T')[0] 
              : booking.date
          } else {
            bookingDate = new Date().toISOString().split('T')[0]
          }
          
          // Обработка времени
          let bookingTime = booking.start_time || booking.time || '00:00'
          if (bookingTime.includes(':')) {
            bookingTime = bookingTime.substring(0, 5) // Обрезаем до HH:MM
          }
          
          const transformedBooking = {
            id: booking.id,
            code: booking.id.toString(),
            confirmation_code: booking.confirmation_code || booking.id.toString(),
            services: [{
              id: booking.service?.id || 0,
              name: booking.service?.name || 'Неизвестная услуга',
              service_name: booking.service?.name || 'Неизвестная услуга',
              price: parseFloat(booking.total_price || booking.price || '0'),
              duration: booking.service?.duration || booking.duration || 60,
              category: booking.service?.category_id?.toString() || booking.category || 'comfort'
            }],
            date: bookingDate,
            time: bookingTime,
            persons: booking.persons || 1,
            guest_name: booking.client_name || booking.guest_name || booking.user?.name || 'Неизвестный клиент',
            room_number: booking.room_number || '',
            phone: booking.client_phone || booking.phone || booking.user?.phone || '',
            notes: booking.notes || '',
            total_price: parseFloat(booking.total_price || booking.price || '0'),
            total_duration: booking.service?.duration || booking.duration || 60,
            status: (booking.status || 'pending') as 'confirmed' | 'pending' | 'cancelled',
            created_at: booking.created_at,
            staff_id: booking.staff_id,
            staff_name: booking.staff?.name || booking.staff_name
          }
          
          console.log('Transformed booking:', transformedBooking)
          return transformedBooking
        }) : []
        
        console.log('Final transformed bookings:', transformedBookings)
        console.log('Setting bookings.value to:', transformedBookings)
        bookings.value = transformedBookings
        
        // Дополнительная проверка
        console.log('bookings.value after assignment:', bookings.value)
        console.log('filteredBookings.value:', filteredBookings.value)
      } else {
        console.log('No data received or request failed')
        bookings.value = []
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка загрузки бронирований'
      console.error('Error fetching bookings:', err)
      bookings.value = []
    } finally {
      loading.value = false
    }
  }

  const rescheduleBooking = async (bookingId: number, newDate: string, newTime: string) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.put<any>(`/api/admin/bookings/${bookingId}`, {
        action: 'reschedule',
        date: newDate,
        time: newTime
      })

      if (data.success) {
        // Update local booking
        const booking = bookings.value.find(b => b.id === bookingId)
        if (booking) {
          booking.date = newDate
          booking.time = newTime
        }
        return true
      } else {
        error.value = data.error || 'Ошибка переноса бронирования'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка переноса бронирования'
      console.error('Error rescheduling booking:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  const cancelBooking = async (bookingId: number, reason: string) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.put<any>(`/api/admin/bookings/${bookingId}`, {
        action: 'cancel',
        reason
      })

      if (data.success) {
        // Update local booking
        const booking = bookings.value.find(b => b.id === bookingId)
        if (booking) {
          booking.status = 'cancelled'
        }
        return true
      } else {
        error.value = data.error || 'Ошибка отмены бронирования'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка отмены бронирования'
      console.error('Error cancelling booking:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  const handleDragDrop = async (event: DragDropEvent) => {
    return await rescheduleBooking(
      event.booking.id,
      event.newDate,
      event.newTime
    )
  }

  const getBookingsForSlot = (date: string, time: string): Booking[] => {
    const key = `${date}-${time}`
    return bookingsByTimeSlot.value[key] || []
  }

  const updateFilters = (newFilters: Partial<FilterOptions>) => {
    // Если устанавливается dateRange, сбрасываем date фильтр
    if (newFilters.dateRange) {
      filters.value = { ...filters.value, ...newFilters, date: undefined }
    } else {
      filters.value = { ...filters.value, ...newFilters }
    }
    console.log('Filters updated:', filters.value)
  }

  const clearFilters = () => {
    filters.value = {
      category: undefined,
      date: new Date().toISOString().split('T')[0],
      status: undefined,
      staff: undefined,
      search: ''
    }
  }

  const createBooking = async (bookingData: Omit<Booking, 'id' | 'code' | 'confirmation_code' | 'created_at' | 'status'>) => {
    loading.value = true
    error.value = null

    try {
      const data = await ApiClient.post<Booking>('/api/admin/bookings', bookingData)

      if (data.success && data.data) {
        // Add new booking to local state
        bookings.value.push(data.data)
        return data.data
      } else {
        error.value = data.error || 'Ошибка создания бронирования'
        return null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка создания бронирования'
      console.error('Error creating booking:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  // Helper functions
  // const generateDateRange = (start: string, end: string): string[] => {
  //   const dates: string[] = []
  //   const startDate = new Date(start)
  //   const endDate = new Date(end)

  //   for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
  //     dates.push(d.toISOString().split('T')[0])
  //   }

  //   return dates
  // }

  const calculateEndTime = (date: string, time: string, duration: number): string => {
    const startTime = new Date(`${date}T${time}`)
    const endTime = new Date(startTime.getTime() + duration * 60000)
    return endTime.toISOString()
  }

  const getServiceCategoryColor = (category: string): string => {
    const colors: Record<string, string> = {
      'spa': '#0ea5e9',
      'food': '#eab308',
      'fitness': '#22c55e',
      'comfort': '#6b7280'
    }
    return colors[category] || colors.comfort
  }

  const getCategorySlug = (categoryId: number): string => {
    const slugs: Record<number, string> = {
      1: 'spa',
      2: 'food',
      3: 'fitness',
      4: 'comfort'
    }
    return slugs[categoryId] || 'comfort'
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    bookings,
    loading,
    error,
    filters,

    // Computed
    filteredBookings,
    bookingsByDate,
    bookingsByTimeSlot,
    calendarEvents,

    // Actions
    fetchBookings,
    createBooking,
    rescheduleBooking,
    cancelBooking,
    handleDragDrop,
    getBookingsForSlot,
    updateFilters,
    clearFilters,
    clearError,
    getCategorySlug,
    getServiceCategoryColor,
    calculateEndTime
  }
})
