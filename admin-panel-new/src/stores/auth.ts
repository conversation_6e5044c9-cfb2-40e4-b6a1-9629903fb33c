import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ApiClient } from '@/utils/api'

export interface User {
  id: number
  name: string
  email: string
  role: string
}

export interface LoginCredentials {
  email: string
  password: string
}

interface LoginResponse {
  success: boolean
  message?: string | null
}

interface AuthData {
  token: string
  user: User
}

interface AuthApiResponse {
  success: boolean
  data?: AuthData
  error?: string
  message?: string
}

export const useAuthStore = defineStore('auth', () => {
  // Состояние
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Вычисляемые свойства
  const isAuthenticated = computed(() => {
    return !!token.value && !!user.value
  })

  const isAdmin = computed(() => {
    return user.value?.role === 'admin'
  })

  // Действия
  const login = async (credentials: LoginCredentials): Promise<LoginResponse> => {
    loading.value = true
    error.value = null

    try {
      const response = await ApiClient.post('/api/admin/login', credentials) as AuthApiResponse
      
      if (response.success && response.data) {
        token.value = response.data.token
        user.value = response.data.user
        
        // Сохраняем данные в localStorage с временной меткой
        const loginTime = Date.now()
        localStorage.setItem('authToken', response.data.token)
        localStorage.setItem('authUser', JSON.stringify(response.data.user))
        localStorage.setItem('authLoginTime', loginTime.toString())
        
        // Устанавливаем токен в ApiClient для последующих запросов
        ApiClient.setAuthToken(response.data.token)
        
        return { success: true }
      } else {
        error.value = response.error || response.message || 'Ошибка авторизации'
        return { success: false, message: error.value }
      }
    } catch (err: any) {
      error.value = err.message || 'Ошибка сети'
      return { success: false, message: error.value }
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    user.value = null
    token.value = null
    error.value = null
    
    // Удаляем все данные аутентификации из localStorage
    localStorage.removeItem('authToken')
    localStorage.removeItem('authUser')
    localStorage.removeItem('authLoginTime')
    
    // Очищаем токен в ApiClient
    ApiClient.clearAuthToken()
  }

  const isTokenExpired = (): boolean => {
    const loginTime = localStorage.getItem('authLoginTime')
    if (!loginTime) return true
    
    const loginTimestamp = parseInt(loginTime)
    const currentTime = Date.now()
    const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000 // 30 дней в миллисекундах
    
    return (currentTime - loginTimestamp) > thirtyDaysInMs
  }

  const initializeAuth = () => {
    const storedToken = localStorage.getItem('authToken')
    const userData = localStorage.getItem('authUser')
    const loginTime = localStorage.getItem('authLoginTime')

    if (storedToken && userData && loginTime) {
      try {
        // Проверяем, не истек ли токен (30 дней)
        const currentTime = Date.now()
        const loginTimestamp = parseInt(loginTime)

        // Проверяем валидность timestamp
        if (isNaN(loginTimestamp)) {
          logout()
          return
        }

        const tokenAge = currentTime - loginTimestamp
        const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000 // 30 дней в миллисекундах

        if (tokenAge > thirtyDaysInMs) {
          // Токен истек, очищаем данные
          logout()
          return
        }

        // Парсим пользователя
        const parsedUser = JSON.parse(userData)

        token.value = storedToken
        user.value = parsedUser
        ApiClient.setAuthToken(storedToken)
      } catch (error) {
        // Ошибка парсинга JSON или другая ошибка
        logout()
      }
    } else {
      // Токен истек или отсутствует
      logout()
    }
  }

  const checkAuth = async () => {
    if (!token.value || isTokenExpired()) {
      logout()
      return false
    }

    try {
      // Устанавливаем токен в ApiClient
      ApiClient.setAuthToken(token.value)
      
      // Проверяем валидность токена
      const response = await ApiClient.get('/api/admin/me') as { success: boolean; data?: User }
      
      if (response.success && response.data) {
        user.value = response.data
        return true
      } else {
        // Токен недействителен
        logout()
        return false
      }
    } catch (err) {
      // Ошибка при проверке токена
      logout()
      return false
    }
  }

  const clearError = () => {
    error.value = null
  }

  // Автоматическая инициализация при создании store
  initializeAuth()

  return {
    // Состояние
    user,
    token,
    loading,
    error,

    // Вычисляемые свойства
    isAuthenticated,
    isAdmin,

    // Действия
    login,
    logout,
    checkAuth,
    clearError,
    initializeAuth,
    isTokenExpired
  }
})