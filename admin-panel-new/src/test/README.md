# Frontend UI Testing Documentation

## Overview

This document describes the comprehensive UI testing setup for the Hotel Admin Panel frontend application. The testing framework uses **Vitest** with **Vue Test Utils** to provide thorough coverage of all frontend components, stores, and user workflows.

## Testing Framework

### Technologies Used
- **Vitest**: Fast unit test framework built on top of Vite
- **Vue Test Utils**: Official testing utilities for Vue.js 3
- **Happy DOM**: Lightweight DOM implementation for testing
- **Pinia**: State management testing with proper mocking

### Test Structure

```
src/test/
├── setup.ts                    # Global test configuration
├── pages/                      # Page component tests
│   ├── Calendar.test.ts        # Calendar functionality tests
│   └── Dashboard.test.ts       # Dashboard analytics tests
├── components/                 # Component tests
│   └── modals/
│       └── BookingFormModal.test.ts
├── stores/                     # Pinia store tests
│   └── booking.test.ts         # Booking store logic tests
└── README.md                   # This documentation
```

## Test Categories

### 1. Component Tests

#### Calendar Component (`src/test/pages/Calendar.test.ts`)
- **Component Rendering**: Verifies all UI elements render correctly
- **View Selection**: Tests day/week/month view switching
- **Date Range Calculation**: Validates date range logic for different views
- **Navigation**: Tests previous/next/today navigation
- **Filters**: Tests date, category, and status filtering
- **Booking Interactions**: Tests drag-and-drop and cell click interactions
- **Date Formatting**: Validates Russian locale date formatting
- **Loading States**: Tests loading overlay display
- **Time Slots**: Validates business hours time slot generation

#### Dashboard Component (`src/test/pages/Dashboard.test.ts`)
- **Component Rendering**: Tests header and metrics display
- **Analytics Data Display**: Validates analytics data presentation
- **Recent Activity**: Tests recent bookings display
- **Quick Actions**: Tests action button functionality
- **Loading States**: Tests loading states for different data sources
- **Data Fetching**: Validates store method calls on mount
- **Charts and Visualizations**: Tests chart container rendering
- **Responsive Design**: Validates responsive CSS classes
- **Error Handling**: Tests graceful error handling
- **Date Range Filtering**: Tests date range selector functionality

#### BookingFormModal Component (`src/test/components/modals/BookingFormModal.test.ts`)
- **Component Rendering**: Tests modal and form field rendering
- **Form Validation**: Comprehensive validation testing for all fields
- **Service Selection**: Tests service dropdown and staff filtering
- **Date and Time Handling**: Tests date/time validation and business rules
- **Form Submission**: Tests successful and failed form submissions
- **Modal Controls**: Tests close, cancel, and escape key functionality
- **Loading States**: Tests loading indicators during operations
- **Accessibility**: Tests ARIA labels, focus management, and keyboard navigation

### 2. Store Tests

#### Booking Store (`src/test/stores/booking.test.ts`)
- **Initial State**: Validates default store state
- **Computed Properties**: Tests filtered bookings, grouping, and calendar events
- **Actions**: Tests all CRUD operations (create, read, update, delete)
- **API Integration**: Tests API client calls with proper parameters
- **Error Handling**: Tests error states and recovery
- **Helper Functions**: Tests utility functions for dates, colors, and categories
- **Filter Management**: Tests filter updates and clearing

### 3. Integration Tests

Integration tests cover complete user workflows:
- **Booking Creation Workflow**: From calendar click to form submission
- **Booking Rescheduling**: Drag-and-drop functionality end-to-end
- **Dashboard Analytics**: Data loading and display workflow
- **Filter and Search**: Multi-filter application and results

## Test Configuration

### Vitest Configuration (`vite.config.ts`)
```typescript
test: {
  globals: true,
  environment: 'happy-dom',
  setupFiles: ['./src/test/setup.ts'],
  include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
  coverage: {
    provider: 'v8',
    reporter: ['text', 'json', 'html'],
  }
}
```

### Global Setup (`src/test/setup.ts`)
- Vue Test Utils configuration with Pinia
- Global mocks for browser APIs (matchMedia, IntersectionObserver, etc.)
- Fetch and storage mocking
- Console method mocking for cleaner test output

## Running Tests

### Available Scripts
```bash
# Run tests in watch mode
npm run test

# Run tests once with coverage
npm run test:run

# Run tests with UI interface
npm run test:ui

# Run tests with coverage report
npm run test:coverage
```

### Test Execution
- **Watch Mode**: Automatically reruns tests when files change
- **Coverage**: Generates detailed coverage reports in HTML and text format
- **UI Mode**: Provides a web interface for test exploration and debugging

## Mocking Strategy

### Store Mocking
All Pinia stores are mocked using Vitest's `vi.mock()` with factory functions:
```typescript
vi.mock('@/stores', () => ({
  useBookingStore: vi.fn(),
  useModalStore: vi.fn(),
  useNotificationStore: vi.fn(),
}))
```

### API Mocking
API client is mocked to simulate different response scenarios:
```typescript
vi.mock('@/utils/api', () => ({
  ApiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  }
}))
```

### Browser API Mocking
Browser APIs are mocked in the global setup to prevent test failures:
- `window.matchMedia` for responsive components
- `IntersectionObserver` for scroll-based components
- `ResizeObserver` for size-aware components
- `localStorage` and `sessionStorage` for persistence

## Test Coverage Goals

### Current Coverage Areas
- ✅ Calendar component functionality
- ✅ Dashboard analytics display
- ✅ Booking form validation and submission
- ✅ Booking store state management
- ✅ Error handling and loading states
- ✅ Responsive design validation
- ✅ Accessibility features

### Planned Coverage Extensions
- 🔄 Services management components
- 🔄 Staff management components
- 🔄 Reports generation components
- 🔄 Authentication flows
- 🔄 Navigation and routing
- 🔄 Additional modal components

## Best Practices

### Test Organization
1. **Describe blocks**: Group related tests logically
2. **Descriptive names**: Use clear, specific test descriptions
3. **Setup/Teardown**: Use `beforeEach` for consistent test state
4. **Isolation**: Each test should be independent

### Mocking Guidelines
1. **Mock external dependencies**: APIs, stores, browser APIs
2. **Preserve component logic**: Don't mock the component under test
3. **Realistic data**: Use representative test data
4. **Error scenarios**: Test both success and failure cases

### Assertion Strategies
1. **User-centric**: Test what users see and interact with
2. **Behavior over implementation**: Test outcomes, not internal details
3. **Accessibility**: Include accessibility assertions
4. **Edge cases**: Test boundary conditions and error states

## Debugging Tests

### Common Issues
1. **Mock setup**: Ensure mocks are properly configured before component mounting
2. **Async operations**: Use `await` for async operations and `nextTick()` for DOM updates
3. **DOM queries**: Use appropriate selectors for reliable element finding
4. **Store state**: Verify mock store state matches component expectations

### Debugging Tools
1. **Test UI**: Use `npm run test:ui` for visual debugging
2. **Console logging**: Add temporary logs in tests for debugging
3. **Component inspection**: Use `wrapper.html()` to inspect rendered output
4. **Coverage reports**: Identify untested code paths

## Maintenance

### Adding New Tests
1. Create test files following the naming convention: `*.test.ts`
2. Import necessary testing utilities and the component/store under test
3. Set up appropriate mocks in `beforeEach`
4. Write descriptive test cases covering all functionality
5. Update this documentation with new test coverage

### Updating Existing Tests
1. Keep tests in sync with component changes
2. Update mock data when API contracts change
3. Refactor tests when component structure changes
4. Maintain test performance and reliability

### Performance Considerations
1. **Selective testing**: Use `test.only()` for focused development
2. **Parallel execution**: Vitest runs tests in parallel by default
3. **Mock optimization**: Keep mocks lightweight and focused
4. **Test data**: Use minimal but representative test data

## Continuous Integration

### CI/CD Integration
Tests are designed to run in CI/CD pipelines with:
- Deterministic results (no flaky tests)
- Fast execution times
- Clear failure reporting
- Coverage threshold enforcement

### Quality Gates
- Minimum test coverage: 80%
- All tests must pass before deployment
- No skipped tests in production builds
- Performance regression detection

## Conclusion

This comprehensive testing setup ensures the reliability, maintainability, and quality of the Hotel Admin Panel frontend. The tests cover critical user workflows, edge cases, and accessibility requirements, providing confidence in the application's functionality across different scenarios and user interactions.

For questions or contributions to the testing suite, please refer to the development team or create an issue in the project repository.