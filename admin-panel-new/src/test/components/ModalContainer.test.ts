import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import ModalContainer from '@/components/ModalContainer.vue'

// Мокаем стор модальных окон
vi.mock('@/stores', () => ({
  useModalStore: vi.fn(),
  useNotificationStore: vi.fn(),
  useServiceStore: vi.fn(),
  useStaffStore: vi.fn(),
  useBookingStore: vi.fn()
}))

// Мокаем иконки
vi.mock('~icons/lucide/x', () => ({ default: { name: 'XIcon' } }))

// Мокаем Teleport для тестирования
vi.mock('vue', async () => {
  const actual = await vi.importActual('vue')
  return {
    ...actual,
    Teleport: {
      name: 'Teleport',
      props: ['to'],
      template: '<div><slot /></div>'
    }
  }
})

describe('ModalContainer.vue', () => {
  let wrapper: any
  let mockModalStore: any

  beforeEach(async () => {
    setActivePinia(createPinia())

    // Мок стора модальных окон
    mockModalStore = {
      modals: {
        'modal-1': {
          id: 'modal-1',
          isOpen: true,
          title: 'Тестовое модальное окно',
          component: 'BookingFormModal',
          props: { bookingId: '123' },
          size: 'medium',
          persistent: false
        },
        'modal-2': {
          id: 'modal-2',
          isOpen: true,
          title: 'Подтверждение действия',
          component: 'ConfirmDialog',
          props: { message: 'Вы уверены?' },
          size: 'small',
          persistent: true
        }
      },
      activeModals: {
        'modal-1': {
          id: 'modal-1',
          isOpen: true,
          title: 'Тестовое модальное окно',
          component: 'BookingFormModal',
          props: { bookingId: '123' },
          size: 'medium',
          persistent: false
        },
        'modal-2': {
          id: 'modal-2',
          isOpen: true,
          title: 'Подтверждение действия',
          component: 'ConfirmDialog',
          props: { message: 'Вы уверены?' },
          size: 'small',
          persistent: true
        }
      },
      closeModal: vi.fn(),
      openModal: vi.fn(),
      isModalOpen: vi.fn()
    }

    // Настройка мока стора
    const { useModalStore, useServiceStore, useStaffStore, useBookingStore, useNotificationStore } = await import('@/stores')
    vi.mocked(useModalStore).mockReturnValue(mockModalStore)
    vi.mocked(useServiceStore).mockReturnValue({
      services: [],
      loading: false,
      fetchServices: vi.fn()
    })
    vi.mocked(useStaffStore).mockReturnValue({
      staff: [],
      loading: false,
      fetchStaff: vi.fn()
    })
    vi.mocked(useBookingStore).mockReturnValue({
      bookings: [],
      loading: false,
      fetchBookings: vi.fn()
    })
    vi.mocked(useNotificationStore).mockReturnValue({
      success: vi.fn(),
      error: vi.fn(),
      info: vi.fn()
    })

    wrapper = mount(ModalContainer, {
      global: {
        stubs: {
          XIcon: true,
          Teleport: {
            template: '<div><slot /></div>'
          },
          Transition: {
            template: '<div><slot /></div>'
          },
          component: true
        }
      }
    })
  })

  describe('Рендеринг модальных окон', () => {
    it('отображает активные модальные окна', () => {
      const modalWrappers = wrapper.findAll('.modal-wrapper')
      expect(modalWrappers.length).toBe(2)
    })

    it('отображает заголовки модальных окон', () => {
      const titles = wrapper.findAll('h3')
      expect(titles[0].text()).toBe('Тестовое модальное окно')
      expect(titles[1].text()).toBe('Подтверждение действия')
    })

    it('отображает кнопки закрытия', () => {
      const closeButtons = wrapper.findAll('button')
      expect(closeButtons.length).toBeGreaterThanOrEqual(2)
    })

    it('отображает overlay для каждого модального окна', () => {
      const overlays = wrapper.findAll('.modal-overlay')
      expect(overlays.length).toBe(2)
    })

    it('отображает контент модальных окон', () => {
      const modalContents = wrapper.findAll('.modal-content')
      expect(modalContents.length).toBe(2)
    })
  })

  describe('Размеры модальных окон', () => {
    it('применяет правильные классы размеров', () => {
      const modalContents = wrapper.findAll('.modal-content')
      
      // Проверяем, что применяются классы размеров
      expect(modalContents[0].classes()).toContain('modal-content')
      expect(modalContents[1].classes()).toContain('modal-content')
    })

    it('обрабатывает разные размеры модальных окон', async () => {
      // Тестируем различные размеры
      mockModalStore.modals['modal-3'] = {
        id: 'modal-3',
        isOpen: true,
        title: 'Большое модальное окно',
        component: 'ReportGeneratorModal',
        size: 'large'
      }
      mockModalStore.activeModals['modal-3'] = {
        id: 'modal-3',
        isOpen: true,
        title: 'Большое модальное окно',
        component: 'ReportGeneratorModal',
        size: 'large'
      }

      await wrapper.vm.$nextTick()

      const modalContents = wrapper.findAll('.modal-content')
      expect(modalContents.length).toBe(3)
    })
  })

  describe('Взаимодействие с модальными окнами', () => {
    it('закрывает модальное окно при клике на кнопку закрытия', async () => {
      const closeButton = wrapper.find('button')
      await closeButton.trigger('click')
      
      expect(mockModalStore.closeModal).toHaveBeenCalled()
    })

    it('закрывает модальное окно при клике на overlay (если не persistent)', async () => {
      const overlay = wrapper.findAll('.modal-overlay')[0]
      await overlay.trigger('click')
      
      expect(mockModalStore.closeModal).toHaveBeenCalledWith('modal-1')
    })

    it('не закрывает persistent модальное окно при клике на overlay', async () => {
      const overlay = wrapper.findAll('.modal-overlay')[1]
      await overlay.trigger('click')
      
      // Для persistent модального окна не должно быть вызова closeModal
      // или должна быть проверка на persistent флаг
      expect(mockModalStore.closeModal).toHaveBeenCalledTimes(0)
    })

    it('не закрывает модальное окно при клике на контент', async () => {
      const modalContent = wrapper.find('.modal-content')
      await modalContent.trigger('click')
      
      // При клике на контент модальное окно не должно закрываться
      expect(mockModalStore.closeModal).not.toHaveBeenCalled()
    })
  })

  describe('Динамические компоненты', () => {
    it('рендерит динамические компоненты', () => {
      const dynamicComponents = wrapper.findAll('component')
      expect(dynamicComponents.length).toBeGreaterThan(0)
    })

    it('передает props в динамические компоненты', () => {
      // Проверяем, что props передаются в компоненты
      const component = wrapper.findComponent({ name: 'component' })
      if (component.exists()) {
        expect(component.props()).toBeDefined()
      }
    })

    it('обрабатывает событие close от дочерних компонентов', async () => {
      const component = wrapper.findComponent({ name: 'component' })
      if (component.exists()) {
        await component.vm.$emit('close')
        expect(mockModalStore.closeModal).toHaveBeenCalled()
      }
    })
  })

  describe('Fallback контент', () => {
    it('отображает fallback контент когда компонент не указан', async () => {
      mockModalStore.modals['modal-fallback'] = {
        id: 'modal-fallback',
        isOpen: true,
        title: 'Модальное окно без компонента',
        component: null
      }
      mockModalStore.activeModals['modal-fallback'] = {
        id: 'modal-fallback',
        isOpen: true,
        title: 'Модальное окно без компонента',
        component: null
      }

      await wrapper.vm.$nextTick()

      expect(wrapper.text()).toContain('Содержимое модального окна')
    })
  })

  describe('Анимации и переходы', () => {
    it('использует Transition для анимации', () => {
      const transitions = wrapper.findAllComponents({ name: 'Transition' })
      expect(transitions.length).toBeGreaterThan(0)
    })

    it('применяет правильные имена анимаций', () => {
      const transition = wrapper.findComponent({ name: 'Transition' })
      if (transition.exists()) {
        expect(transition.props('name')).toBe('modal')
      }
    })
  })

  describe('Доступность', () => {
    it('содержит aria-label для кнопки закрытия', () => {
      const closeButton = wrapper.find('button')
      const srOnly = closeButton.find('.sr-only')
      expect(srOnly.text()).toBe('Закрыть')
    })

    it('поддерживает навигацию с клавиатуры', async () => {
      const closeButton = wrapper.find('button')
      
      // Проверяем focus ring классы
      expect(closeButton.classes()).toContain('focus:outline-none')
      expect(closeButton.classes()).toContain('focus:ring-2')
    })
  })

  describe('Обработка ошибок', () => {
    it('корректно обрабатывает отсутствующие модальные окна', async () => {
      mockModalStore.modals = {}
      mockModalStore.activeModals = {}
      await wrapper.vm.$nextTick()

      const modalWrappers = wrapper.findAll('.modal-wrapper')
      expect(modalWrappers.length).toBe(0)
    })

    it('обрабатывает некорректные данные модальных окон', async () => {
      mockModalStore.modals = {
        'invalid-modal': {
          id: 'invalid-modal',
          isOpen: true
          // Отсутствуют обязательные поля
        }
      }
      mockModalStore.activeModals = {
        'invalid-modal': {
          id: 'invalid-modal',
          isOpen: true
          // Отсутствуют обязательные поля
        }
      }

      await wrapper.vm.$nextTick()

      // Компонент должен корректно обработать некорректные данные
      expect(wrapper.exists()).toBe(true)
    })
  })

  describe('Множественные модальные окна', () => {
    it('корректно отображает несколько модальных окон одновременно', () => {
      const modalWrappers = wrapper.findAll('.modal-wrapper')
      expect(modalWrappers.length).toBe(2)
    })

    it('управляет z-index для наложения модальных окон', () => {
      const overlays = wrapper.findAll('.modal-overlay')
      overlays.forEach((overlay: { exists: () => any }) => {
        expect(overlay.exists()).toBe(true)
      })
    })
  })

  describe('Методы компонента', () => {
    it('имеет метод handleOverlayClick', () => {
      expect(typeof wrapper.vm.handleOverlayClick).toBe('function')
    })

    it('имеет метод closeModal', () => {
      expect(typeof wrapper.vm.closeModal).toBe('function')
    })

    it('имеет метод getModalSizeClass', () => {
      expect(typeof wrapper.vm.getModalSizeClass).toBe('function')
    })

    it('имеет метод getModalComponent', () => {
      expect(typeof wrapper.vm.getModalComponent).toBe('function')
    })
  })

  describe('Реактивность', () => {
    it('реагирует на изменения в activeModals', async () => {
      const initialCount = wrapper.findAll('.modal-wrapper').length

      // Добавляем новое модальное окно
      mockModalStore.modals['new-modal'] = {
        id: 'new-modal',
        isOpen: true,
        title: 'Новое модальное окно',
        component: 'TestModal'
      }
      mockModalStore.activeModals['new-modal'] = {
        id: 'new-modal',
        isOpen: true,
        title: 'Новое модальное окно',
        component: 'TestModal'
      }

      await wrapper.vm.$nextTick()

      const newCount = wrapper.findAll('.modal-wrapper').length
      expect(newCount).toBe(initialCount + 1)
    })

    it('обновляется при закрытии модального окна', async () => {
      mockModalStore.modals['modal-1'].isOpen = false
      mockModalStore.activeModals['modal-1'].isOpen = false
      await wrapper.vm.$nextTick()

      // Модальное окно должно исчезнуть или скрыться
      const visibleModals = wrapper.findAll('.modal-overlay')
      expect(visibleModals.length).toBeLessThanOrEqual(2)
    })
  })
})