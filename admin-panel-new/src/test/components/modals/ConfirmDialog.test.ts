import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import ConfirmDialog from '@/components/modals/ConfirmDialog.vue'

describe('ConfirmDialog', () => {
  let wrapper: any

  const createWrapper = (props = {}) => {
    return mount(ConfirmDialog, {
      props: {
        title: 'Подтвердить действие',
        message: 'Вы действительно хотите выполнить это действие?',
        ...props
      }
    })
  }

  describe('Component Rendering', () => {
    it('renders with default props', () => {
      wrapper = createWrapper()

      expect(wrapper.find('h3').text()).toBe('Подтвердить действие')
      expect(wrapper.find('p').text()).toBe('Вы действительно хотите выполнить это действие?')
    })

    it('renders with custom title and message', () => {
      wrapper = createWrapper({
        title: 'Удалить запись',
        message: 'Это действие нельзя отменить. Продолжить?'
      })

      expect(wrapper.find('h3').text()).toBe('Удалить запись')
      expect(wrapper.find('p').text()).toBe('Это действие нельзя отменить. Продолжить?')
    })

    it('renders warning icon', () => {
      wrapper = createWrapper()

      const iconContainer = wrapper.find('.bg-red-100')
      expect(iconContainer.exists()).toBe(true)
      expect(iconContainer.find('svg').exists()).toBe(true)
    })

    it('renders action buttons', () => {
      wrapper = createWrapper()

      const buttons = wrapper.findAll('button')
      expect(buttons).toHaveLength(2)
      expect(buttons[0].text()).toBe('Отмена')
      expect(buttons[1].text()).toBe('Подтвердить')
    })
  })

  describe('Default Props', () => {
    it('uses default title when not provided', () => {
      wrapper = mount(ConfirmDialog, {
        props: {
          message: 'Test message'
        }
      })

      expect(wrapper.find('h3').text()).toBe('Подтверждение действия')
    })

    it('uses default message when not provided', () => {
      wrapper = mount(ConfirmDialog, {
        props: {
          title: 'Test title'
        }
      })

      expect(wrapper.find('p').text()).toBe('Вы уверены, что хотите выполнить это действие?')
    })

    it('uses both defaults when no props provided', () => {
      wrapper = mount(ConfirmDialog)

      expect(wrapper.find('h3').text()).toBe('Подтверждение действия')
      expect(wrapper.find('p').text()).toBe('Вы уверены, что хотите выполнить это действие?')
    })
  })

  describe('Event Handling', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('emits close event when cancel button is clicked', async () => {
      const cancelButton = wrapper.find('button.btn-secondary')
      await cancelButton.trigger('click')

      expect(wrapper.emitted('close')).toBeTruthy()
      expect(wrapper.emitted('close')).toHaveLength(1)
    })

    it('emits close event when confirm button is clicked without onConfirm prop', async () => {
      const confirmButton = wrapper.find('button.btn-danger')
      await confirmButton.trigger('click')

      expect(wrapper.emitted('close')).toBeTruthy()
      expect(wrapper.emitted('close')).toHaveLength(1)
    })
  })

  describe('Async Confirmation Handling', () => {
    it('calls onConfirm function when provided', async () => {
      const onConfirmMock = vi.fn().mockResolvedValue(undefined)
      wrapper = createWrapper({ onConfirm: onConfirmMock })

      const confirmButton = wrapper.find('button.btn-danger')
      await confirmButton.trigger('click')

      expect(onConfirmMock).toHaveBeenCalledTimes(1)
    })

    it('shows loading state during async operation', async () => {
      let resolvePromise: () => void
      const onConfirmMock = vi.fn().mockImplementation(() => {
        return new Promise<void>((resolve) => {
          resolvePromise = resolve
        })
      })

      wrapper = createWrapper({ onConfirm: onConfirmMock })
      const confirmButton = wrapper.find('button.btn-danger')

      // Click confirm button
      const clickPromise = confirmButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Should show loading state
      expect(confirmButton.text()).toBe('Выполняется...')
      expect(confirmButton.attributes('disabled')).toBeDefined()

      // Resolve the promise
      resolvePromise!()
      await clickPromise
      await wrapper.vm.$nextTick()

      // Should return to normal state
      expect(confirmButton.text()).toBe('Подтвердить')
      expect(confirmButton.attributes('disabled')).toBeUndefined()
    })

    it('emits close event after successful async operation', async () => {
      const onConfirmMock = vi.fn().mockResolvedValue(undefined)
      wrapper = createWrapper({ onConfirm: onConfirmMock })

      const confirmButton = wrapper.find('button.btn-danger')
      await confirmButton.trigger('click')

      // Wait for async operation to complete
      await new Promise(resolve => setTimeout(resolve, 0))

      expect(wrapper.emitted('close')).toBeTruthy()
      expect(wrapper.emitted('close')).toHaveLength(1)
    })

    it('handles async operation errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      const onConfirmMock = vi.fn().mockRejectedValue(new Error('Operation failed'))
      
      wrapper = createWrapper({ onConfirm: onConfirmMock })

      const confirmButton = wrapper.find('button.btn-danger')
      await confirmButton.trigger('click')

      // Wait for async operation to complete
      await new Promise(resolve => setTimeout(resolve, 0))

      expect(consoleSpy).toHaveBeenCalledWith('Error in confirm action:', expect.any(Error))
      
      // Should not emit close event on error
      expect(wrapper.emitted('close')).toBeFalsy()
      
      // Should reset loading state
      expect(confirmButton.text()).toBe('Подтвердить')
      expect(confirmButton.attributes('disabled')).toBeUndefined()

      consoleSpy.mockRestore()
    })
  })

  describe('Button States', () => {
    it('disables confirm button during loading', async () => {
      let resolvePromise: () => void
      const onConfirmMock = vi.fn().mockImplementation(() => {
        return new Promise<void>((resolve) => {
          resolvePromise = resolve
        })
      })

      wrapper = createWrapper({ onConfirm: onConfirmMock })
      const confirmButton = wrapper.find('button.btn-danger')

      expect(confirmButton.attributes('disabled')).toBeUndefined()

      const clickPromise = confirmButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(confirmButton.attributes('disabled')).toBeDefined()

      resolvePromise!()
      await clickPromise
      await wrapper.vm.$nextTick()

      expect(confirmButton.attributes('disabled')).toBeUndefined()
    })

    it('does not disable cancel button during loading', async () => {
      let resolvePromise: () => void
      const onConfirmMock = vi.fn().mockImplementation(() => {
        return new Promise<void>((resolve) => {
          resolvePromise = resolve
        })
      })

      wrapper = createWrapper({ onConfirm: onConfirmMock })
      const cancelButton = wrapper.find('button.btn-secondary')
      const confirmButton = wrapper.find('button.btn-danger')

      await confirmButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(cancelButton.attributes('disabled')).toBeUndefined()

      resolvePromise!()
    })
  })

  describe('Accessibility', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('has proper semantic structure', () => {
      expect(wrapper.find('h3').exists()).toBe(true)
      expect(wrapper.find('p').exists()).toBe(true)
    })

    it('has proper button types', () => {
      const buttons = wrapper.findAll('button')
      buttons.forEach((button: any) => {
        expect(button.attributes('type')).toBe('button')
      })
    })

    it('has proper button classes for visual distinction', () => {
      const cancelButton = wrapper.find('button.btn-secondary')
      const confirmButton = wrapper.find('button.btn-danger')

      expect(cancelButton.classes()).toContain('btn-secondary')
      expect(confirmButton.classes()).toContain('btn-danger')
    })

    it('has warning icon with proper styling', () => {
      const iconContainer = wrapper.find('.bg-red-100')
      const icon = iconContainer.find('svg')

      expect(iconContainer.classes()).toContain('bg-red-100')
      expect(icon.classes()).toContain('text-red-600')
    })
  })

  describe('Icon Component', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('renders SVG icon correctly', () => {
      const svg = wrapper.find('svg')
      expect(svg.exists()).toBe(true)
      expect(svg.attributes('viewBox')).toBe('0 0 24 24')
      expect(svg.attributes('fill')).toBe('none')
      expect(svg.attributes('stroke')).toBe('currentColor')
    })

    it('has proper SVG path for exclamation triangle', () => {
      const path = wrapper.find('svg path')
      expect(path.exists()).toBe(true)
      expect(path.attributes('stroke-linecap')).toBe('round')
      expect(path.attributes('stroke-linejoin')).toBe('round')
      expect(path.attributes('stroke-width')).toBe('2')
    })
  })

  describe('Component Layout', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('has centered layout', () => {
      const container = wrapper.find('.text-center')
      expect(container.exists()).toBe(true)
    })

    it('has proper spacing between elements', () => {
      const iconContainer = wrapper.find('.mb-4')
      const title = wrapper.find('.mb-2')
      const message = wrapper.find('.mb-6')
      const buttonContainer = wrapper.find('.space-x-3')

      expect(iconContainer.exists()).toBe(true)
      expect(title.exists()).toBe(true)
      expect(message.exists()).toBe(true)
      expect(buttonContainer.exists()).toBe(true)
    })

    it('has proper button layout', () => {
      const buttonContainer = wrapper.find('.flex.space-x-3.justify-center')
      expect(buttonContainer.exists()).toBe(true)
      
      const buttons = buttonContainer.findAll('button')
      expect(buttons).toHaveLength(2)
    })
  })

  describe('Edge Cases', () => {
    it('handles undefined onConfirm gracefully', async () => {
      wrapper = createWrapper({ onConfirm: undefined })
      
      const confirmButton = wrapper.find('button.btn-danger')
      await confirmButton.trigger('click')

      expect(wrapper.emitted('close')).toBeTruthy()
    })

    it('handles null onConfirm gracefully', async () => {
      wrapper = createWrapper({ onConfirm: null })
      
      const confirmButton = wrapper.find('button.btn-danger')
      await confirmButton.trigger('click')

      expect(wrapper.emitted('close')).toBeTruthy()
    })

    it('handles synchronous onConfirm function', async () => {
      const onConfirmMock = vi.fn()
      wrapper = createWrapper({ onConfirm: onConfirmMock })

      const confirmButton = wrapper.find('button.btn-danger')
      await confirmButton.trigger('click')

      expect(onConfirmMock).toHaveBeenCalledTimes(1)
      expect(wrapper.emitted('close')).toBeTruthy()
    })

    it('handles empty title and message', () => {
      wrapper = createWrapper({
        title: '',
        message: ''
      })

      expect(wrapper.find('h3').text()).toBe('')
      expect(wrapper.find('p').text()).toBe('')
    })

    it('handles very long title and message', () => {
      const longTitle = 'A'.repeat(100)
      const longMessage = 'B'.repeat(500)
      
      wrapper = createWrapper({
        title: longTitle,
        message: longMessage
      })

      expect(wrapper.find('h3').text()).toBe(longTitle)
      expect(wrapper.find('p').text()).toBe(longMessage)
    })
  })

  describe('Multiple Clicks Prevention', () => {
    it('prevents multiple simultaneous async operations', async () => {
      let resolveCount = 0
      const onConfirmMock = vi.fn().mockImplementation(() => {
        return new Promise<void>((resolve) => {
          setTimeout(() => {
            resolveCount++
            resolve()
          }, 100)
        })
      })

      wrapper = createWrapper({ onConfirm: onConfirmMock })
      const confirmButton = wrapper.find('button.btn-danger')

      // Click multiple times rapidly
      await confirmButton.trigger('click')
      await confirmButton.trigger('click')
      await confirmButton.trigger('click')

      // Should only call onConfirm once due to disabled state
      expect(onConfirmMock).toHaveBeenCalledTimes(1)

      // Wait for operation to complete
      await new Promise(resolve => setTimeout(resolve, 150))
      
      expect(resolveCount).toBe(1)
    })
  })
})