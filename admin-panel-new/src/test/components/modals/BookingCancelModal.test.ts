import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import BookingCancelModal from '@/components/modals/BookingCancelModal.vue'
import { useBookingStore, useModalStore, useNotificationStore } from '@/stores'
import type { Booking } from '@/types'

// Mock booking data
const mockBooking: Booking = {
  id: 1,
  code: 'BK001',
  guest_name: 'Иван Петров',
  room_number: '101',
  date: '2024-06-20',
  time: '14:00',
  services: [
    { id: 1, name: 'Массаж', price: 3000, duration: 60 },
    { id: 2, name: 'СПА процедуры', price: 2000, duration: 90 }
  ],
  total_price: 5000,
  status: 'confirmed',
  staff_id: 1,
  created_at: '2024-06-19T10:00:00Z',
  confirmation_code: '',
  persons: 0,
  phone: '',
  notes: '',
  total_duration: 0
}

describe('BookingCancelModal', () => {
  let wrapper: any
  let pinia: any
  let bookingStore: any
  let modalStore: any
  let notificationStore: any

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    
    bookingStore = useBookingStore()
    modalStore = useModalStore()
    notificationStore = useNotificationStore()

    // Mock store methods
    vi.spyOn(modalStore, 'closeModal')
    vi.spyOn(notificationStore, 'success')
    vi.spyOn(notificationStore, 'error')
    
    // Set up mock booking in store
    bookingStore.bookings = [mockBooking]
  })

  const createWrapper = (props = {}) => {
    return mount(BookingCancelModal, {
      props: {
        bookingId: 1,
        ...props
      },
      global: {
        plugins: [pinia]
      }
    })
  }

  describe('Component Rendering', () => {
    it('renders modal header correctly', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('h3').text()).toBe('Отменить бронирование')
      expect(wrapper.text()).toContain('Код: BK001 • Иван Петров')
    })

    it('displays booking information correctly', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const bookingInfo = wrapper.find('.bg-gray-50')
      expect(bookingInfo.text()).toContain('20 июня 2024 г. в 14:00')
      expect(bookingInfo.text()).toContain('Иван Петров (101)')
      expect(bookingInfo.text()).toContain('Массаж, СПА процедуры')
      // Check for currency with flexible matching
      expect(bookingInfo.text()).toMatch(/5\s?000.*₽|5\s?000.*руб/i)
    })

    it('renders all form fields', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      // Reason select
      expect(wrapper.find('select').exists()).toBe(true)
      expect(wrapper.find('select option[value="guest_request"]').text()).toBe('Просьба гостя')

      // Details textarea
      expect(wrapper.find('textarea').exists()).toBe(true)

      // Refund options
      const refundRadios = wrapper.findAll('input[type="radio"]')
      expect(refundRadios).toHaveLength(3)
      expect(wrapper.text()).toContain('Полный возврат')
      expect(wrapper.text()).toContain('Частичный возврат')
      expect(wrapper.text()).toContain('Без возврата')

      // Notification checkboxes
      const checkboxes = wrapper.findAll('input[type="checkbox"]')
      expect(checkboxes).toHaveLength(3) // 2 notification + 1 confirmation

      // Confirmation checkbox
      expect(wrapper.text()).toContain('Я подтверждаю, что хочу отменить это бронирование')
    })

    it('shows loading state when booking is not loaded', () => {
      wrapper = createWrapper({ bookingId: 999 }) // Non-existent booking
      expect(wrapper.text()).toContain('Загрузка данных бронирования...')
    })
  })

  describe('Form Validation', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('validates required reason field', async () => {
      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.attributes('disabled')).toBeDefined()

      // Select a reason
      await wrapper.find('select').setValue('guest_request')
      await wrapper.find('input[type="checkbox"][required]').setChecked(true)
      
      expect(submitButton.attributes('disabled')).toBeUndefined()
    })

    it('validates confirmation checkbox', async () => {
      await wrapper.find('select').setValue('guest_request')
      
      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.attributes('disabled')).toBeDefined()

      await wrapper.find('input[type="checkbox"][required]').setChecked(true)
      expect(submitButton.attributes('disabled')).toBeUndefined()
    })

    it('validates partial refund amount', async () => {
      // Select partial refund
      await wrapper.find('input[value="partial"]').setChecked(true)
      await wrapper.vm.$nextTick()

      // Partial refund amount field should appear
      const amountInput = wrapper.find('input[type="number"]')
      expect(amountInput.exists()).toBe(true)

      // Set required fields
      await wrapper.find('select').setValue('guest_request')
      await wrapper.find('input[type="checkbox"][required]').setChecked(true)

      // Invalid amount (0)
      await amountInput.setValue(0)
      let submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.attributes('disabled')).toBeDefined()

      // Valid amount
      await amountInput.setValue(2500)
      expect(submitButton.attributes('disabled')).toBeUndefined()

      // Amount exceeding total price
      await amountInput.setValue(6000)
      expect(submitButton.attributes('disabled')).toBeDefined()
    })
  })

  describe('Refund Options', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('shows full refund by default', () => {
      const fullRefundRadio = wrapper.find('input[value="full"]')
      expect(fullRefundRadio.element.checked).toBe(true)
      // Check for currency with flexible matching
      expect(wrapper.text()).toMatch(/Полный возврат.*5\s?000.*₽|Полный возврат.*5\s?000.*руб/i)
    })

    it('shows partial refund amount input when partial is selected', async () => {
      expect(wrapper.find('input[type="number"]').exists()).toBe(false)

      await wrapper.find('input[value="partial"]').setChecked(true)
      await wrapper.vm.$nextTick()

      const amountInput = wrapper.find('input[type="number"]')
      expect(amountInput.exists()).toBe(true)
      expect(amountInput.attributes('max')).toBe('5000')
    })

    it('hides partial refund amount input when other options selected', async () => {
      // First select partial to show input
      await wrapper.find('input[value="partial"]').setChecked(true)
      await wrapper.vm.$nextTick()
      expect(wrapper.find('input[type="number"]').exists()).toBe(true)

      // Then select no refund
      await wrapper.find('input[value="none"]').setChecked(true)
      await wrapper.vm.$nextTick()
      expect(wrapper.find('input[type="number"]').exists()).toBe(false)
    })
  })

  describe('Notification Options', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('has notification checkboxes checked by default', () => {
      const checkboxes = wrapper.findAll('input[type="checkbox"]:not([required])')
      expect(checkboxes[0].element.checked).toBe(true) // notify_guest
      expect(checkboxes[1].element.checked).toBe(true) // notify_staff
    })

    it('updates warning message based on guest notification setting', async () => {
      expect(wrapper.text()).toContain('Гость получит уведомление об отмене.')

      const guestNotifyCheckbox = wrapper.findAll('input[type="checkbox"]:not([required])')[0]
      await guestNotifyCheckbox.setChecked(false)
      await wrapper.vm.$nextTick()

      expect(wrapper.text()).not.toContain('Гость получит уведомление об отмене.')
    })
  })

  describe('Form Submission', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      // Fill required fields
      await wrapper.find('select').setValue('guest_request')
      await wrapper.find('input[type="checkbox"][required]').setChecked(true)
    })

    it('submits form with correct data', async () => {
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')

      expect(notificationStore.success).toHaveBeenCalledWith('Успешно', 'Бронирование отменено')
      expect(modalStore.closeModal).toHaveBeenCalledWith('booking-cancel')
    })

    it('shows loading state during submission', async () => {
      const submitButton = wrapper.find('button[type="submit"]')
      expect(submitButton.text()).toBe('Отменить бронирование')

      // Mock a delayed submission
      const originalSubmit = wrapper.vm.handleSubmit
      wrapper.vm.handleSubmit = async () => {
        wrapper.vm.submitting = true
        await wrapper.vm.$nextTick()
        expect(submitButton.text()).toBe('Отмена бронирования...')
        expect(submitButton.attributes('disabled')).toBeDefined()
        
        await originalSubmit()
      }

      const form = wrapper.find('form')
      await form.trigger('submit.prevent')
    })

    it("handles submission errors", async () => {
      expect(typeof wrapper.vm.handleSubmit).toBe("function")
    })
  })

  describe('Modal Controls', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('closes modal when cancel button is clicked', async () => {
      const cancelButton = wrapper.find('button.btn-secondary')
      await cancelButton.trigger('click')

      expect(modalStore.closeModal).toHaveBeenCalledWith('booking-cancel')
    })
  })

  describe('Currency and Date Formatting', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('formats currency correctly', () => {
      const formatted5000 = wrapper.vm.formatCurrency(5000)
      const formatted1500 = wrapper.vm.formatCurrency(1500)
      
      // Check that currency formatting includes the amount and currency symbol
      expect(formatted5000).toMatch(/5\s?000.*₽|5\s?000.*руб/i)
      expect(formatted1500).toMatch(/1\s?500.*₽|1\s?500.*руб/i)
    })

    it('formats dates correctly', () => {
      expect(wrapper.vm.formatDate('2024-06-20')).toBe('20 июня 2024 г.')
    })
  })

  describe('Accessibility', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('has proper form labels', async () => {
      expect(wrapper.find('label[class*="label"]').exists()).toBe(true)
      expect(wrapper.text()).toContain('Причина отмены *')
      expect(wrapper.text()).toContain('Дополнительные детали')
      
      // Select partial refund to show the refund amount field
      await wrapper.find('input[value="partial"]').setChecked(true)
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Сумма возврата')
    })

    it('has required field indicators', () => {
      expect(wrapper.text()).toContain('Причина отмены *')
      expect(wrapper.find('select[required]').exists()).toBe(true)
      expect(wrapper.find('input[type="checkbox"][required]').exists()).toBe(true)
    })

    it('has proper button types and classes', () => {
      const cancelButton = wrapper.find('button.btn-secondary')
      const submitButton = wrapper.find('button.btn-danger')
      
      expect(cancelButton.attributes('type')).toBe('button')
      expect(submitButton.attributes('type')).toBe('submit')
    })
  })

  describe('Edge Cases', () => {
    it('handles missing booking gracefully', () => {
      wrapper = createWrapper({ bookingId: undefined })
      expect(wrapper.text()).toContain('Загрузка данных бронирования...')
    })

    it('handles booking without services', async () => {
      const bookingWithoutServices = { ...mockBooking, services: [] }
      bookingStore.bookings = [bookingWithoutServices]
      
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      expect(wrapper.text()).toContain('Информация о бронировании')
      // Should not crash when services array is empty
    })

    it('prevents form submission when booking is null', async () => {
      wrapper = createWrapper({ bookingId: 999 }) // Non-existent booking
      
      // Try to submit form
      const form = wrapper.find('form')
      await form.trigger('submit.prevent')

      // Should not call notification or modal methods
      expect(notificationStore.success).not.toHaveBeenCalled()
      expect(modalStore.closeModal).not.toHaveBeenCalled()
    })
  })
})