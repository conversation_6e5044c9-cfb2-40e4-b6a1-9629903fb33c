import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import BookingDetailsModal from '@/components/modals/BookingDetailsModal.vue'
import { useBookingStore, useModalStore } from '@/stores'
import type { Booking } from '@/types'

// Mock booking data with all possible fields
const mockBooking: Booking = {
  id: 1,
  code: 'BK001',
  guest_name: '<PERSON>в<PERSON><PERSON> Петров',
  room_number: '101',
  phone: '+7 (999) 123-45-67',
  persons: 2,
  date: '2024-06-20',
  time: '14:00',
  staff_name: '<PERSON><PERSON><PERSON>',
  confirmation_code: 'CONF123456',
  services: [
    { 
      id: 1, 
      name: '<PERSON>а<PERSON><PERSON><PERSON><PERSON>', 
      price: 3000, 
      duration: 60, 
      category: 'Релаксация' 
    },
    { 
      id: 2, 
      name: 'С<PERSON>А процедуры', 
      price: 2000, 
      duration: 90, 
      category: 'Красота' 
    }
  ],
  total_price: 5000,
  total_duration: 150,
  status: 'confirmed',
  staff_id: 1,
  notes: 'Особые пожелания: тихая музыка, ароматерапия',
  created_at: '2024-06-19T10:00:00Z'
}

// Mock booking without optional fields
const mockMinimalBooking: Booking = {
  id: 2,
  code: 'BK002',
  guest_name: 'Мария Иванова',
  room_number: '202',
  date: '2024-06-21',
  time: '16:00',
  services: [
    { id: 3, name: 'Маникюр', price: 1500, duration: 45 }
  ],
  total_price: 1500,
  total_duration: 45,
  status: 'pending',
  staff_id: 2,
  created_at: '2024-06-20T12:00:00Z',
  confirmation_code: '',
  persons: 0,
  phone: '',
  notes: ''
}

const mockCancelledBooking: Booking = {
  ...mockBooking,
  id: 3,
  status: 'cancelled'
}

describe('BookingDetailsModal', () => {
  let wrapper: any
  let pinia: any
  let bookingStore: any
  let modalStore: any

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    
    bookingStore = useBookingStore()
    modalStore = useModalStore()

    // Mock store methods
    vi.spyOn(modalStore, 'closeModal')
    vi.spyOn(modalStore, 'openModal')
    
    // Set up mock bookings in store
    bookingStore.bookings = [mockBooking, mockMinimalBooking, mockCancelledBooking]
  })

  const createWrapper = (props = {}) => {
    return mount(BookingDetailsModal, {
      props: {
        bookingId: 1,
        ...props
      },
      global: {
        plugins: [pinia]
      }
    })
  }

  describe('Component Rendering', () => {
    it('renders modal header correctly', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('h3').text()).toBe('Детали бронирования')
      expect(wrapper.text()).toContain('Код: BK001')
    })

    it('displays status badge correctly for confirmed booking', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const statusBadge = wrapper.find('.px-3.py-1.rounded-full')
      expect(statusBadge.text()).toBe('Подтверждено')
      expect(statusBadge.classes()).toContain('bg-green-100')
      expect(statusBadge.classes()).toContain('text-green-800')
    })

    it('displays status badge correctly for pending booking', async () => {
      wrapper = createWrapper({ bookingId: 2 })
      await wrapper.vm.$nextTick()

      const statusBadge = wrapper.find('.px-3.py-1.rounded-full')
      expect(statusBadge.text()).toBe('Ожидает подтверждения')
      expect(statusBadge.classes()).toContain('bg-yellow-100')
      expect(statusBadge.classes()).toContain('text-yellow-800')
    })

    it('displays status badge correctly for cancelled booking', async () => {
      wrapper = createWrapper({ bookingId: 3 })
      await wrapper.vm.$nextTick()

      const statusBadge = wrapper.find('.px-3.py-1.rounded-full')
      expect(statusBadge.text()).toBe('Отменено')
      expect(statusBadge.classes()).toContain('bg-red-100')
      expect(statusBadge.classes()).toContain('text-red-800')
    })

    it('shows loading state when booking is not found', () => {
      wrapper = createWrapper({ bookingId: 999 })
      expect(wrapper.text()).toContain('Загрузка данных бронирования...')
    })
  })

  describe('Guest Information Display', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('displays complete guest information', () => {
      const guestSection = wrapper.find('.bg-gray-50')
      expect(guestSection.text()).toContain('Информация о госте')
      expect(guestSection.text()).toContain('Иван Петров')
      expect(guestSection.text()).toContain('101')
      expect(guestSection.text()).toContain('+7 (999) 123-45-67')
      expect(guestSection.text()).toContain('2')
    })

    it('handles missing phone number gracefully', async () => {
      wrapper = createWrapper({ bookingId: 2 })
      await wrapper.vm.$nextTick()

      expect(wrapper.text()).toContain('Не указан')
    })
  })

  describe('Booking Details Display', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('displays booking details correctly', () => {
      const sections = wrapper.findAll('.bg-gray-50')
      const bookingSection = sections[1] // Second gray section
      
      expect(bookingSection.text()).toContain('Детали бронирования')
      expect(bookingSection.text()).toContain('20 июня 2024 г.')
      expect(bookingSection.text()).toContain('14:00')
      expect(bookingSection.text()).toContain('Анна Смирнова')
      expect(bookingSection.text()).toContain('CONF123456')
    })

    it('handles missing staff name gracefully', async () => {
      wrapper = createWrapper({ bookingId: 2 })
      await wrapper.vm.$nextTick()

      expect(wrapper.text()).toContain('Не назначен')
    })
  })

  describe('Services Display', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('displays all services correctly', () => {
      const serviceItems = wrapper.findAll('.bg-white.rounded-lg.border')
      expect(serviceItems).toHaveLength(2)

      // First service
      expect(serviceItems[0].text()).toContain('Массаж')
      expect(serviceItems[0].text()).toContain('60 мин')
      expect(serviceItems[0].text()).toContain('Релаксация')
      expect(serviceItems[0].text()).toMatch(/3\s?000.*₽|3\s?000.*руб/i)

      // Second service
      expect(serviceItems[1].text()).toContain('СПА процедуры')
      expect(serviceItems[1].text()).toContain('90 мин')
      expect(serviceItems[1].text()).toContain('Красота')
      expect(serviceItems[1].text()).toMatch(/2\s?000.*₽|2\s?000.*руб/i)
    })

    it('handles services without category', async () => {
      wrapper = createWrapper({ bookingId: 2 })
      await wrapper.vm.$nextTick()

      const serviceItem = wrapper.find('.bg-white.rounded-lg.border')
      expect(serviceItem.text()).toContain('Маникюр')
      expect(serviceItem.text()).toContain('45 мин')
      expect(serviceItem.text()).not.toContain('•') // No category separator
    })
  })

  describe('Total Cost Display', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('displays total cost and duration correctly', () => {
      const totalSection = wrapper.find('.bg-primary-50')
      expect(totalSection.text()).toContain('Общая стоимость:')
      expect(totalSection.text()).toContain('Продолжительность: 150 минут')
      expect(totalSection.text()).toMatch(/5\s?000.*₽|5\s?000.*руб/i)
    })
  })

  describe('Notes Display', () => {
    it('displays notes when present', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      expect(wrapper.text()).toContain('Особые пожелания')
      expect(wrapper.text()).toContain('Особые пожелания: тихая музыка, ароматерапия')
    })

    it('hides notes section when not present', async () => {
      wrapper = createWrapper({ bookingId: 2 })
      await wrapper.vm.$nextTick()

      expect(wrapper.text()).not.toContain('Особые пожелания')
    })
  })

  describe('Timestamps Display', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('displays creation timestamp correctly', () => {
      expect(wrapper.text()).toContain('Информация о создании')
      expect(wrapper.text()).toContain('Создано: 19 июня 2024 г. в 13:00')
    })
  })

  describe('Action Buttons', () => {
    it('shows all action buttons for confirmed booking', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const buttons = wrapper.findAll('button')
      expect(buttons).toHaveLength(3)
      
      expect(buttons[0].text()).toBe('Закрыть')
      expect(buttons[1].text()).toBe('Перенести')
      expect(buttons[2].text()).toBe('Отменить')
    })

    it('hides action buttons for cancelled booking', async () => {
      wrapper = createWrapper({ bookingId: 3 })
      await wrapper.vm.$nextTick()

      const buttons = wrapper.findAll('button')
      expect(buttons).toHaveLength(1) // Only close button
      expect(buttons[0].text()).toBe('Закрыть')
    })

    it('closes modal when close button is clicked', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const closeButton = wrapper.find('button.btn-secondary')
      await closeButton.trigger('click')

      expect(modalStore.closeModal).toHaveBeenCalledWith('booking-details')
    })

    it('opens reschedule modal when reschedule button is clicked', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const rescheduleButton = wrapper.find('button.btn-primary')
      await rescheduleButton.trigger('click')

      expect(modalStore.openModal).toHaveBeenCalledWith(
        'booking-reschedule',
        'Перенести бронирование',
        'BookingRescheduleModal',
        { bookingId: 1 }
      )
    })

    it('opens cancel modal when cancel button is clicked', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const cancelButton = wrapper.find('button.btn-danger')
      await cancelButton.trigger('click')

      expect(modalStore.openModal).toHaveBeenCalledWith(
        'booking-cancel',
        'Отменить бронирование',
        'BookingCancelModal',
        { bookingId: 1 }
      )
    })
  })

  describe('Computed Properties', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('computes status classes correctly', () => {
      expect(wrapper.vm.statusClasses).toBe('bg-green-100 text-green-800')
    })

    it('computes status text correctly', () => {
      expect(wrapper.vm.statusText).toBe('Подтверждено')
    })

    it('handles unknown status gracefully', async () => {
      const unknownStatusBooking = { ...mockBooking, status: 'unknown' }
      bookingStore.bookings = [unknownStatusBooking]
      
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      expect(wrapper.vm.statusClasses).toBe('bg-gray-100 text-gray-800')
      expect(wrapper.vm.statusText).toBe('Неизвестно')
    })
  })

  describe('Formatting Methods', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('formats currency correctly', () => {
      const formatted5000 = wrapper.vm.formatCurrency(5000)
      const formatted1500 = wrapper.vm.formatCurrency(1500)
      const formatted0 = wrapper.vm.formatCurrency(0)
      
      expect(formatted5000).toMatch(/5\s?000.*₽|5\s?000.*руб/i)
      expect(formatted1500).toMatch(/1\s?500.*₽|1\s?500.*руб/i)
      expect(formatted0).toMatch(/0.*₽|0.*руб/i)
    })

    it('formats date correctly', () => {
      expect(wrapper.vm.formatDate('2024-06-20')).toBe('20 июня 2024 г.')
      expect(wrapper.vm.formatDate('2024-12-31')).toBe('31 декабря 2024 г.')
    })

    it('formats datetime correctly', () => {
      expect(wrapper.vm.formatDateTime('2024-06-19T10:00:00Z')).toBe('19 июня 2024 г. в 13:00')
      expect(wrapper.vm.formatDateTime('2024-12-31T23:59:00Z')).toBe('1 января 2025 г. в 02:59')
    })
  })

  describe('Lifecycle and Data Loading', () => {
    it('loads booking data on mount', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      expect(wrapper.vm.booking).toEqual(mockBooking)
    })

    it('handles missing booking ID gracefully', async () => {
      wrapper = createWrapper({ bookingId: undefined })
      await wrapper.vm.$nextTick()

      expect(wrapper.vm.booking).toBeNull()
    })

    it('logs warning for non-existent booking', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      wrapper = createWrapper({ bookingId: 999 })
      await wrapper.vm.$nextTick()

      expect(consoleSpy).toHaveBeenCalledWith('Booking not found in store:', 999)
      
      consoleSpy.mockRestore()
    })
  })

  describe('Accessibility', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('has proper semantic structure', () => {
      expect(wrapper.find('h3').exists()).toBe(true)
      expect(wrapper.findAll('h4')).toHaveLength(5) // All section headers
    })

    it('has proper button types', () => {
      const buttons = wrapper.findAll('button')
      buttons.forEach((button: { attributes: (arg0: string) => any }) => {
        expect(button.attributes('type')).toBe('button')
      })
    })

    it('has proper labels for data fields', () => {
      const labels = wrapper.findAll('label')
      expect(labels.length).toBeGreaterThan(0)
      
      const labelTexts = labels.map((label: { text: () => any }) => label.text())
      expect(labelTexts).toContain('Имя гостя')
      expect(labelTexts).toContain('Номер комнаты')
      expect(labelTexts).toContain('Дата')
      expect(labelTexts).toContain('Время')
    })
  })

  describe('Edge Cases', () => {
    it('handles empty services array', async () => {
      const bookingWithoutServices = { ...mockBooking, services: [] }
      bookingStore.bookings = [bookingWithoutServices]
      
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      expect(wrapper.text()).toContain('Услуги')
      expect(wrapper.findAll('.bg-white.rounded-lg.border')).toHaveLength(0)
    })

    it('handles null booking gracefully', async () => {
      wrapper = createWrapper({ bookingId: 999 })
      
      expect(wrapper.vm.statusClasses).toBe('')
      expect(wrapper.vm.statusText).toBe('')
    })

    it('prevents modal actions when booking is null', async () => {
      wrapper = createWrapper({ bookingId: 999 })
      
      // Try to call modal methods directly
      wrapper.vm.openRescheduleModal()
      wrapper.vm.openCancelModal()

      expect(modalStore.openModal).not.toHaveBeenCalled()
    })
  })
})