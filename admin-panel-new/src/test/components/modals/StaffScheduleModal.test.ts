import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import StaffScheduleModal from '@/components/modals/StaffScheduleModal.vue'
import type { Staff, Booking } from '@/types'

// Мокаем stores
vi.mock('@/stores', () => ({
  useStaffStore: vi.fn(),
  useBookingStore: vi.fn(),
  useModalStore: vi.fn(),
  useNotificationStore: vi.fn()
}))

describe('StaffScheduleModal.vue', () => {
  let wrapper: any
  let mockStaffStore: any
  let mockBookingStore: any
  let mockModalStore: any
  let mockNotificationStore: any

  const mockStaffMember: Staff = {
    id: 1,
    name: '<PERSON>ария Иванова',
    email: '<EMAIL>',
    phone: '+7 (999) 123-45-67',
    position: 'Массажист',
    department: 'СПА',
    specializations: ['Классический массаж', 'Релакс массаж'],
    is_active: true,
    working_hours: {
      monday: { start: '09:00', end: '18:00', is_working: true },
      tuesday: { start: '09:00', end: '18:00', is_working: true },
      wednesday: { start: '09:00', end: '18:00', is_working: true },
      thursday: { start: '09:00', end: '18:00', is_working: true },
      friday: { start: '09:00', end: '18:00', is_working: true },
      saturday: { start: '10:00', end: '16:00', is_working: true },
      sunday: { start: '10:00', end: '16:00', is_working: false }
    },
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }

  const mockBookings: Booking[] = [
    {
      id: 1,
      code: 'BK001',
      confirmation_code: 'CONF001',
      services: [
        {
          id: 1,
          name: 'Классический массаж',
          price: 3000,
          duration: 60
        }
      ],
      date: '2024-01-15',
      time: '10:00',
      persons: 1,
      guest_name: 'Иван Петров',
      room_number: '101',
      phone: '+7 (999) 111-22-33',
      notes: 'Без особых пожеланий',
      total_price: 3000,
      total_duration: 60,
      status: 'confirmed',
      created_at: '2024-01-01T00:00:00Z',
      staff_id: 1,
      staff_name: 'Мария Иванова'
    },
    {
      id: 2,
      code: 'BK002',
      confirmation_code: 'CONF002',
      services: [
        {
          id: 2,
          name: 'Релакс массаж',
          price: 4000,
          duration: 90
        }
      ],
      date: '2024-01-15',
      time: '14:00',
      persons: 1,
      guest_name: 'Анна Сидорова',
      room_number: '102',
      phone: '+7 (999) 222-33-44',
      notes: '',
      total_price: 4000,
      total_duration: 90,
      status: 'pending',
      created_at: '2024-01-01T00:00:00Z',
      staff_id: 1,
      staff_name: 'Мария Иванова'
    }
  ]

  beforeEach(async () => {
    setActivePinia(createPinia())

    // Мок staff store
    mockStaffStore = {
      staff: [mockStaffMember],
      loading: false,
      error: null
    }

    // Мок booking store
    mockBookingStore = {
      bookings: mockBookings,
      loading: false,
      error: null,
      fetchBookings: vi.fn().mockResolvedValue(mockBookings)
    }

    // Мок modal store
    mockModalStore = {
      closeModal: vi.fn(),
      openStaffForm: vi.fn()
    }

    // Мок notification store
    mockNotificationStore = {
      success: vi.fn(),
      error: vi.fn()
    }

    // Настройка моков stores
    const { useStaffStore, useBookingStore, useModalStore, useNotificationStore } = await import('@/stores')
    vi.mocked(useStaffStore).mockReturnValue(mockStaffStore)
    vi.mocked(useBookingStore).mockReturnValue(mockBookingStore)
    vi.mocked(useModalStore).mockReturnValue(mockModalStore)
    vi.mocked(useNotificationStore).mockReturnValue(mockNotificationStore)

    // Монтируем компонент
    wrapper = mount(StaffScheduleModal, {
      props: {
        staffId: 1,
        date: '2024-01-15'
      },
      global: {
        stubs: {
          // Заглушки для иконок и других компонентов
        }
      }
    })
  })

  describe('Рендеринг компонента', () => {
    it('отображает информацию о сотруднике', () => {
      expect(wrapper.text()).toContain('Мария Иванова')
      expect(wrapper.text()).toContain('Массажист')
      expect(wrapper.text()).toContain('СПА')
    })

    it('отображает инициалы сотрудника', () => {
      expect(wrapper.text()).toContain('МИ')
    })

    it('отображает навигацию по датам', () => {
      expect(wrapper.text()).toContain('Предыдущий день')
      expect(wrapper.text()).toContain('Следующий день')
      expect(wrapper.find('input[type="date"]').exists()).toBe(true)
    })

    it('отображает рабочие часы', () => {
      expect(wrapper.text()).toContain('Рабочие часы')
      expect(wrapper.text()).toContain('09:00 - 18:00')
    })

    it('отображает расписание на день', () => {
      expect(wrapper.text()).toContain('Расписание на день')
    })

    it('отображает статистику', () => {
      expect(wrapper.text()).toContain('Бронирований')
      expect(wrapper.text()).toContain('Рабочих часов')
      expect(wrapper.text()).toContain('Выручка')
    })

    it('отображает кнопки действий', () => {
      expect(wrapper.text()).toContain('Закрыть')
      expect(wrapper.text()).toContain('Редактировать рабочие часы')
    })
  })

  describe('Функциональность', () => {
    it('загружает расписание при монтировании', () => {
      expect(mockBookingStore.fetchBookings).toHaveBeenCalledWith({
        start: '2024-01-15',
        end: '2024-01-15'
      })
    })

    it('переключает на предыдущий день', async () => {
      const prevButtons = wrapper.findAll('button')
      const prevButton = prevButtons.find((btn: any) => btn.text().includes('Предыдущий день'))
      await prevButton.trigger('click')
      
      // Проверяем, что дата изменилась
      expect(wrapper.vm.selectedDate).toBe('2024-01-14')
    })

    it('переключает на следующий день', async () => {
      const nextButtons = wrapper.findAll('button')
      const nextButton = nextButtons.find((btn: any) => btn.text().includes('Следующий день'))
      await nextButton.trigger('click')
      
      // Проверяем, что дата изменилась
      expect(wrapper.vm.selectedDate).toBe('2024-01-16')
    })

    it('изменяет дату через input', async () => {
      const dateInput = wrapper.find('input[type="date"]')
      await dateInput.setValue('2024-01-20')
      await dateInput.trigger('change')
      
      expect(wrapper.vm.selectedDate).toBe('2024-01-20')
      expect(mockBookingStore.fetchBookings).toHaveBeenCalledWith({
        start: '2024-01-20',
        end: '2024-01-20'
      })
    })

    it('закрывает модальное окно при нажатии кнопки закрытия', async () => {
      const buttons = wrapper.findAll('button')
      const closeButton = buttons.find((btn: { text: () => string | string[] }) => btn.text().includes('Закрыть'))
      
      expect(closeButton).toBeDefined()
      await closeButton!.trigger('click')
      
      expect(mockModalStore.closeModal).toHaveBeenCalled()
    })

    it('открывает форму редактирования рабочих часов', async () => {
      const buttons = wrapper.findAll('button')
      const editButton = buttons.find((btn: { text: () => string | string[] }) => btn.text().includes('Редактировать рабочие часы'))
      
      expect(editButton).toBeDefined()
      await editButton!.trigger('click')
      
      expect(mockModalStore.openStaffForm).toHaveBeenCalledWith(1)
    })
  })

  describe('Отображение бронирований', () => {
    it('отображает бронирования в соответствующих временных слотах', () => {
      expect(wrapper.text()).toContain('Иван Петров')
      expect(wrapper.text()).toContain('Анна Сидорова')
      expect(wrapper.text()).toContain('Классический массаж')
      expect(wrapper.text()).toContain('Релакс массаж')
    })

    it('отображает статусы бронирований', () => {
      expect(wrapper.text()).toContain('Подтверждено')
      expect(wrapper.text()).toContain('Ожидает')
    })

    it('отображает цены бронирований', () => {
      expect(wrapper.text()).toContain('3\u00A0000\u00A0₽')
      expect(wrapper.text()).toContain('4\u00A0000\u00A0₽')
    })

    it('отображает номера комнат', () => {
      expect(wrapper.text()).toContain('Номер 101')
      expect(wrapper.text()).toContain('Номер 102')
    })
  })

  describe('Вычисляемые свойства', () => {
    it('правильно вычисляет общее количество рабочих часов', () => {
      expect(wrapper.vm.totalWorkingHours).toBe(9) // 18:00 - 09:00 = 9 часов
    })

    it('правильно вычисляет общую выручку за день', () => {
      expect(wrapper.vm.todayRevenue).toBe(7000) // 3000 + 4000
    })

    it('генерирует правильные временные слоты', () => {
      const timeSlots = wrapper.vm.timeSlots
      expect(timeSlots).toContain('09:00')
      expect(timeSlots).toContain('09:30')
      expect(timeSlots).toContain('21:00')
      expect(timeSlots).not.toContain('21:30')
    })
  })

  describe('Обработка ошибок', () => {
    it('обрабатывает ошибку загрузки расписания', async () => {
      mockBookingStore.fetchBookings.mockRejectedValue(new Error('Network error'))
      
      await wrapper.vm.loadSchedule()
      
      expect(mockNotificationStore.error).toHaveBeenCalledWith(
        'Ошибка',
        'Не удалось загрузить расписание'
      )
    })

    it('отображает состояние загрузки', async () => {
      wrapper.vm.loading = true
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Загрузка расписания...')
    })
  })

  describe('Форматирование данных', () => {
    it('правильно форматирует инициалы', () => {
      expect(wrapper.vm.getInitials('Мария Иванова')).toBe('МИ')
      expect(wrapper.vm.getInitials('Анна')).toBe('А')
      expect(wrapper.vm.getInitials('')).toBe('')
    })

    it('правильно форматирует дату для отображения', () => {
      const formatted = wrapper.vm.formatDateDisplay('2024-01-15')
      expect(formatted).toContain('понедельник')
      expect(formatted).toContain('15')
      expect(formatted).toContain('января')
      expect(formatted).toContain('2024')
    })

    it('правильно форматирует валюту', () => {
      expect(wrapper.vm.formatCurrency(3000)).toBe('3\u00A0000\u00A0₽')
      expect(wrapper.vm.formatCurrency(4500)).toBe('4\u00A0500\u00A0₽')
    })

    it('правильно определяет рабочие часы для дня', () => {
      expect(wrapper.vm.getWorkingHoursForDay('2024-01-15')).toBe('09:00 - 18:00') // понедельник
      expect(wrapper.vm.getWorkingHoursForDay('2024-01-14')).toBe('Выходной день') // воскресенье
    })
  })
})