import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useBookingStore } from '@/stores/booking'
import type { Booking, FilterOptions } from '@/types'

// Mock the API client
vi.mock('@/utils/api', () => ({
  ApiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    init: vi.fn(),
    setAuthToken: vi.fn(),
    clearAuthToken: vi.fn(),
  }
}))

describe('Booking Store', () => {
  let store: ReturnType<typeof useBookingStore>
  let mockApiClient: any

  beforeEach(async () => {
    setActivePinia(createPinia())
    store = useBookingStore()

    // Get the mocked API client
    const { ApiClient } = await import('@/utils/api')
    mockApiClient = ApiClient

    // Reset all mocks
    vi.clearAllMocks()
  })

  describe('Initial State', () => {
    it('has correct initial state', () => {
      expect(store.bookings).toEqual([])
      expect(store.loading).toBe(false)
      expect(store.error).toBe(null)
      expect(store.filters).toEqual({
        category: undefined,
        date: expect.any(String), // Today's date
        status: undefined,
        staff: undefined,
        search: ''
      })
    })
  })

  describe('Computed Properties', () => {
    beforeEach(async () => {
      // Recreate store to ensure clean state
      setActivePinia(createPinia())
      store = useBookingStore()
      
      // Set up test data
      store.bookings = [
        {
          id: 1,
          code: 'BK001',
          confirmation_code: 'CONF001',
          guest_name: 'John Doe',
          date: '2024-01-15',
          time: '10:00',
          persons: 1,
          phone: '+1234567890',
          notes: '',
          total_price: 100,
          total_duration: 60,
          status: 'confirmed',
          services: [{ id: 1, name: 'Massage', service_name: 'Massage', price: 100, duration: 60, category: 'spa' }],
          room_number: '101',
          created_at: '2024-01-15T09:00:00Z'
        },
        {
          id: 2,
          code: 'BK002',
          confirmation_code: 'CONF002',
          guest_name: 'Jane Smith',
          date: '2024-01-15',
          time: '11:00',
          persons: 2,
          phone: '+1234567891',
          notes: '',
          total_price: 50,
          total_duration: 30,
          status: 'pending',
          services: [{ id: 2, name: 'Breakfast', service_name: 'Breakfast', price: 50, duration: 30, category: 'food' }],
          room_number: '102',
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          id: 3,
          code: 'BK003',
          confirmation_code: 'CONF003',
          guest_name: 'Bob Johnson',
          date: '2024-01-16',
          time: '10:00',
          persons: 1,
          phone: '+1234567892',
          notes: '',
          total_price: 100,
          total_duration: 60,
          status: 'cancelled',
          services: [{ id: 1, name: 'Massage', service_name: 'Massage', price: 100, duration: 60, category: 'spa' }],
          room_number: '103',
          created_at: '2024-01-16T09:00:00Z'
        }
      ] as Booking[]
    })

    describe('filteredBookings', () => {
      it('returns all bookings when no filters applied', () => {
        // Clear all filters including date
        store.filters.category = undefined
        store.filters.date = ''
        store.filters.status = undefined
        store.filters.staff = undefined
        store.filters.search = ''

        expect(store.filteredBookings).toHaveLength(3)
      })

      it('filters by status', () => {
        // Clear date filter first
        store.filters.date = ''
        store.filters.status = 'confirmed'

        expect(store.filteredBookings).toHaveLength(1)
        expect(store.filteredBookings[0].status).toBe('confirmed')
      })

      it('filters by date', () => {
        store.filters.date = '2024-01-15'

        expect(store.filteredBookings).toHaveLength(2)
        expect(store.filteredBookings.every(b => b.date === '2024-01-15')).toBe(true)
      })

      it('filters by category', () => {
        // Clear date filter first
        store.filters.date = ''
        store.filters.category = 'spa'

        expect(store.filteredBookings).toHaveLength(2)
        expect(store.filteredBookings.every(b => 
          b.services.some(s => s.category === 'spa')
        )).toBe(true)
      })

      it('filters by search term', () => {
      // Clear date filter first
      store.filters.date = ''
      store.filters.search = 'John Doe'
      
      expect(store.filteredBookings).toHaveLength(1)
      expect(store.filteredBookings[0].guest_name).toContain('John')
      })

      it('applies multiple filters', () => {
        store.filters.date = '2024-01-15'
        store.filters.status = 'confirmed'

        expect(store.filteredBookings).toHaveLength(1)
        expect(store.filteredBookings[0].guest_name).toBe('John Doe')
      })
    })

    describe('bookingsByDate', () => {
      it('groups bookings by date', () => {
        // Clear date filter first
        store.filters.date = ''
        const grouped = store.bookingsByDate

        expect(grouped['2024-01-15']).toHaveLength(2)
        expect(grouped['2024-01-16']).toHaveLength(1)
      })
    })

    describe('bookingsByTimeSlot', () => {
      it('groups bookings by date and time', () => {
        const grouped = store.bookingsByTimeSlot

        expect(grouped['2024-01-15-10:00']).toHaveLength(1)
        expect(grouped['2024-01-15-11:00']).toHaveLength(1)
        expect(grouped['2024-01-16-10:00']).toHaveLength(1)
      })
    })

    describe('calendarEvents', () => {
      it('converts bookings to calendar events', () => {
      const events = store.calendarEvents
      
      expect(events).toHaveLength(3)
      expect(events[0]).toMatchObject({
      id: '1',
      title: 'John Doe - Massage',
      start: '2024-01-15T10:00',
      extendedProps: {
      booking: expect.any(Object),
      category: 'spa',
      status: 'confirmed'
      }
      })
      })
    })
  })

  describe('Actions', () => {
    describe('fetchBookings', () => {
      it('fetches bookings successfully', async () => {
        // Clear date filter to avoid query parameters
        store.filters.date = ''
        
        const mockBookings = [
          {
            id: 1,
            client_name: 'Test Guest',
            booking_date: '2024-01-15',
            start_time: '10:00',
            status: 'confirmed',
            total_price: '100.00',
            service: { id: 1, name: 'Test Service', duration: 60, category_id: 1 },
            room_number: '101',
            client_phone: '1234567890',
            notes: 'Some notes',
            staff_id: 1,
            staff: { name: 'Test Staff' }
          }
        ]

        mockApiClient.get.mockResolvedValue({
          success: true,
          data: { data: mockBookings }, // Simulate paginated response
          timestamp: new Date().toISOString()
        })

        await store.fetchBookings()

        expect(mockApiClient.get).toHaveBeenCalledWith('/api/admin/bookings')
        // Проверяем, что данные были преобразованы в ожидаемый формат Booking
        expect(store.bookings).toEqual([
          {
            id: 1,
            code: '1',
            confirmation_code: '1',
            services: [
              {
                id: 1,
                name: 'Test Service',
                service_name: 'Test Service',
                price: 100,
                duration: 60,
                category: '1'
              }
            ],
            date: '2024-01-15',
            time: '10:00',
            persons: 1,
            guest_name: 'Test Guest',
            room_number: '101',
            phone: '1234567890',
            notes: 'Some notes',
            total_price: 100,
            total_duration: 60,
            status: 'confirmed',
            created_at: undefined, // created_at не мокается в тестовых данных
            staff_id: 1,
            staff_name: 'Test Staff'
          }
        ])
        expect(store.loading).toBe(false)
         expect(store.error).toBe(null)
      })

      it('handles fetch error', async () => {
        mockApiClient.get.mockRejectedValue(new Error('API Error'))

        await store.fetchBookings()

        expect(store.error).toBe('API Error')
        expect(store.loading).toBe(false)
      })

      it('sets loading state during fetch', async () => {
        mockApiClient.get.mockImplementation(() => 
          new Promise(resolve => setTimeout(() => resolve({ success: true, data: [] }), 100))
        )

        const fetchPromise = store.fetchBookings()
        expect(store.loading).toBe(true)

        await fetchPromise
        expect(store.loading).toBe(false)
      })

      it('fetches with date range parameters', async () => {
        mockApiClient.get.mockResolvedValue({ success: true, data: [] })

        await store.fetchBookings({
          start: '2024-01-01',
          end: '2024-01-31'
        })

        expect(mockApiClient.get).toHaveBeenCalledWith(
          '/api/admin/bookings?start_date=2024-01-01&end_date=2024-01-31'
        )
      })
    })

    describe('createBooking', () => {
      it('creates booking successfully', async () => {
        // Убедимся, что массив бронирований пуст перед тестом
        store.bookings = []
        
        const newBooking = {
          guest_name: 'New Guest',
          date: '2024-01-15',
          time: '10:00',
          persons: 1,
          phone: '+1234567890',
          notes: '',
          total_price: 100,
          total_duration: 60,
          services: [{ id: 1, name: 'Test Service', service_name: 'Test Service', price: 100, duration: 60, category: 'test' }],
          room_number: '101'
        }

        const createdBooking = { 
          id: 1, 
          code: 'BK001',
          confirmation_code: 'CONF001',
          status: 'pending' as const,
          created_at: '2024-01-15T10:00:00Z',
          ...newBooking 
        }

        // Настроим мок для возврата успешного ответа
        mockApiClient.post.mockResolvedValue({
          success: true,
          data: createdBooking,
          timestamp: new Date().toISOString()
        })

        const result = await store.createBooking(newBooking)

        expect(mockApiClient.post).toHaveBeenCalledWith('/api/admin/bookings', newBooking)
        expect(result).toEqual(createdBooking)
        expect(store.bookings).toHaveLength(1)
        expect(store.bookings[0]).toEqual(createdBooking)
        expect(store.error).toBe(null)
      })

      it('handles create error', async () => {
        mockApiClient.post.mockResolvedValue({
          success: false,
          error: 'Validation error',
          timestamp: new Date().toISOString()
        })

        const result = await store.createBooking({} as any)

        expect(store.error).toBe('Validation error')
        expect(result).toBe(null)
      })
    })

    describe('rescheduleBooking', () => {
      beforeEach(() => {
        store.bookings = [
          {
            id: 1,
            code: 'BK001',
            confirmation_code: 'CONF001',
            services: [{ id: 1, name: 'Test Service', category: 'spa', duration: 60, price: 100 }],
            date: '2024-01-15',
            time: '10:00',
            persons: 1,
            guest_name: 'Test Guest',
            room_number: '101',
            phone: '+1234567890',
            notes: '',
            total_price: 100,
            total_duration: 60,
            status: 'confirmed',
            created_at: '2024-01-15T10:00:00Z'
          }
        ] as Booking[]
      })

      it('reschedules booking successfully', async () => {
        mockApiClient.put.mockResolvedValue({
          success: true,
          data: {
            id: 1,
            guest_name: 'Test Guest',
            date: '2024-01-16',
            time: '11:00',
            status: 'confirmed'
          },
          timestamp: new Date().toISOString()
        })

        const result = await store.rescheduleBooking(1, '2024-01-16', '11:00')

        expect(mockApiClient.put).toHaveBeenCalledWith(
          '/api/admin/bookings/1',
          { action: 'reschedule', date: '2024-01-16', time: '11:00' }
        )
        expect(result).toBe(true)
        expect(store.bookings[0].date).toBe('2024-01-16')
        expect(store.bookings[0].time).toBe('11:00')
      })

      it('handles reschedule error', async () => {
        mockApiClient.put.mockResolvedValue({
          success: false,
          error: 'Time slot not available',
          timestamp: new Date().toISOString()
        })

        const result = await store.rescheduleBooking(1, '2024-01-16', '11:00')

        expect(result).toBe(false)
        expect(store.error).toBe('Time slot not available')
      })
    })

    describe('cancelBooking', () => {
      beforeEach(() => {
        store.bookings = [
          {
            id: 1,
            code: 'BK001',
            confirmation_code: 'CONF001',
            services: [{ id: 1, name: 'Test Service', category: 'spa', duration: 60, price: 100 }],
            date: '2024-01-15',
            time: '10:00',
            persons: 1,
            guest_name: 'Test Guest',
            room_number: '101',
            phone: '+1234567890',
            notes: '',
            total_price: 100,
            total_duration: 60,
            status: 'confirmed',
            created_at: '2024-01-15T10:00:00Z'
          }
        ] as Booking[]
      })

      it('cancels booking successfully', async () => {
        mockApiClient.put.mockResolvedValue({
          success: true,
          data: {
            id: 1,
            guest_name: 'Test Guest',
            status: 'cancelled'
          },
          timestamp: new Date().toISOString()
        })

        const result = await store.cancelBooking(1, 'Test cancellation')

        expect(mockApiClient.put).toHaveBeenCalledWith(
          '/api/admin/bookings/1',
          { action: 'cancel', reason: 'Test cancellation' }
        )
        expect(result).toBe(true)
        expect(store.bookings[0].status).toBe('cancelled')
      })

      it('handles cancel error', async () => {
        mockApiClient.put.mockRejectedValue(new Error('Network error'))

        const result = await store.cancelBooking(1, 'Test cancellation')

        expect(result).toBe(false)
        expect(store.error).toBe('Network error')
      })
    })

    describe('handleDragDrop', () => {
      it('calls rescheduleBooking with correct parameters', async () => {
        const bookingId = 1
        const newDate = '2024-01-16'
        const newTime = '11:00'
        
        // Set up a booking in the store first
        store.bookings = [
          {
            id: 1,
            code: 'BK001',
            confirmation_code: 'CONF001',
            services: [{ id: 1, name: 'Test Service', category: 'spa', duration: 60, price: 100 }],
            date: '2024-01-15',
            time: '10:00',
            persons: 1,
            guest_name: 'Test Guest',
            room_number: '101',
            phone: '+1234567890',
            notes: '',
            total_price: 100,
            total_duration: 60,
            status: 'confirmed',
            created_at: '2024-01-15T10:00:00Z'
          }
        ]
        
        // Mock the API call
        mockApiClient.put.mockResolvedValue({
          success: true,
          data: { id: 1, date: newDate, time: newTime },
          timestamp: new Date().toISOString()
        })
        
        const booking = store.bookings.find(b => b.id === bookingId)!
        const result = await store.handleDragDrop({ booking, newDate, newTime, oldDate: booking.date, oldTime: booking.time })
        
        expect(mockApiClient.put).toHaveBeenCalledWith(
          `/api/admin/bookings/${bookingId}`,
          { action: 'reschedule', date: newDate, time: newTime }
        )
        expect(result).toBe(true)
        expect(store.bookings[0].date).toBe(newDate)
        expect(store.bookings[0].time).toBe(newTime)
      })
    })

    describe('getBookingsForSlot', () => {
      beforeEach(() => {
        store.bookings = [
          {
            id: 1,
            code: 'BK001',
            confirmation_code: 'CONF001',
            services: [{ id: 1, name: 'Test Service', category: 'spa', duration: 60, price: 100 }],
            date: '2024-01-15',
            time: '10:00',
            persons: 1,
            guest_name: 'Guest 1',
            room_number: '101',
            phone: '+1234567890',
            notes: '',
            total_price: 100,
            total_duration: 60,
            status: 'confirmed',
            created_at: '2024-01-15T10:00:00Z'
          },
          {
            id: 2,
            code: 'BK002',
            confirmation_code: 'CONF002',
            services: [{ id: 1, name: 'Test Service', category: 'spa', duration: 60, price: 100 }],
            date: '2024-01-15',
            time: '10:00',
            persons: 1,
            guest_name: 'Guest 2',
            room_number: '102',
            phone: '+1234567891',
            notes: '',
            total_price: 100,
            total_duration: 60,
            status: 'confirmed',
            created_at: '2024-01-15T10:00:00Z'
          },
          {
            id: 3,
            code: 'BK003',
            confirmation_code: 'CONF003',
            services: [{ id: 1, name: 'Test Service', category: 'spa', duration: 60, price: 100 }],
            date: '2024-01-15',
            time: '11:00',
            persons: 1,
            guest_name: 'Guest 3',
            room_number: '103',
            phone: '+1234567892',
            notes: '',
            total_price: 100,
            total_duration: 60,
            status: 'confirmed',
            created_at: '2024-01-15T11:00:00Z'
          }
        ] as Booking[]
        // Clear date filter to ensure all bookings are visible
        store.filters.date = ''
      })

      it('returns bookings for specific time slot', () => {
        const bookings = store.getBookingsForSlot('2024-01-15', '10:00')

        expect(bookings).toHaveLength(2)
        expect(bookings.every(b => b.date === '2024-01-15' && b.time === '10:00')).toBe(true)
      })

      it('returns empty array for empty slot', () => {
        const bookings = store.getBookingsForSlot('2024-01-16', '10:00')

        expect(bookings).toHaveLength(0)
      })
    })

    describe('updateFilters', () => {
      it('updates filters correctly', () => {
        const newFilters: Partial<FilterOptions> = {
          status: 'confirmed',
          category: 1
        }

        store.updateFilters(newFilters)

        expect(store.filters.status).toBe('confirmed')
        expect(store.filters.category).toBe(1)
      })

      it('resets date filter when dateRange is set', () => {
        store.filters.date = '2024-01-15'

        store.updateFilters({
          dateRange: {
            start: '2024-01-01',
            end: '2024-01-31'
          }
        })

        expect(store.filters.date).toBeUndefined()
        expect(store.filters.dateRange).toEqual({
          start: '2024-01-01',
          end: '2024-01-31'
        })
      })
    })

    describe('clearFilters', () => {
      it('resets filters to default values', () => {
        store.filters = {
          category: 1,
          date: '2024-01-15',
          status: 'confirmed',
          staff: 1,
          search: 'test'
        }

        store.clearFilters()

        expect(store.filters.category).toBeUndefined()
        expect(store.filters.status).toBeUndefined()
        expect(store.filters.staff).toBeUndefined()
        expect(store.filters.search).toBe('')
        expect(store.filters.date).toBe(new Date().toISOString().split('T')[0])
      })
    })
  })

  describe('Helper Functions', () => {
    describe('calculateEndTime', () => {
      it('calculates end time correctly', () => {
        expect(store.calculateEndTime('2024-01-15', '10:00', 60)).toBe('2024-01-15T08:00:00.000Z')
      })
    })

    describe('getServiceCategoryColor', () => {
      it('returns correct colors for categories', () => {
        expect(store.getServiceCategoryColor('spa')).toBe('#0ea5e9')
        expect(store.getServiceCategoryColor('food')).toBe('#eab308')
        expect(store.getServiceCategoryColor('fitness')).toBe('#22c55e')
        expect(store.getServiceCategoryColor('unknown')).toBe('#6b7280')
      })
    })

    describe('getCategorySlug', () => {
      it('returns correct slugs for category IDs', () => {
        expect(store.getCategorySlug(1)).toBe('spa')
        expect(store.getCategorySlug(2)).toBe('food')
        expect(store.getCategorySlug(3)).toBe('fitness')
        expect(store.getCategorySlug(999)).toBe('comfort')
      })
    })
  })

  describe('Error Handling', () => {
    it('handles network errors gracefully', async () => {
      mockApiClient.get.mockRejectedValue(new Error('Network error'))

      await store.fetchBookings()

      expect(store.error).toBe('Network error')
      expect(store.loading).toBe(false)
    })

    it('handles API errors with custom messages', async () => {
      mockApiClient.post.mockResolvedValue({
        success: false,
        error: 'Custom error message',
        timestamp: new Date().toISOString()
      })

      await store.createBooking({} as any)

      expect(store.error).toBe('Custom error message')
    })

    it('clears error on successful operations', async () => {
      // Устанавливаем ошибку через метод store
      store.error = 'Previous error'

      mockApiClient.get.mockResolvedValue({
          success: true,
          data: [],
          timestamp: new Date().toISOString()
        })

      await store.fetchBookings()

      expect(store.error).toBe(null)
    })
  })
})
