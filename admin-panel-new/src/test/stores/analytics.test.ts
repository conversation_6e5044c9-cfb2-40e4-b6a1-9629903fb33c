import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'

// Мок ApiClient должен быть объявлен до импорта store
const mockApiClient = {
  get: vi.fn(),
  post: vi.fn()
}

vi.mock('@/utils/api', () => ({
  ApiClient: mockApiClient
}))

import { useAnalyticsStore } from '@/stores/analytics'

// Мок Date для консистентного тестирования
const mockDateNow = vi.fn()
vi.stubGlobal('Date', {
  ...Date,
  now: mockDateNow
})

describe('Analytics Store', () => {
  let analyticsStore: any
  let pinia: any

  const mockAnalyticsData = {
    totalBookings: 150,
    totalRevenue: 45000,
    averageBookingValue: 300,
    cancellationRate: 5.2,
    popularServices: [
      { name: 'Массаж', count: 45, revenue: 13500 },
      { name: 'СПА процедуры', count: 32, revenue: 9600 },
      { name: 'Завтрак', count: 28, revenue: 4200 }
    ],
    revenueByCategory: [
      { category: 'spa', revenue: 25000, count: 80 },
      { category: 'food', revenue: 15000, count: 50 },
      { category: 'wellness', revenue: 5000, count: 20 }
    ],
    bookingsByStatus: [
      { status: 'confirmed', count: 120, percentage: 80 },
      { status: 'pending', count: 20, percentage: 13.3 },
      { status: 'cancelled', count: 10, percentage: 6.7 }
    ],
    revenueByPeriod: [
      { period: '2024-01', revenue: 15000 },
      { period: '2024-02', revenue: 18000 },
      { period: '2024-03', revenue: 12000 }
    ],
    staffPerformance: [
      { staffId: 1, name: 'Мария Иванова', bookings: 45, revenue: 13500, rating: 4.8 },
      { staffId: 2, name: 'Анна Петрова', bookings: 35, revenue: 10500, rating: 4.6 },
      { staffId: 3, name: 'Петр Сидоров', bookings: 25, revenue: 7500, rating: 4.7 }
    ]
  }

  const mockReportData = {
    revenue: {
      total: 125000,
      byPeriod: [
        { period: '2024-01', amount: 45000 },
        { period: '2024-02', amount: 52000 },
        { period: '2024-03', amount: 28000 }
      ],
      byService: [
        { serviceName: 'Массаж', amount: 67000, count: 134 },
        { serviceName: 'СПА процедуры', amount: 45000, count: 90 }
      ]
    },
    bookings: {
      total: 311,
      byStatus: [
        { status: 'confirmed', count: 248, percentage: 79.7 },
        { status: 'pending', count: 31, percentage: 10.0 },
        { status: 'cancelled', count: 32, percentage: 10.3 }
      ]
    }
  }

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    analyticsStore = useAnalyticsStore()
    
    // Сброс всех моков
    vi.clearAllMocks()
    
    // Установка текущего времени
    mockDateNow.mockReturnValue(new Date('2024-03-15T10:00:00Z').getTime())
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Начальное состояние', () => {
    it('имеет правильное начальное состояние', () => {
      expect(analyticsStore.loading).toBe(false)
      expect(analyticsStore.error).toBe(null)
      expect(analyticsStore.analytics).toBe(null)
      expect(analyticsStore.reports).toBe(null)
      expect(analyticsStore.dateRange).toEqual({
        start: null,
        end: null
      })
    })
  })

  describe('Загрузка аналитики', () => {
    it('успешно загружает аналитические данные', async () => {
      mockApiClient.get.mockResolvedValue({ data: mockAnalyticsData })

      await analyticsStore.fetchAnalytics()

      expect(analyticsStore.loading).toBe(false)
      expect(analyticsStore.error).toBe(null)
      expect(analyticsStore.analytics).toEqual(mockAnalyticsData)
      expect(mockApiClient.get).toHaveBeenCalledWith('/analytics')
    })

    it('загружает аналитику с параметрами даты', async () => {
      mockApiClient.get.mockResolvedValue({ data: mockAnalyticsData })
      
      const dateRange = {
        start: '2024-01-01',
        end: '2024-03-31'
      }

      await analyticsStore.fetchAnalytics(dateRange)

      expect(mockApiClient.get).toHaveBeenCalledWith('/analytics', {
        params: {
          startDate: '2024-01-01',
          endDate: '2024-03-31'
        }
      })
      expect(analyticsStore.dateRange).toEqual(dateRange)
    })

    it('обрабатывает ошибку при загрузке аналитики', async () => {
      const errorMessage = 'Ошибка сервера'
      mockApiClient.get.mockRejectedValue(new Error(errorMessage))

      await analyticsStore.fetchAnalytics()

      expect(analyticsStore.loading).toBe(false)
      expect(analyticsStore.error).toBe(errorMessage)
      expect(analyticsStore.analytics).toBe(null)
    })

    it('устанавливает состояние загрузки во время запроса', async () => {
      let resolvePromise: (value: any) => void
      const promise = new Promise(resolve => {
        resolvePromise = resolve
      })
      mockApiClient.get.mockReturnValue(promise)

      const fetchPromise = analyticsStore.fetchAnalytics()
      
      expect(analyticsStore.loading).toBe(true)
      
      resolvePromise!({ data: mockAnalyticsData })
      await fetchPromise
      
      expect(analyticsStore.loading).toBe(false)
    })
  })

  describe('Генерация отчетов', () => {
    it('успешно генерирует отчет', async () => {
      mockApiClient.post.mockResolvedValue({ data: mockReportData })
      
      const reportParams = {
        type: 'revenue',
        startDate: '2024-01-01',
        endDate: '2024-03-31',
        serviceId: null,
        staffId: null
      }

      const result = await analyticsStore.generateReport(reportParams)

      expect(result).toEqual(mockReportData)
      expect(analyticsStore.reports).toEqual(mockReportData)
      expect(mockApiClient.post).toHaveBeenCalledWith('/analytics/reports', reportParams)
    })

    it('генерирует отчет с фильтрами', async () => {
      mockApiClient.post.mockResolvedValue({ data: mockReportData })
      
      const reportParams = {
        type: 'staff',
        startDate: '2024-01-01',
        endDate: '2024-03-31',
        serviceId: 1,
        staffId: 2
      }

      await analyticsStore.generateReport(reportParams)

      expect(mockApiClient.post).toHaveBeenCalledWith('/analytics/reports', reportParams)
    })

    it('обрабатывает ошибку при генерации отчета', async () => {
      const errorMessage = 'Ошибка генерации отчета'
      mockApiClient.post.mockRejectedValue(new Error(errorMessage))
      
      const reportParams = {
        type: 'revenue',
        startDate: '2024-01-01',
        endDate: '2024-03-31'
      }

      await expect(analyticsStore.generateReport(reportParams)).rejects.toThrow(errorMessage)
      expect(analyticsStore.error).toBe(errorMessage)
    })
  })

  describe('Экспорт отчетов', () => {
    it('успешно экспортирует отчет', async () => {
      const exportUrl = 'https://example.com/report.pdf'
      mockApiClient.post.mockResolvedValue({ data: { url: exportUrl } })
      
      const exportParams = {
        type: 'revenue',
        format: 'pdf',
        startDate: '2024-01-01',
        endDate: '2024-03-31'
      }

      const result = await analyticsStore.exportReport(exportParams)

      expect(result).toBe(exportUrl)
      expect(mockApiClient.post).toHaveBeenCalledWith('/analytics/export', exportParams)
    })

    it('экспортирует отчет в разных форматах', async () => {
      const exportUrl = 'https://example.com/report.xlsx'
      mockApiClient.post.mockResolvedValue({ data: { url: exportUrl } })
      
      const exportParams = {
        type: 'bookings',
        format: 'xlsx',
        startDate: '2024-01-01',
        endDate: '2024-03-31'
      }

      await analyticsStore.exportReport(exportParams)

      expect(mockApiClient.post).toHaveBeenCalledWith('/analytics/export', exportParams)
    })

    it('обрабатывает ошибку при экспорте отчета', async () => {
      const errorMessage = 'Ошибка экспорта'
      mockApiClient.post.mockRejectedValue(new Error(errorMessage))
      
      const exportParams = {
        type: 'revenue',
        format: 'pdf',
        startDate: '2024-01-01',
        endDate: '2024-03-31'
      }

      await expect(analyticsStore.exportReport(exportParams)).rejects.toThrow(errorMessage)
      expect(analyticsStore.error).toBe(errorMessage)
    })
  })

  describe('Геттеры', () => {
    beforeEach(() => {
      analyticsStore.analytics = mockAnalyticsData
    })

    it('возвращает топ услуги', () => {
      const topServices = analyticsStore.topServices
      
      expect(topServices).toHaveLength(3)
      expect(topServices[0].name).toBe('Массаж')
      expect(topServices[0].count).toBe(45)
    })

    it('возвращает статистику по категориям', () => {
      const categoryStats = analyticsStore.categoryStats
      
      expect(categoryStats).toHaveLength(3)
      expect(categoryStats[0].category).toBe('spa')
      expect(categoryStats[0].revenue).toBe(25000)
    })

    it('возвращает производительность персонала', () => {
      const staffStats = analyticsStore.staffPerformanceStats
      
      expect(staffStats).toHaveLength(3)
      expect(staffStats[0].name).toBe('Мария Иванова')
      expect(staffStats[0].bookings).toBe(45)
    })

    it('возвращает тренды выручки', () => {
      const revenueTrends = analyticsStore.revenueTrends
      
      expect(revenueTrends).toHaveLength(3)
      expect(revenueTrends[0].period).toBe('2024-01')
      expect(revenueTrends[0].revenue).toBe(15000)
    })

    it('возвращает коэффициент конверсии', () => {
      const conversionRate = analyticsStore.conversionRate
      
      expect(conversionRate).toBe(93.3) // (120 + 20) / 150 * 100
    })

    it('возвращает средний чек', () => {
      const averageCheck = analyticsStore.averageCheck
      
      expect(averageCheck).toBe(300)
    })
  })

  describe('Действия с данными', () => {
    it('очищает аналитические данные', () => {
      analyticsStore.analytics = mockAnalyticsData
      analyticsStore.reports = mockReportData
      analyticsStore.error = 'Некоторая ошибка'
      
      analyticsStore.clearAnalytics()
      
      expect(analyticsStore.analytics).toBe(null)
      expect(analyticsStore.reports).toBe(null)
      expect(analyticsStore.error).toBe(null)
    })

    it('устанавливает диапазон дат', () => {
      const dateRange = {
        start: '2024-01-01',
        end: '2024-03-31'
      }
      
      analyticsStore.setDateRange(dateRange)
      
      expect(analyticsStore.dateRange).toEqual(dateRange)
    })

    it('сбрасывает диапазон дат', () => {
      analyticsStore.dateRange = {
        start: '2024-01-01',
        end: '2024-03-31'
      }
      
      analyticsStore.resetDateRange()
      
      expect(analyticsStore.dateRange).toEqual({
        start: null,
        end: null
      })
    })
  })

  describe('Вычисляемые свойства', () => {
    beforeEach(() => {
      analyticsStore.analytics = mockAnalyticsData
    })

    it('вычисляет общую статистику', () => {
      const totalStats = analyticsStore.totalStats
      
      expect(totalStats.bookings).toBe(150)
      expect(totalStats.revenue).toBe(45000)
      expect(totalStats.averageValue).toBe(300)
      expect(totalStats.cancellationRate).toBe(5.2)
    })

    it('вычисляет процентное изменение выручки', () => {
      const revenueChange = analyticsStore.revenueChangePercentage
      
      // Изменение между февралем и мартом: (12000 - 18000) / 18000 * 100 = -33.33%
      expect(revenueChange).toBeCloseTo(-33.33, 1)
    })

    it('определяет лучшего сотрудника', () => {
      const topPerformer = analyticsStore.topPerformer
      
      expect(topPerformer.name).toBe('Мария Иванова')
      expect(topPerformer.bookings).toBe(45)
      expect(topPerformer.revenue).toBe(13500)
    })

    it('возвращает null для лучшего сотрудника если нет данных', () => {
      analyticsStore.analytics = null
      
      const topPerformer = analyticsStore.topPerformer
      
      expect(topPerformer).toBe(null)
    })
  })

  describe('Обновление данных в реальном времени', () => {
    it('обновляет аналитику при изменении диапазона дат', async () => {
      mockApiClient.get.mockResolvedValue({ data: mockAnalyticsData })
      
      const dateRange = {
        start: '2024-01-01',
        end: '2024-03-31'
      }
      
      await analyticsStore.updateAnalyticsForDateRange(dateRange)
      
      expect(analyticsStore.dateRange).toEqual(dateRange)
      expect(mockApiClient.get).toHaveBeenCalledWith('/analytics', {
        params: {
          startDate: '2024-01-01',
          endDate: '2024-03-31'
        }
      })
    })

    it('автоматически обновляет данные при изменении', async () => {
      mockApiClient.get.mockResolvedValue({ data: mockAnalyticsData })
      
      await analyticsStore.refreshAnalytics()
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/analytics')
      expect(analyticsStore.analytics).toEqual(mockAnalyticsData)
    })
  })
})