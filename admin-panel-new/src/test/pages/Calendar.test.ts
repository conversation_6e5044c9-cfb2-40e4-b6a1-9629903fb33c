import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import Calendar from '@/pages/Calendar.vue'

// Mock the stores
vi.mock('@/stores', () => ({
  useBookingStore: vi.fn(),
  useModalStore: vi.fn(),
  useNotificationStore: vi.fn(),
}))

describe('Calendar.vue', () => {
  let wrapper: any
  let mockBookingStore: any
  let mockModalStore: any
  let mockNotificationStore: any

  beforeEach(async () => {
    setActivePinia(createPinia())

    // Mock booking store
    mockBookingStore = {
      loading: false,
      error: null,
      bookings: [],
      updateFilters: vi.fn(),
      fetchBookings: vi.fn(),
      rescheduleBooking: vi.fn(),
      getBookingsForSlot: vi.fn(() => []),
    }

    // Mock modal store
    mockModalStore = {
      openBookingForm: vi.fn(),
      openBookingDetails: vi.fn(),
      openBookingReschedule: vi.fn(),
      openBookingCancel: vi.fn(),
      openReportGenerator: vi.fn(),
    }

    // Mock notification store
    mockNotificationStore = {
      success: vi.fn(),
      error: vi.fn(),
    }

    // Setup store mocks
    const { useBookingStore, useModalStore, useNotificationStore } = await import('@/stores')
    vi.mocked(useBookingStore).mockReturnValue(mockBookingStore)
    vi.mocked(useModalStore).mockReturnValue(mockModalStore)
    vi.mocked(useNotificationStore).mockReturnValue(mockNotificationStore)

    wrapper = mount(Calendar, {
      global: {
        stubs: {
          // Stub complex components that aren't the focus of this test
        }
      }
    })
  })

  describe('Component Rendering', () => {
    it('renders the calendar header correctly', () => {
      expect(wrapper.find('h1').text()).toBe('Календарь услуг')
      expect(wrapper.find('p').text()).toBe('Управление расписанием и бронированиями')
    })

    it('renders view selection buttons', () => {
      const viewButtons = wrapper.findAll('button')
      const dayButton = viewButtons.find((btn: { text: () => string }) => btn.text() === 'День')
      const weekButton = viewButtons.find((btn: { text: () => string }) => btn.text() === 'Неделя')
      const monthButton = viewButtons.find((btn: { text: () => string }) => btn.text() === 'Месяц')

      expect(dayButton).toBeTruthy()
      expect(weekButton).toBeTruthy()
      expect(monthButton).toBeTruthy()
    })

    it('renders navigation controls', () => {
      const todayButton = wrapper.findAll('button').find((btn: { text: () => string }) => btn.text() === 'Сегодня')
      expect(todayButton).toBeTruthy()
    })

    it('renders filter controls', () => {
      expect(wrapper.find('input[type="date"]').exists()).toBe(true)
      expect(wrapper.findAll('select')).toHaveLength(2) // Category and Status selects
    })

    it('renders calendar table structure', () => {
      expect(wrapper.find('table').exists()).toBe(true)
      expect(wrapper.find('thead').exists()).toBe(true)
      expect(wrapper.find('tbody').exists()).toBe(true)
    })
  })

  describe('View Selection', () => {
    it('defaults to week view', () => {
      const weekButton = wrapper.findAll('button').find((btn: { text: () => string }) => btn.text() === 'Неделя')
      expect(weekButton.classes()).toContain('bg-white')
    })

    it('switches to day view when day button is clicked', async () => {
      const dayButton = wrapper.findAll('button').find((btn: { text: () => string }) => btn.text() === 'День')
      await dayButton.trigger('click')

      expect(dayButton.classes()).toContain('bg-white')
    })

    it('switches to month view when month button is clicked', async () => {
      const monthButton = wrapper.findAll('button').find((btn: { text: () => string }) => btn.text() === 'Месяц')
      await monthButton.trigger('click')

      expect(monthButton.classes()).toContain('bg-white')
    })
  })

  describe('Date Range Calculation', () => {
    it('shows 1 day for day view', async () => {
      const dayButton = wrapper.findAll('button').find((btn: { text: () => string }) => btn.text() === 'День')
      await dayButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Check that dateRange computed property returns 1 day
      expect(wrapper.vm.dateRange).toHaveLength(1)
    })

    it('shows 7 days for week view', async () => {
      const weekButton = wrapper.findAll('button').find((btn: { text: () => string }) => btn.text() === 'Неделя')
      await weekButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(wrapper.vm.dateRange).toHaveLength(7)
    })

    it('shows full month for month view', async () => {
      const monthButton = wrapper.findAll('button').find((btn: { text: () => string }) => btn.text() === 'Месяц')
      await monthButton.trigger('click')
      await wrapper.vm.$nextTick()

      // Month view should show 28-31 days depending on the month
      expect(wrapper.vm.dateRange.length).toBeGreaterThanOrEqual(28)
      expect(wrapper.vm.dateRange.length).toBeLessThanOrEqual(31)
    })
  })

  describe('Navigation', () => {
    it('navigates to today when today button is clicked', async () => {
      const todayButton = wrapper.findAll('button').find((btn: { text: () => string }) => btn.text() === 'Сегодня')
      const today = new Date().toISOString().split('T')[0]

      await todayButton.trigger('click')

      expect(wrapper.vm.startDate).toBe(today)
    })

    it('navigates to previous period when previous button is clicked', async () => {
      const initialDate = wrapper.vm.startDate
      const prevButton = wrapper.find('[title="Предыдущий период"]')

      await prevButton.trigger('click')

      expect(wrapper.vm.startDate).not.toBe(initialDate)
    })

    it('navigates to next period when next button is clicked', async () => {
      const initialDate = wrapper.vm.startDate
      const nextButton = wrapper.find('[title="Следующий период"]')

      await nextButton.trigger('click')

      expect(wrapper.vm.startDate).not.toBe(initialDate)
    })
  })

  describe('Filters', () => {
    it('updates filters when date input changes', async () => {
      const dateInput = wrapper.find('input[type="date"]')
      await dateInput.setValue('2024-01-15')
      await dateInput.trigger('change')

      expect(mockBookingStore.updateFilters).toHaveBeenCalled()
      expect(mockBookingStore.fetchBookings).toHaveBeenCalled()
    })

    it('updates filters when category select changes', async () => {
      const categorySelect = wrapper.findAll('select')[0]
      await categorySelect.setValue('1')
      await categorySelect.trigger('change')

      expect(mockBookingStore.updateFilters).toHaveBeenCalled()
      expect(mockBookingStore.fetchBookings).toHaveBeenCalled()
    })

    it('updates filters when status select changes', async () => {
      const statusSelect = wrapper.findAll('select')[1]
      await statusSelect.setValue('confirmed')
      await statusSelect.trigger('change')

      expect(mockBookingStore.updateFilters).toHaveBeenCalled()
      expect(mockBookingStore.fetchBookings).toHaveBeenCalled()
    })
  })

  describe('Booking Interactions', () => {
    it('opens booking form when empty cell is clicked', async () => {
      const cell = wrapper.find('td[class*="cursor-pointer"]')
      await cell.trigger('click')

      expect(mockModalStore.openBookingForm).toHaveBeenCalled()
    })

    it('handles drag start for booking cards', async () => {
      // Mock a booking in the slot
      mockBookingStore.getBookingsForSlot.mockReturnValue([
        {
          id: 1,
          guest_name: 'Test Guest',
          services: [{ service_name: 'Test Service' }],
          room_number: '101',
          status: 'confirmed'
        }
      ])

      await wrapper.vm.$nextTick()

      const bookingCard = wrapper.find('.booking-card')
      if (bookingCard.exists()) {
        const dragEvent = new DragEvent('dragstart')
        Object.defineProperty(dragEvent, 'dataTransfer', {
          value: {
            effectAllowed: '',
            setData: vi.fn()
          }
        })

        await bookingCard.trigger('dragstart', dragEvent)
        expect(wrapper.vm.draggedBooking).toBeTruthy()
      }
    })
  })

  describe('Date Formatting', () => {
    it('formats date range correctly for different views', async () => {
      // Set a fixed date for testing
      await wrapper.vm.$nextTick()
      wrapper.vm.startDate = '2024-01-15'

      // Test day view formatting
      wrapper.vm.currentView = 'day'
      const dayFormat = wrapper.vm.formatDateRange()
      expect(dayFormat).toBeTruthy()

      // Test week view formatting
      wrapper.vm.currentView = 'week'
      const weekFormat = wrapper.vm.formatDateRange()
      expect(weekFormat).toContain('-') // Should contain date range separator

      // Test month view formatting
      wrapper.vm.currentView = 'month'
      const monthFormat = wrapper.vm.formatDateRange()
      expect(monthFormat).toBeTruthy()
    })

    it('formats date headers correctly', () => {
      const testDate = '2024-01-15'
      const formatted = wrapper.vm.formatDateHeader(testDate)
      expect(formatted).toMatch(/\d{2}\.\d{2}/) // Should be in DD.MM format
    })

    it('formats day of week correctly', () => {
      const testDate = '2024-01-15' // Monday
      const dayOfWeek = wrapper.vm.formatDayOfWeek(testDate)
      expect(dayOfWeek).toBeTruthy()
      expect(typeof dayOfWeek).toBe('string')
    })
  })

  describe('Loading States', () => {
    it('shows loading overlay when component loading is true', async () => {
      // Set loading through the component's reactive data
      wrapper.vm.loading = true
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.fixed.inset-0').exists()).toBe(true)
      expect(wrapper.text()).toContain('Загрузка...')
    })

    it('hides loading overlay when component loading is false', async () => {
      // Set loading to false
      wrapper.vm.loading = false
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.fixed.inset-0').exists()).toBe(false)
    })
  })

  describe('Time Slots', () => {
    it('generates correct time slots', () => {
      const timeSlots = wrapper.vm.timeSlots
      expect(timeSlots).toContain('09:00')
      expect(timeSlots).toContain('09:30')
      expect(timeSlots).toContain('21:00')
      expect(timeSlots).not.toContain('21:30') // Should not include 21:30
    })
  })
})
