import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import Dashboard from '@/pages/Dashboard.vue'

// Mock the stores
vi.mock('@/stores', () => ({
  useBookingStore: vi.fn(),
  useAnalyticsStore: vi.fn(),
  useServiceStore: vi.fn(),
  useStaffStore: vi.fn(),
  useNotificationStore: vi.fn(),
}))

describe('Dashboard.vue', () => {
  let wrapper: any
  let mockBookingStore: any
  let mockAnalyticsStore: any
  let mockServiceStore: any
  let mockStaffStore: any

  beforeEach(async () => {
    setActivePinia(createPinia())

    // Mock booking store
    mockBookingStore = {
      loading: false,
      bookings: [],
      fetchBookings: vi.fn(),
      filteredBookings: [],
      clearFilters: vi.fn(),
      updateFilters: vi.fn(),
    }

    // Mock analytics store
    mockAnalyticsStore = {
      loading: false,
      analytics: {
        totalBookings: 150,
        totalRevenue: 45000,
        averageBookingValue: 300,
        cancellationRate: 5.2,
        popularServices: [
          { name: 'Массаж', count: 45, revenue: 13500 },
          { name: 'СПА процедуры', count: 32, revenue: 9600 }
        ],
        revenueByCategory: [
          { category: 'spa', revenue: 25000, count: 80 },
          { category: 'food', revenue: 15000, count: 50 }
        ],
        bookingsByStatus: [
          { status: 'confirmed', count: 120, percentage: 80 },
          { status: 'pending', count: 20, percentage: 13.3 },
          { status: 'cancelled', count: 10, percentage: 6.7 }
        ],
        dailyStats: [
          { date: '2024-01-01', bookings: 15, revenue: 4500, cancellations: 1 },
          { date: '2024-01-02', bookings: 18, revenue: 5400, cancellations: 0 }
        ]
      },
      fetchAnalytics: vi.fn(),
    }

    // Mock service store
    mockServiceStore = {
      loading: false,
      services: [
        { id: 1, name: 'Массаж', category: 'spa', price: 300 },
        { id: 2, name: 'Завтрак', category: 'food', price: 150 }
      ],
      fetchServices: vi.fn(),
    }

    // Mock staff store
    mockStaffStore = {
      loading: false,
      staff: [
        { id: 1, name: 'Иван Иванов', position: 'Массажист', active: true },
        { id: 2, name: 'Мария Петрова', position: 'Администратор', active: true }
      ],
      activeStaff: [
        { id: 1, name: 'Иван Иванов', position: 'Массажист', active: true },
        { id: 2, name: 'Мария Петрова', position: 'Администратор', active: true }
      ],
      fetchStaff: vi.fn(),
      initializeMockStaff: vi.fn(),
    }

    // Setup store mocks
    const { useBookingStore, useAnalyticsStore, useServiceStore, useStaffStore } = await import('@/stores')
    vi.mocked(useBookingStore).mockReturnValue(mockBookingStore)
    vi.mocked(useAnalyticsStore).mockReturnValue(mockAnalyticsStore)
    vi.mocked(useServiceStore).mockReturnValue(mockServiceStore)
    vi.mocked(useStaffStore).mockReturnValue(mockStaffStore)

    wrapper = mount(Dashboard, {
      global: {
        stubs: {
          // Stub chart components that might be complex to test
          'Line': true,
          'Bar': true,
          'Doughnut': true,
          'RouterLink': true,
          'CalendarIcon': true,
          'CurrencyIcon': true,
          'UsersIcon': true,
          'ChartIcon': true,
          'ServicesIcon': true,
          'DocumentIcon': true,
        }
      }
    })
  })

  describe('Component Rendering', () => {
    it('renders the dashboard header correctly', () => {
      expect(wrapper.find('h1').text()).toBe('Добро пожаловать в админ-панель')
    })

    it('успешно рендерится', () => {
      expect(wrapper.exists()).toBe(true);
    });

    it('renders key metrics cards', () => {
      const cards = wrapper.findAll('.bg-white')
      expect(cards.length).toBeGreaterThan(0)
    })

    it('displays today bookings metric', () => {
      expect(wrapper.text()).toContain('Бронирований сегодня')
      expect(wrapper.text()).toContain('0') // Default value when no bookings
    })

    it('displays today revenue metric', () => {
      expect(wrapper.text()).toContain('Выручка сегодня')
      expect(wrapper.text()).toContain('0 ₽') // Default value when no revenue
    })

    it('displays active staff count', () => {
      expect(wrapper.text()).toContain('Активных сотрудников')
      expect(wrapper.text()).toContain('2') // From mock staff store
    })

    it('displays occupancy rate', () => {
      expect(wrapper.text()).toContain('Загрузка')
      expect(wrapper.text()).toContain('0%') // Default when no bookings
    })
  })

  describe('Quick Actions Section', () => {
    it('shows quick actions section', () => {
      expect(wrapper.text()).toContain('Быстрые действия')
    })

    it('displays calendar link', () => {
      expect(wrapper.text()).toContain('Календарь')
    })

    it('displays services link', () => {
      expect(wrapper.text()).toContain('Услуги')
    })

    it('displays staff link', () => {
      expect(wrapper.text()).toContain('Персонал')
    })

    it('displays reports link', () => {
      expect(wrapper.text()).toContain('Отчеты')
    })
  })

  describe('Recent Activity', () => {
    it('shows recent bookings section', () => {
      expect(wrapper.text()).toContain('Последние бронирования')
    })

    it('displays booking information when bookings exist', async () => {
      mockBookingStore.filteredBookings = [
        {
          id: 1,
          guest_name: 'Тест Гость',
          services: [{ service_name: 'Массаж' }],
          date: '2024-01-15',
          time: '10:00',
          status: 'confirmed'
        }
      ]

      await wrapper.vm.$nextTick()
      expect(wrapper.text()).toContain('Тест Гость')
    })

    it('shows empty state when no recent bookings', () => {
      mockBookingStore.filteredBookings = []
      expect(wrapper.text()).toContain('Нет недавних бронирований')
    })
  })

  describe('Navigation Links', () => {
    it('renders router links', () => {
      const links = wrapper.findAllComponents({ name: 'RouterLink' })
      expect(links.length).toBeGreaterThan(0)
    })

    it('has calendar link', () => {
      const calendarLink = wrapper.findAllComponents({ name: 'RouterLink' }).find((link: any) =>
        link.props('to') === '/calendar'
      )
      expect(calendarLink).toBeTruthy()
    })

    it('has services link', () => {
      const servicesLink = wrapper.findAllComponents({ name: 'RouterLink' }).find((link: any) =>
        link.props('to') === '/services'
      )
      expect(servicesLink).toBeTruthy()
    })

    it('has reports link', () => {
      const reportsLink = wrapper.findAllComponents({ name: 'RouterLink' }).find((link: any) =>
        link.props('to') === '/reports'
      )
      expect(reportsLink).toBeTruthy()
    })
  })

  describe('Loading States', () => {
    it('shows loading state for analytics', async () => {
      mockAnalyticsStore.loading = true
      await wrapper.vm.$nextTick()

      expect(wrapper.text()).toContain('Загрузка')
    })

    it('shows loading state for bookings', async () => {
      mockBookingStore.loading = true
      await wrapper.vm.$nextTick()

      expect(wrapper.text()).toContain('Загрузка')
    })

    it('hides loading when data is loaded', async () => {
      mockBookingStore.loading = false
      await wrapper.vm.$nextTick()

      // Should show actual data instead of loading
      expect(wrapper.text()).toContain('Бронирований сегодня')
    })
  })

  describe('Data Fetching', () => {
    it('fetches bookings data on mount', () => {
      expect(mockBookingStore.fetchBookings).toHaveBeenCalled()
    })

    it('initializes staff data on mount', () => {
      expect(mockStaffStore.initializeMockStaff).toHaveBeenCalled()
    })

    it('clears filters before fetching', () => {
      expect(mockBookingStore.clearFilters).toHaveBeenCalled()
    })

    it('updates filters with today date', () => {
      expect(mockBookingStore.updateFilters).toHaveBeenCalled()
    })
  })

  describe('Schedule Section', () => {
    it('renders schedule section', () => {
      expect(wrapper.text()).toContain('Расписание на сегодня')
    })

    it('shows empty schedule message', () => {
      expect(wrapper.text()).toContain('На сегодня нет запланированных услуг')
    })

    it('has link to calendar', () => {
      const calendarLink = wrapper.findAllComponents({ name: 'RouterLink' }).find((link: any) =>
        link.props('to') === '/calendar' && link.text().includes('Открыть календарь')
      )
      expect(calendarLink).toBeTruthy()
    })
  })

  describe('Responsive Design', () => {
    it('has responsive grid classes', () => {
      const gridElements = wrapper.findAll('.grid')
      expect(gridElements.length).toBeGreaterThan(0)

      // Check for responsive classes
      const hasResponsiveClasses = gridElements.some((el: { classes: () => any[] }) => 
        el.classes().some(cls => cls.includes('md:') || cls.includes('lg:'))
      )
      expect(hasResponsiveClasses).toBe(true)
    })

    it('has responsive spacing classes', () => {
      const spacingElements = wrapper.findAll('[class*="space-"]')
      expect(spacingElements.length).toBeGreaterThan(0)
    })
  })

  describe('Error Handling', () => {
    it('handles analytics fetch errors gracefully', async () => {
      mockAnalyticsStore.fetchAnalytics.mockRejectedValue(new Error('API Error'))

      // Component should still render without crashing
      expect(wrapper.exists()).toBe(true)
    })

    it('shows fallback data when analytics is null', async () => {
      mockAnalyticsStore.analytics = null
      await wrapper.vm.$nextTick()

      // Should show default values or empty state
      expect(wrapper.exists()).toBe(true)
    })
  })

  describe('Component Methods', () => {
    it('formats currency correctly', () => {
      const formatted = wrapper.vm.formatCurrency(1000)
      expect(formatted).toContain('1 000')
      expect(formatted).toContain('₽')
    })

    it('gets correct status class', () => {
      expect(wrapper.vm.getStatusClass('confirmed')).toContain('green')
      expect(wrapper.vm.getStatusClass('pending')).toContain('yellow')
      expect(wrapper.vm.getStatusClass('cancelled')).toContain('red')
    })

    it('gets correct status text', () => {
      expect(wrapper.vm.getStatusText('confirmed')).toBe('Подтверждено')
      expect(wrapper.vm.getStatusText('pending')).toBe('Ожидает')
      expect(wrapper.vm.getStatusText('cancelled')).toBe('Отменено')
    })
  })
})
