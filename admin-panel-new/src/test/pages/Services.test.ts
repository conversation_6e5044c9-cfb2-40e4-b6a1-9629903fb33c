import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import Services from '../../pages/Services.vue'

// Мокаем все внешние зависимости
vi.mock('@iconify/vue', () => ({
  Icon: {
    name: 'MockIcon',
    props: ['icon'],
    template: '<span class="mock-icon">{{ icon }}</span>'
  }
}))

vi.mock('../../components/ServiceForm.vue', () => ({
  default: {
    name: 'MockServiceForm',
    template: '<div class="mock-service-form">Service Form</div>'
  }
}))

vi.mock('../../components/CategoryForm.vue', () => ({
  default: {
    name: 'MockCategoryForm', 
    template: '<div class="mock-category-form">Category Form</div>'
  }
}))

// Мокаем роутер
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn()
  }),
  useRoute: () => ({
    params: {},
    query: {},
    path: '/services',
    name: 'services'
  })
}))

describe('Services Component - Простые тесты', () => {
  let wrapper: any

  beforeEach(() => {
    // Создаем чистую тестовую Pinia
    const pinia = createTestingPinia({
      createSpy: vi.fn,
      stubActions: false,
      initialState: {
        service: {
          services: [],
          loading: false,
          error: null
        },
        category: {
          categories: [],
          loading: false,
          error: null
        }
      }
    })

    wrapper = mount(Services, {
      global: {
        plugins: [pinia],
        stubs: {
          Icon: true,
          ServiceForm: true,
          CategoryForm: true
        }
      }
    })
  })

  it('должен успешно монтироваться', () => {
    expect(wrapper.exists()).toBe(true)
  })

  it('должен содержать основные элементы', () => {
    expect(wrapper.find('div').exists()).toBe(true)
  })

  it('должен иметь правильную структуру компонента', () => {
    expect(wrapper.vm).toBeDefined()
    expect(typeof wrapper.vm).toBe('object')
  })
})