import { useRouter } from 'vue-router'
import type { ApiResponse } from '@/types'

/**
 * Утилита для выполнения API запросов с автоматической обработкой ошибок авторизации
 */
export class ApiClient {
  private static router: any = null
  private static authToken: string | null = null

  /**
   * Инициализация роутера для перенаправлений
   */
  static init(router: any) {
    this.router = router
  }

  /**
   * Установка токена авторизации
   */
  static setAuthToken(token: string) {
    this.authToken = token
  }

  /**
   * Очистка токена авторизации
   */
  static clearAuthToken() {
    this.authToken = null
  }

  /**
   * Получение заголовков с токеном авторизации
   */
  private static getHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`
    }

    return headers
  }

  /**
   * Выполнение GET запроса
   */
  static async get<T>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      method: 'GET',
      headers: this.getHeaders()
    })
  }

  /**
   * Выполнение POST запроса
   */
  static async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      method: 'POST',
      headers: this.getHeaders(),
      body: data ? JSON.stringify(data) : undefined
    })
  }

  /**
   * Выполнение PUT запроса
   */
  static async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: data ? JSON.stringify(data) : undefined
    })
  }

  /**
   * Выполнение DELETE запроса
   */
  static async delete<T>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      method: 'DELETE',
      headers: this.getHeaders()
    })
  }

  /**
   * Базовый метод для выполнения запросов
   */
  private static async request<T>(url: string, options: RequestInit): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(url, options)
      
      // Проверка на 401 ошибку (неавторизованный доступ)
      if (response.status === 401) {
        const errorData = await response.json().catch(() => ({ 
          success: false, 
          message: 'Неавторизованный доступ' 
        }))
        
        console.warn('Получена 401 ошибка, перенаправление на страницу авторизации:', errorData)
        
        // Перенаправление на страницу авторизации
        if (this.router) {
          await this.router.push('/login')
        } else {
          // Fallback если роутер не инициализирован
          window.location.href = '/login'
        }
        
        // Возвращаем ошибку для дальнейшей обработки
        return {
          success: false,
          error: errorData.message || 'Неавторизованный доступ',
          timestamp: new Date().toISOString()
        }
      }
      
      // Проверка на другие HTTP ошибки
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ 
          success: false, 
          message: `HTTP Error: ${response.status}` 
        }))
        
        return {
          success: false,
          error: errorData.message || `HTTP Error: ${response.status}`,
          timestamp: new Date().toISOString()
        }
      }
      
      // Успешный ответ
      const data: ApiResponse<T> = await response.json()
      return data
      
    } catch (error) {
      console.error('API request failed:', error)
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Неизвестная ошибка',
        timestamp: new Date().toISOString()
      }
    }
  }
}

/**
 * Хук для использования API клиента в компонентах
 */
export function useApi() {
  const router = useRouter()
  
  // Инициализируем роутер при первом использовании
  if (!ApiClient['router']) {
    ApiClient.init(router)
  }
  
  return {
    get: <T>(url: string) => ApiClient.get<T>(url),
    post: <T>(url: string, data?: any) => ApiClient.post<T>(url, data),
    put: <T>(url: string, data?: any) => ApiClient.put<T>(url, data),
    delete: <T>(url: string) => ApiClient.delete<T>(url)
  }
}