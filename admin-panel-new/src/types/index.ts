// Base API Response
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  timestamp: string
}

// Route Meta
export interface RouteMeta {
  title?: string
  icon?: string
  requiresAuth?: boolean
}

// Service Category
export interface Category {
  id: number
  name: string
  slug: string
  description: string
  working_hours: {
    monday: { start: string; end: string }
    tuesday: { start: string; end: string }
    wednesday: { start: string; end: string }
    thursday: { start: string; end: string }
    friday: { start: string; end: string }
    saturday: { start: string; end: string }
    sunday: { start: string; end: string }
  }
  is_active: boolean
  services_count: number
  icon?: string
  image?: string
  card_width?: 'third' | 'half' | 'full'
}

// Service
export interface Service {
  id: number
  name: string
  price: number
  duration: number
  is_free: boolean
  requires_booking: boolean
  max_persons: number
  time_slot_step?: number
  image?: string
  card_width?: 'third' | 'half' | 'full'
  category?: string | Category // Может быть строкой или объектом Category
  description?: string
  is_active?: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

// Booking Service (service within a booking)
export interface BookingService {
  id: number
  name: string
  service_name?: string
  price: number
  duration: number
  category?: string
}

// Booking
export interface Booking {
  id: number
  code: string
  confirmation_code: string
  services: BookingService[]
  date: string
  time: string
  persons: number
  guest_name: string
  room_number: string
  phone: string
  notes: string
  total_price: number
  total_duration: number
  status: 'confirmed' | 'pending' | 'cancelled'
  created_at: string
  staff_id?: number
  staff_name?: string
}

// Staff Member
export interface Staff {
  id: number
  name: string
  email: string
  phone: string
  position: string
  department: string
  specializations: string[]
  is_active: boolean
  working_hours: {
    monday: { start: string; end: string; is_working: boolean }
    tuesday: { start: string; end: string; is_working: boolean }
    wednesday: { start: string; end: string; is_working: boolean }
    thursday: { start: string; end: string; is_working: boolean }
    friday: { start: string; end: string; is_working: boolean }
    saturday: { start: string; end: string; is_working: boolean }
    sunday: { start: string; end: string; is_working: boolean }
  }
  avatar?: string
  created_at: string
  updated_at: string
}

// Time Slot
export interface TimeSlot {
  time: string
  available: boolean
  price?: number
}

// Contact
export interface Contact {
  id: number
  name: string
  description: string
  phone: string
  icon: string
  is_active: boolean
  sort_order: number
}

// Feedback
export interface Feedback {
  id: number
  rating: number
  category: string
  message: string
  guest_name: string
  room_number: string
  contact: string
  is_anonymous: boolean
  created_at: string
}

// Calendar Event (for FullCalendar)
export interface CalendarEvent {
  id: string
  title: string
  start: string
  end?: string
  backgroundColor?: string
  borderColor?: string
  textColor?: string
  extendedProps?: {
    booking: Booking
    category: string
    status: string
  }
}

// Filter Options
export interface FilterOptions {
  category?: number | string
  date?: string
  dateRange?: {
    start: string
    end: string
  }
  status?: string
  staff?: number
  search?: string
}

// Analytics Data
export interface AnalyticsData {
  totalBookings: number
  totalRevenue: number
  averageBookingValue: number
  cancellationRate: number
  popularServices: Array<{
    name: string
    count: number
    revenue: number
  }>
  revenueByCategory: Array<{
    category: string
    revenue: number
    count: number
  }>
  bookingsByStatus: Array<{
    status: string
    count: number
    percentage: number
  }>
  staffPerformance: Array<{
    staff_id: number
    staff_name: string
    bookings_count: number
    revenue: number
    rating: number
  }>
  dailyStats: Array<{
    date: string
    bookings: number
    revenue: number
    cancellations: number
  }>
}

// Report Configuration
export interface ReportConfig {
  type: 'excel' | 'pdf'
  format: 'summary' | 'detailed'
  dateRange: {
    start: string
    end: string
  }
  categories?: number[]
  staff?: number[]
  includeAnalytics: boolean
  includeCancelled: boolean
}

// Export Data
export interface ExportData {
  bookings: Booking[]
  analytics: AnalyticsData
  metadata: {
    generatedAt: string
    dateRange: {
      start: string
      end: string
    }
    totalRecords: number
  }
}

// Drag and Drop Event
export interface DragDropEvent {
  booking: Booking
  newDate: string
  newTime: string
  oldDate: string
  oldTime: string
}

// Modal State
export interface ModalState {
  isOpen: boolean
  title: string
  component?: string
  props?: Record<string, any>
}

// Notification
export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
  actions?: Array<{
    label: string
    action: () => void
  }>
}

// Form Validation Error
export interface ValidationError {
  field: string
  message: string
}

// API Error
export interface ApiError {
  message: string
  code?: string
  details?: Record<string, any>
}

// Pagination
export interface Pagination {
  page: number
  limit: number
  total: number
  totalPages: number
}

// Sort Options
export interface SortOptions {
  field: string
  direction: 'asc' | 'desc'
}

// Table Column
export interface TableColumn {
  key: string
  label: string
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
  render?: (value: any, row: any) => string
}

// Chart Data
export interface ChartData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
  }>
}

// Dashboard Widget
export interface DashboardWidget {
  id: string
  title: string
  type: 'chart' | 'stat' | 'table' | 'calendar'
  size: 'small' | 'medium' | 'large'
  data: any
  refreshInterval?: number
}
