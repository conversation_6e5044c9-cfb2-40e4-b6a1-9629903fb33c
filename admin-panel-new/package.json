{"name": "hotel-admin-panel-new", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@fullcalendar/core": "^6.1.9", "@fullcalendar/daygrid": "^6.1.9", "@fullcalendar/interaction": "^6.1.9", "@fullcalendar/timegrid": "^6.1.9", "@fullcalendar/vue3": "^6.1.9", "@vueuse/core": "^10.5.0", "chart.js": "^4.4.0", "pinia": "^2.1.7", "sortablejs": "^1.15.0", "tsx": "^4.20.3", "vue": "^3.4.0", "vue-chartjs": "^5.2.0", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@pinia/testing": "^0.1.7", "@types/node": "^20.5.0", "@vitejs/plugin-vue": "^5.2.4", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.15", "happy-dom": "^18.0.1", "jsdom": "^26.1.0", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.2.4", "vue-tsc": "^2.2.10"}}