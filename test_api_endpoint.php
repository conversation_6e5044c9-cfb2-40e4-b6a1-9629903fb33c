<?php
// Test script to verify the API endpoint is working
echo "=== TESTING API ENDPOINT ===\n";

// Test the booking endpoint
$url = 'http://localhost:3002/api/admin/bookings?date=2025-06-18';
echo "Testing URL: $url\n";

// Initialize cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

// Execute the request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Status Code: $httpCode\n";

if ($error) {
    echo "cURL Error: $error\n";
} else {
    // Split headers and body
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);

    echo "Response Headers:\n";
    echo $headers . "\n";

    echo "Response Body:\n";
    echo $body . "\n";

    // Try to decode JSON
    $decoded = json_decode($body, true);
    if ($decoded !== null) {
        echo "JSON is valid\n";
        echo "Success: " . ($decoded['success'] ? 'true' : 'false') . "\n";
        if (isset($decoded['data'])) {
            echo "Data items: " . count($decoded['data']) . "\n";
        }
        if (isset($decoded['error'])) {
            echo "Error: " . $decoded['error'] . "\n";
        }
    } else {
        echo "Response is not valid JSON\n";
    }
}
?>
