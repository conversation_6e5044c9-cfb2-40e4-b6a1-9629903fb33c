<?php
// Test script to verify the POST endpoint for creating bookings

$url = 'http://localhost:8000/backend/backend.php/api/admin/bookings';

$bookingData = [
    'service_id' => 1,
    'client_name' => 'Test User',
    'client_phone' => '+1234567890',
    'client_email' => '<EMAIL>',
    'date' => '2024-06-20',
    'time' => '10:00',
    'notes' => 'Test booking creation',
    'persons_count' => 1
];

$options = [
    'http' => [
        'header' => "Content-type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($bookingData)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "Error: Could not connect to the API\n";
} else {
    echo "Response:\n";
    echo $result . "\n";
    
    $response = json_decode($result, true);
    if ($response && $response['success']) {
        echo "\n✅ POST endpoint for creating bookings is working correctly!\n";
        echo "Created booking ID: " . $response['data']['id'] . "\n";
    } else {
        echo "\n❌ POST endpoint failed\n";
        echo "Error: " . ($response['error'] ?? 'Unknown error') . "\n";
    }
}
?>